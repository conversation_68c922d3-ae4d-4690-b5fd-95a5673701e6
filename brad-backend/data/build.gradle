apply plugin: 'idea'
apply from: '../config/quality/quality.gradle'

dependencies {

    implementation project(':domain')
    testImplementation project(':network')

    // Spring context
    implementation "org.springframework:spring-context-support:${springVersion}"
    implementation group: 'org.postgresql', name: 'postgresql', version: '42.6.0'

    // hibernate
    implementation "org.springframework:spring-orm:${springVersion}"
    implementation group: 'org.hibernate', name: 'hibernate-core', version: '6.4.1.Final'
    implementation group: 'org.hibernate', name: 'hibernate-core-jakarta', version: '5.6.15.Final'
    implementation group: 'org.hibernate', name: 'hibernate-entitymanager', version: '5.6.15.Final'
    implementation group: 'org.hibernate', name: 'hibernate-envers-jakarta', version: '5.6.15.Final'
    implementation group: 'com.zaxxer', name: 'HikariCP', version: '5.0.1'

    //JPA
    implementation group: 'org.springframework.data', name: 'spring-data-jpa', version: '3.1.5'

    // Spring Batch for robust batch processing
    implementation ("org.springframework.boot:spring-boot-starter-batch:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    implementation "com.querydsl:querydsl-jpa:${queryDslVersion}:jakarta"
    annotationProcessor "com.querydsl:querydsl-apt:${queryDslVersion}:jakarta"

    // flyway db migrations
    implementation "org.flywaydb:flyway-core:9.19.1"
    implementation "org.springframework.boot:spring-boot-autoconfigure:${springBootVersion}"

    testImplementation ("org.springframework.boot:spring-boot-starter-test:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    annotationProcessor("org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}")
    implementation "javax.annotation:javax.annotation-api:1.3.2"

    // https://mvnrepository.com/artifact/org.quartz-scheduler/quartz
    implementation 'org.quartz-scheduler:quartz:2.3.2'

    implementation 'com.microsoft.sqlserver:mssql-jdbc:9.4.0.jre11'

    implementation group: 'net.sourceforge.jtds', name: 'jtds', version: '1.2'

    // Advanced jdbc driver implementation for postgresql event listening
    implementation group: 'com.impossibl.pgjdbc-ng', name: 'pgjdbc-ng', version: '0.8.9'
}

// configure the folder for the generated sources (Query DSL entities)
compileJava {
    options.compilerArgs << "-s"
    options.compilerArgs << "$buildDir/generated/sources/annotationProcessor/java/main"

    doFirst {
        // make sure that directory exists
        file(file("$buildDir/generated/sources/annotationProcessor/java/main")).mkdirs()
    }
}

dockerCompose.isRequiredBy(test)
dockerCompose {
    useComposeFiles = ['../dockers/docker-compose.yml']
    startedServices = ['brad-database', 'bale-database', 'finrec-statements-database', 'fxrates-database']
}