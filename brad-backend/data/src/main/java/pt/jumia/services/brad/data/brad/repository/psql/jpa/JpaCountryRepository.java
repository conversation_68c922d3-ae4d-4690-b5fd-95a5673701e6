package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import pt.jumia.services.brad.data.brad.entities.CountryPsql;

import java.util.Optional;

public interface JpaCountryRepository extends JpaRepository<CountryPsql, Long>, JpaSpecificationExecutor<CountryPsql> {

    Optional<CountryPsql> findByCode(String code);
}
