package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.ApiLogPsql;
import pt.jumia.services.brad.data.brad.entities.QApiLogPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaApiLogRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.ApiLog;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.apilog.ApiLogFilters;
import pt.jumia.services.brad.domain.entities.filter.apilog.ApiLogSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.ApiLogRepository;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlApiLogRepository extends BaseRepository implements ApiLogRepository {

    private final QApiLogPsql root = new QApiLogPsql("root");
    private final JpaApiLogRepository jpaApiLogRepository;
    private final EntityManager entityManager;

    @Override
    public ApiLog upsert(ApiLog apiLog) {
        return jpaApiLogRepository.save(new ApiLogPsql(apiLog)).toEntity();
    }

    @Override
    @Transactional
    public Optional<ApiLog> findById(Long id) {
        return jpaApiLogRepository.findById(id)
                .map(ApiLogPsql::toEntity);
    }

    @Override
    public List<ApiLog> findAll(ApiLogFilters apiLogFilters, ApiLogSortFilters apiLogSortFilters,
                                PageFilters pageFilters) throws EntityErrorsException {
        JPAQuery<ApiLogPsql> query = new JPAQueryFactory(entityManager)
                .select(root)
                .from(root);

        if (Objects.nonNull(apiLogFilters)) {
            applyProjectionSelect(apiLogFilters.getSelectedFields(), query, root, ApiLogPsql.class);
        }
        buildWhereClauses(apiLogFilters, query);
        applySort(apiLogSortFilters, query, root, ApiLog.SortingFields.class);
        applyPagination(pageFilters, query);

        List<ApiLogPsql> apiLogPsqlList = query.distinct().fetch();

        return apiLogPsqlList.stream()
                .map(ApiLogPsql::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteById(long id) {
        jpaApiLogRepository.deleteById(id);
    }

    @Override
    public Integer count(ApiLogFilters apiLogFilters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(apiLogFilters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    @Override
    public void deleteAll() {
        jpaApiLogRepository.deleteAll();
    }

    private void buildWhereClauses(ApiLogFilters apiLogFilters, JPAQuery<?> query) {
        if (Objects.isNull(apiLogFilters)) {
            return;
        }
        filterByPartitionKey(apiLogFilters, query);

        filterByLogType(apiLogFilters, query);
        filterByRelatedEntityId(apiLogFilters, query);
        filterByLogStatus(apiLogFilters, query);
        filterByRequest(apiLogFilters, query);
        filterByCreatedAt(apiLogFilters, query);

    }

    private void filterByPartitionKey(ApiLogFilters apiLogFilters, JPAQuery<?> query) {
        if (!Objects.isNull(apiLogFilters.getLogType())) {
            query.where(root.partitionKey.in(apiLogFilters.getLogType()));
        }
    }

    private void filterByLogType(ApiLogFilters apiLogFilters, JPAQuery<?> query) {
        if (!Objects.isNull(apiLogFilters.getLogType())) {
            query.where(root.logType.in(apiLogFilters.getLogType()));
        }
    }

    private void filterByRelatedEntityId(ApiLogFilters apiLogFilters, JPAQuery<?> query) {
        if (!Objects.isNull(apiLogFilters.getRelatedEntityId())) {
            query.where(root.relatedEntityId.likeIgnoreCase("%" + apiLogFilters.getRelatedEntityId() + "%"));
        }
    }

    private void filterByLogStatus(ApiLogFilters apiLogFilters, JPAQuery<?> query) {
        if (!Objects.isNull(apiLogFilters.getLogStatus())) {
            query.where(root.logStatus.in(apiLogFilters.getLogStatus()));
        }
    }

    private void filterByRequest(ApiLogFilters apiLogFilters, JPAQuery<?> query) {
        if (!Objects.isNull(apiLogFilters.getRequest())) {
            query.where(root.request.likeIgnoreCase("%" + apiLogFilters.getRequest() + "%"));
        }
    }

    public void filterByCreatedAt(ApiLogFilters apiLogFilters, JPAQuery<?> query) {
        if (!Objects.isNull(apiLogFilters.getCreatedAtStart())) {
            LocalDateTime startDate = apiLogFilters.getCreatedAtStart().with(LocalTime.MIN);
            LocalDateTime endDate = Objects.isNull(apiLogFilters.getCreatedAtEnd()) ? startDate.with(LocalTime.MAX) :
                    apiLogFilters.getCreatedAtEnd().with(LocalTime.MAX);
            query.where(root.createdAt.between(startDate, endDate));
        }
    }
}
