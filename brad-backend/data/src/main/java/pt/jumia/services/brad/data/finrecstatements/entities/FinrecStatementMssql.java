package pt.jumia.services.brad.data.finrecstatements.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import pt.jumia.services.brad.domain.entities.FinrecStatement;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;


@Entity
@Table(name = "`BANK_STATEMENTS`", schema = "BRAD")
@Getter
@Setter
@NoArgsConstructor
public class FinrecStatementMssql {

    @Id
    @Column(name = "`ID`")
    private String tranID;

    @Column(name = "`InitialDate`")
    private String initialDate;

    @Column(name = "`FinalDate`")
    private String finalDate;

    @Column(name = "`account_number`")
    private String accountNumber;

    @Column(name = "`openingBalance`")
    private BigDecimal openingBalance;

    @Column(name = "`currency`")
    private String currency;

    @Column(name = "`Type`")
    private String type;

    @Column(name = "`hasTransaction`")
    private String hasTransaction;

    @Column(name = "`runningBalance`")
    private BigDecimal runningBalance;

    @Column(name = "`reference`")
    private String reference;

    @Column(name = "`valueDate`")
    private String valueDate;

    @Column(name = "`Description`")
    private String description;

    @Column(name = "`tranAmount`")
    private BigDecimal tranAmount;

    @Column(name = "`Direction`")
    private String direction;

    @Column(name = "`tranDate`")
    private String tranDate;

    @Column(name = "`SK_AUD_INSERT`")
    private Integer skAudInsert;

    @Column(name = "`TIMESTAMP_RUN_AT`")
    private Timestamp timestampRunAt;

    public FinrecStatementMssql(FinrecStatement finrecStatement) {
        this.tranID = finrecStatement.getTranID();
        this.initialDate =  finrecStatement.getInitialDate().toString();
        this.finalDate = finrecStatement.getFinalDate().toString();
        this.accountNumber = finrecStatement.getAccountNumber();
        this.openingBalance = finrecStatement.getOpeningBalance();
        this.currency = finrecStatement.getCurrency();
        this.type = finrecStatement.getType();
        this.hasTransaction = finrecStatement.getHasTransaction();
        this.runningBalance = finrecStatement.getRunningBalance();
        this.reference = finrecStatement.getReference();
        this.valueDate = finrecStatement.getValueDate();
        this.description = finrecStatement.getDescription();
        this.tranAmount = finrecStatement.getTranAmount();
        this.direction = finrecStatement.getDirection();
        this.tranDate = finrecStatement.getTranDate();
        this.skAudInsert = finrecStatement.getSkAudInsert();
        this.timestampRunAt = Timestamp.valueOf(finrecStatement.getTimestampRunAt());
    }

    @SneakyThrows(ParseException.class)
    public FinrecStatement toEntity() {
        return FinrecStatement.builder()
                .tranID(tranID)
                .initialDate(DateParser.parseToLocalDateTime(initialDate))
                .finalDate(DateParser.parseToLocalDateTime(finalDate))
                .accountNumber(accountNumber)
                .openingBalance(openingBalance)
                .currency(currency)
                .type(type)
                .hasTransaction(hasTransaction)
                .runningBalance(runningBalance)
                .reference(reference)
                .valueDate(valueDate)
                .description(description)
                .tranAmount(tranAmount)
                .direction(direction)
                .tranDate(tranDate)
                .skAudInsert(skAudInsert)
                .timestampRunAt(timestampRunAt.toLocalDateTime())
                .build();
    }

}
