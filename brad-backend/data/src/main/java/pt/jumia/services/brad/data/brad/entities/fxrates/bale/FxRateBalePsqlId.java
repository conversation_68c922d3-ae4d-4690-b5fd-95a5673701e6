package pt.jumia.services.brad.data.brad.entities.fxrates.bale;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class FxRateBalePsqlId implements Serializable {

    @Serial
    private static final long serialVersionUID = 2364514516754L;

    @Column(name = "FX_RATES_ID")
    private Integer fxRateId;
    @Column(name = "BALE_ID")
    private Long baleId;
    @Column(name = "ID_COMPANY")
    private String idCompany;

}
