package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import pt.jumia.services.brad.data.brad.entities.CountryPsql;
import pt.jumia.services.brad.data.brad.entities.QCountryPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaCountryRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.filter.country.CountrySortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.CountryRepository;

import java.util.List;
import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class PsqlCountryRepository extends BaseRepository implements CountryRepository {

    private final QCountryPsql root = new QCountryPsql("root");
    private final JpaCountryRepository jpaCountryRepository;
    private final EntityManager entityManager;

    @Override
    public Country upsert(Country country) {
        return jpaCountryRepository.save(new CountryPsql(country)).toEntity();
    }

    @Override
    public Optional<Country> findById(long id) {
        return jpaCountryRepository.findById(id).map(CountryPsql::toEntity);
    }

    @Override
    public Optional<Country> findByCode(String code) {
        return jpaCountryRepository.findByCode(code).map(CountryPsql::toEntity);
    }

    @Override
    public List<Country> findAll() throws EntityErrorsException {
        JPAQuery<CountryPsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root);
        CountrySortFilters countrySortFilters = CountrySortFilters.builder()
                .field(Country.SortingFields.NAME)
                .direction(OrderDirection.ASC)
                .build();
        applySort(countrySortFilters, query, root, Country.SortingFields.class);
        return query.fetch().stream().map(CountryPsql::toEntity).toList();

    }

    @Override
    public void deleteById(long id) {
        jpaCountryRepository.deleteById(id);
    }
}
