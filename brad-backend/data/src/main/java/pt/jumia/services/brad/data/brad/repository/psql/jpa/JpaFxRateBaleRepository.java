package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import pt.jumia.services.brad.data.brad.entities.BalePsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.bale.FxRateBalePsql;

import java.util.Set;

public interface JpaFxRateBaleRepository extends JpaRepository<FxRateBalePsql, Integer>,
        JpaSpecificationExecutor<FxRateBalePsql> {

    Set<FxRateBalePsql> findByBale(BalePsql balePsql);

    Set<FxRateBalePsql> findByBaleId(Long baleId);
}
