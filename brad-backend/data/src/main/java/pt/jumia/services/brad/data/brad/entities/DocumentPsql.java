package pt.jumia.services.brad.data.brad.entities;


import jakarta.persistence.*;
import lombok.Setter;
import org.hibernate.envers.Audited;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.enumerations.DocumentType;

import java.time.LocalDateTime;
import java.util.Map;

@Setter
@Entity
@Audited
@Table(name = "document")
public class DocumentPsql extends BaseEntityFieldMap<Document.SortingFields> {

    @Id
    @SequenceGenerator(name = "document_id_seq", sequenceName = "document_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "document_sequence_id_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "type", nullable = false)
    private String documentType;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "description", nullable = false)
    private String description;

    @Column(name = "file_key", nullable = false)
    private String fileKey;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "bank_account_id", nullable = false)
    private AccountPsql account;

    @Column(name = "file", nullable = false)
    private String file;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "created_by", updatable = false)
    private String createdBy;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    public DocumentPsql() {
    }

    public DocumentPsql(Document document) {
        this.id = document.getId();
        this.documentType = document.getDocumentType().toString();
        this.name = document.getName();
        this.description = document.getDescription();
        this.fileKey = document.getFileKey();
        this.file = document.getFile();
        this.account = new AccountPsql(document.getAccount());
        this.createdBy = document.getCreatedBy() == null
                ? RequestContext.getUsername()
                : document.getCreatedBy();
        this.createdAt = document.getCreatedAt() == null
                ? LocalDateTime.now()
                : document.getCreatedAt();
        this.updatedBy = document.getUpdatedBy() == null
                ? RequestContext.getUsername()
                : document.getUpdatedBy();
        this.updatedAt = document.getUpdatedAt() == null
                ? LocalDateTime.now()
                : document.getUpdatedAt();
    }

    public Document toEntity() {
        return Document
                .builder()
                .id(id)
                .documentType(DocumentType.fromString(documentType))
                .name(name)
                .description(description)
                .fileKey(fileKey)
                .account(account.toEntity())
                .file(file)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }


    static {
        Map<Document.SortingFields, String> entityFields = Map.of(
                Document.SortingFields.ID, "id",
                Document.SortingFields.DOCUMENT_TYPE, "documentType",
                Document.SortingFields.NAME, "name",
                Document.SortingFields.DESCRIPTION, "description",
                Document.SortingFields.CREATED_AT, "createdAt",
                Document.SortingFields.CREATED_BY, "createdBy",
                Document.SortingFields.UPDATED_AT, "updatedAt",
                Document.SortingFields.UPDATED_BY, "updatedBy"
        );
        addEntityFieldMap(Document.SortingFields.class, entityFields);
    }

}
