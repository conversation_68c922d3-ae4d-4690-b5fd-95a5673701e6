package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import pt.jumia.services.brad.data.brad.entities.TransactionPsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.transaction.FxRateTransactionPsql;

public interface JpaFxRateTransactionRepository extends JpaRepository<FxRateTransactionPsql, Integer>,
    JpaSpecificationExecutor<FxRateTransactionPsql> {

    @Modifying
    @Query("DELETE FROM FxRateTransactionPsql fxt where fxt in (SELECT f FROM FxRateTransactionPsql f WHERE f.transaction"
        + ".accountStatement.id = :statementId)")
    void deleteAllByTransactionAccountStatementId(@Param("statementId") Long statementId);

    Set<FxRateTransactionPsql> findByTransaction(TransactionPsql transactionPsql);

    Set<FxRateTransactionPsql> findByTransactionId(Long transactionId);

}
