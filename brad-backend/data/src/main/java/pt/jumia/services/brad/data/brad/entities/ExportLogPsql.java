package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.ExportLog;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "export_log")
public class ExportLogPsql extends BaseEntityFieldMap<ExportLog.SortingFields> {

    @Id
    @SequenceGenerator(name = "export_log_seq", sequenceName = "export_log_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "export_log_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "type", nullable = false)
    private String type;

    @Column(name = "file_url")
    private String fileUrl;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "row_count")
    private Integer rowCount;

    @Column(name = "status")
    private String status;

    @Column(name = "execution_time")
    private Long executionTime;

    @Column(name = "filters")
    private String filters;

    @Column(name = "countries_list")
    private String countries;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    public ExportLogPsql(ExportLog exportLog) {
        id = exportLog.getId();
        type = exportLog.getType().name();
        fileUrl = exportLog.getFileUrl();
        fileName = exportLog.getFileName();
        rowCount = exportLog.getRowCount();
        status = exportLog.getStatus() == null ? null : exportLog.getStatus().name();
        executionTime = exportLog.getExecutionTime();
        filters = exportLog.getFilters();
        countries = exportLog.getCountries() != null ?
                exportLog.getCountries().stream().map(Object::toString).collect(Collectors.joining(", ")) :
                null;
        this.createdBy = exportLog.getCreatedBy() == null
                ? RequestContext.getUsername()
                : exportLog.getCreatedBy();
        this.createdAt = exportLog.getCreatedAt() == null
                ? LocalDateTime.now(ZoneOffset.UTC)
                : exportLog.getCreatedAt();
        this.updatedAt = exportLog.getUpdatedAt() == null
                ? LocalDateTime.now(ZoneOffset.UTC)
                : exportLog.getUpdatedAt();
    }


    public ExportLog toEntity() {
        return ExportLog.builder()
                .id(id)
                .type(ExportLog.Type.valueOf(type))
                .fileUrl(fileUrl)
                .fileName(fileName)
                .rowCount(rowCount)
                .status(Objects.isNull(status) ? ExportLog.Status.PENDING : ExportLog.Status.valueOf(status))
                .executionTime(executionTime)
                .filters(filters)
                .countries(this.countries != null ?
                        Stream.of(this.countries.split(", ", -1)).collect(Collectors.toList()) : null)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .build();
    }

    static {
        Map<ExportLog.SortingFields, String> entityFields = Map.of(
                ExportLog.SortingFields.ID, "id",
                ExportLog.SortingFields.TYPE, "type",
                ExportLog.SortingFields.FILE_URL, "fileUrl",
                ExportLog.SortingFields.FILE_NAME, "fileName",
                ExportLog.SortingFields.STATUS, "status",
                ExportLog.SortingFields.ROW_COUNT, "rowCount",
                ExportLog.SortingFields.EXECUTION_TIME, "executionTime",
                ExportLog.SortingFields.CREATED_AT, "createdAt",
                ExportLog.SortingFields.UPDATED_AT, "updatedAt"
        );
        addEntityFieldMap(ExportLog.SortingFields.class, entityFields);
    }
}
