package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.*;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.reconciliation.Threshold;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

@Setter
@Entity
@Audited
@Table(name = "threshold")
@NoArgsConstructor
public class ThresholdPsql extends BaseEntityFieldMap<Threshold.SortingFields> {


    @Id
    @SequenceGenerator(name = "threshold_sequence_id_seq", sequenceName = "threshold_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "threshold_sequence_id_seq")
    @Column(name = "ID")
    private Long id;

    @ManyToOne
    @JoinColumn(name = "FK_CURRENCY", nullable = false)
    private CurrencyPsql currency;

    @ManyToOne
    @JoinColumn(name = "FK_COUNTRY", nullable = false)
    private CountryPsql country;

    @Column(name = "AMOUNT", nullable = false)
    private BigDecimal amount;

    @Column(name = "CREATED_BY", nullable = false, updatable = false)
    private String createdBy;

    @Column(name = "CREATED_AT", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "UPDATED_BY", nullable = false)
    private String updatedBy;

    @Column(name = "UPDATED_AT", nullable = false)
    private LocalDateTime updatedAt;

    public ThresholdPsql(Threshold threshold) {

        this.id = threshold.getId();
        this.currency = new CurrencyPsql(threshold.getCurrency());
        this.country = new CountryPsql(threshold.getCountry());
        this.amount = threshold.getAmount();
        this.createdBy = threshold.getCreatedBy() == null
                ? RequestContext.getUsername()
                : threshold.getCreatedBy();
        this.createdAt = threshold.getCreatedAt() == null
                ? LocalDateTime.now()
                : threshold.getCreatedAt();
        this.updatedBy = threshold.getUpdatedBy() == null
                ? RequestContext.getUsername()
                : threshold.getUpdatedBy();
        this.updatedAt = threshold.getUpdatedAt() == null
                ? LocalDateTime.now()
                : threshold.getUpdatedAt();
    }

    public Threshold toEntity() {

        return Threshold.builder()
                .id(this.id)
                .currency(this.currency.toEntity())
                .country(this.country.toEntity())
                .amount(this.amount)
                .createdBy(this.createdBy)
                .createdAt(this.createdAt)
                .updatedBy(this.updatedBy)
                .updatedAt(this.updatedAt)
                .build();
    }

    static {
        Map<Threshold.SortingFields, String> entityFields = Map.ofEntries(
                Map.entry(Threshold.SortingFields.ID, "id"),
                Map.entry(Threshold.SortingFields.CURRENCY, "currency"),
                Map.entry(Threshold.SortingFields.COUNTRY, "country"),
                Map.entry(Threshold.SortingFields.AMOUNT, "amount")
        );
        addEntityFieldMap(Threshold.SortingFields.class, entityFields);
    }

}
