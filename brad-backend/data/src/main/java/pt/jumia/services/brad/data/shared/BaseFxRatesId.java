package pt.jumia.services.brad.data.shared;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;


@NoArgsConstructor
@AllArgsConstructor
public abstract class BaseFxRatesId implements Serializable {

    @Serial
    private static final long serialVersionUID = 811454236L;


    protected String baseCurrency;
    protected String quoteCurrency;
    protected LocalDate rateDate;

}
