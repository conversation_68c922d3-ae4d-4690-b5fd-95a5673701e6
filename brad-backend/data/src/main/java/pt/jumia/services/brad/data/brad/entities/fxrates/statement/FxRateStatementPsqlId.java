package pt.jumia.services.brad.data.brad.entities.fxrates.statement;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class FxRateStatementPsqlId implements Serializable {

    @Serial
    private static final long serialVersionUID = 13465563723487651L;

    @Column(name = "FX_RATES_ID")
    private Integer fxRatesId;
    @Column(name = "BANK_STATEMENT_ID")
    private Long accountStatementId;
    @Column(name = "PARTITION_KEY")
    private String partitionKey;

}
