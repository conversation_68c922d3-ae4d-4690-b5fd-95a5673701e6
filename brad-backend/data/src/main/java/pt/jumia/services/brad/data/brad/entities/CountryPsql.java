package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.*;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.account.Country;

import java.time.LocalDateTime;
import java.util.Map;

@Setter
@Entity
@Audited
@NoArgsConstructor
@Table(name = "country")
public class CountryPsql extends BaseEntityFieldMap<Country.SortingFields> {

    @Id
    @SequenceGenerator(name = "country_id_seq", sequenceName = "country_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "country_sequence_id_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "code", nullable = false, unique = true)
    private String code;

    @ManyToOne
    private CurrencyPsql currency;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "created_by", updatable = false)
    private String createdBy;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    public CountryPsql(Country country) {
        this.id = country.getId();
        this.name = country.getName();
        this.code = country.getCode();
        this.currency = new CurrencyPsql(country.getCurrency());
        this.createdBy = country.getCreatedBy() == null
                ? RequestContext.getUsername()
                : country.getCreatedBy();
        this.createdAt = country.getCreatedAt() == null
                ? LocalDateTime.now()
                : country.getCreatedAt();
        this.updatedBy = country.getUpdatedBy() == null
                ? RequestContext.getUsername()
                : country.getUpdatedBy();
        this.updatedAt = country.getUpdatedAt() == null
                ? LocalDateTime.now()
                : country.getUpdatedAt();
    }

    public Country toEntity() {
        return Country.builder()
                .id(this.id)
                .name(this.name)
                .code(this.code)
                .currency(this.currency.toEntity())
                .createdAt(this.createdAt)
                .updatedAt(this.updatedAt)
                .createdBy(this.createdBy)
                .updatedBy(this.updatedBy)
                .build();
    }

    static {
        Map<Country.SortingFields, String> entityFields = Map.of(
                Country.SortingFields.ID, "id",
                Country.SortingFields.NAME, "name",
                Country.SortingFields.CODE, "code",
                Country.SortingFields.CREATED_AT, "createdAt",
                Country.SortingFields.CREATED_BY, "createdBy",
                Country.SortingFields.UPDATED_AT, "updatedAt",
                Country.SortingFields.UPDATED_BY, "updatedBy"
        );
        addEntityFieldMap(Country.SortingFields.class, entityFields);
    }

}
