package pt.jumia.services.brad.data.brad.utils;

import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;

import java.sql.Date;
import java.util.Locale;

@Service
@RequiredArgsConstructor
public class PartitionCreator {

    private final EntityManager entityManager;

    @Transactional
    public void createListPartition(String partitionName, TableNames table) throws DatabaseErrorsException {
        String tableName = table.name().toLowerCase(Locale.ENGLISH);
        try {
            String partitionFullName = String.format(tableName + "_" + partitionName).toLowerCase(Locale.ENGLISH);

            final String query = "CREATE TABLE IF NOT EXISTS " + partitionFullName +
                    " PARTITION OF " + tableName +
                    " FOR VALUES IN ('" + partitionName + "')";

            entityManager.createNativeQuery(query).executeUpdate();
        } catch (Exception e) {
            throw DatabaseErrorsException.createPartitionError(partitionName, tableName);
        }

    }
    @Transactional
    public void createRangePartition(String partitionName, TableNames table, Date fromDate, Date toDate) throws DatabaseErrorsException {
        String tableName = table.name().toLowerCase(Locale.ENGLISH);
        try {
            String partitionFullName = String.format(tableName + "_" + partitionName).toLowerCase(Locale.ENGLISH);

            final String query = "CREATE TABLE IF NOT EXISTS " + partitionFullName +
                    " PARTITION OF " + tableName +
                    " FOR VALUES FROM ('" + fromDate + "') TO ('" + toDate + "')";

            entityManager.createNativeQuery(query).executeUpdate();
        } catch (Exception e) {
            throw DatabaseErrorsException.createPartitionError(partitionName, tableName);
        }
    }

    @Transactional
    public void checkPartitionExists(String partitionName, TableNames table, Date fromDate, Date toDate) throws DatabaseErrorsException {
        String tableName = table.name().toLowerCase(Locale.ENGLISH);
        try {
            String partitionFullName = String.format(tableName + "_" + partitionName).toLowerCase(Locale.ENGLISH);

            final String query = "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = '" + partitionFullName + "')";

            if (!Boolean.parseBoolean(entityManager.createNativeQuery(query).getSingleResult().toString())) {
                createRangePartition(partitionName, table, fromDate, toDate);
            }
        } catch (Exception e) {
           throw DatabaseErrorsException.createPartitionError(partitionName, tableName);
        }
    }


    public enum TableNames {
        BANK_STATEMENT, TRANSACTIONS, BALE, RECONCILIATIONS
    }

}
