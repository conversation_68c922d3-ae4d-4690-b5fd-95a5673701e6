package pt.jumia.services.brad.data.brad.job;

import org.jetbrains.annotations.NotNull;
import org.quartz.JobExecutionContext;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.FinrecStatement;
import pt.jumia.services.brad.domain.usecases.accountstatement.SyncBradFinrecStatementsUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.finrecstatement.ReadFinrecStatementUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.CreateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.ReadExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.UpdateExecutionLogsUseCase;

import java.util.List;

public class FinrecStatementSyncJob extends QuartzJobBean {

    @Override
    protected void executeInternal(@NotNull JobExecutionContext jobExecutionContext){
        try {
            ApplicationContext applicationContext = (ApplicationContext)
                    jobExecutionContext.getScheduler().getContext().get("applicationContext");

            ReadFinrecStatementUseCase readFinrecStatementUseCase = applicationContext.getBean(ReadFinrecStatementUseCase.class);
            SyncBradFinrecStatementsUseCase syncBradFinrecStatementsUseCase = applicationContext.getBean(SyncBradFinrecStatementsUseCase.class);
            CreateExecutionLogsUseCase createExecutionLogsUseCase = applicationContext.getBean(CreateExecutionLogsUseCase.class);
            ReadExecutionLogsUseCase readExecutionLogsUseCase = applicationContext.getBean(ReadExecutionLogsUseCase.class);
            UpdateExecutionLogsUseCase updateExecutionLogsUseCase = applicationContext.getBean(UpdateExecutionLogsUseCase.class);
            ExecutionLog newExecutionLog = ExecutionLog.builder()
                    .logType(ExecutionLog.ExecutionLogType.FINREC_STATEMENTS)
                    .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
                    .build();
            ExecutionLog createdExecutionLog = createExecutionLogsUseCase.execute(newExecutionLog);
            List<FinrecStatement> finrecStatementList = readFinrecStatementUseCase.execute(createdExecutionLog);
            ExecutionLog updatedExecutionLog = readExecutionLogsUseCase.execute(createdExecutionLog.getId());
            if (finrecStatementList.isEmpty()) {
                updateExecutionLogsUseCase.execute(updatedExecutionLog.toBuilder()
                        .logStatus(ExecutionLog.ExecutionLogStatus.EMPTY)
                        .build());
                return;
            }
            syncBradFinrecStatementsUseCase.execute(finrecStatementList, updatedExecutionLog);



        } catch (Exception e){
            e.printStackTrace();
        }
    }
}
