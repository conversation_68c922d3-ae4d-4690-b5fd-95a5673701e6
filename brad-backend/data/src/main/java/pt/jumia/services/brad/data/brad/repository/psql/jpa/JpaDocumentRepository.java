package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import pt.jumia.services.brad.data.brad.entities.AccountPsql;
import pt.jumia.services.brad.data.brad.entities.DocumentPsql;

public interface JpaDocumentRepository extends JpaRepository<DocumentPsql, Long>, JpaSpecificationExecutor<AccountPsql> {

}
