package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.*;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.PartitionKey;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.ApiLog;

import java.time.LocalDateTime;
import java.util.Map;

@Setter
@Entity
@NoArgsConstructor
@Table(name = "api_log")
public class ApiLogPsql extends BaseEntityFieldMap<ApiLog.SortingFields> {

    @Id
    @SequenceGenerator(name = "api_log_sequence_id_seq", sequenceName = "api_log_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "api_log_sequence_id_seq")
    @Column(name = "id")
    Long id;


    @Column(name = "log_type", nullable = false)
    private String logType;

    @Column(name = "request", nullable = false, columnDefinition = "text")
    private String request;

    @Column(name = "response", nullable = false, columnDefinition = "text")
    private String response;

    @Column(name = "related_entity_id")
    private String relatedEntityId;

    @Column(name = "status", nullable = false)
    private String logStatus;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "created_by", nullable = false, updatable = false)
    private String createdBy;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    @PartitionKey
    @Column(name = "PARTITION_KEY", nullable = false)
    private String partitionKey;


    public ApiLogPsql(ApiLog apiLog) {

        this.id = apiLog.getId();
        this.logType = apiLog.getLogType().name();
        this.request = apiLog.getRequest();
        this.response = apiLog.getResponse();
        this.relatedEntityId = apiLog.getRelatedEntityId();
        this.logStatus = apiLog.getLogStatus().name();
        this.createdBy = apiLog.getCreatedBy() == null
                ? RequestContext.getUsername()
                : apiLog.getCreatedBy();
        this.createdAt = apiLog.getCreatedAt() == null
                ? LocalDateTime.now()
                : apiLog.getCreatedAt();
        this.updatedBy = apiLog.getUpdatedBy() == null
                ? RequestContext.getUsername()
                : apiLog.getUpdatedBy();
        this.updatedAt = apiLog.getUpdatedAt() == null
                ? LocalDateTime.now()
                : apiLog.getUpdatedAt();
        this.partitionKey = apiLog.getLogType().name();
    }

    public ApiLog toEntity() {
        return ApiLog.builder()
                .id(this.id)
                .logType(ApiLog.ApiLogType.valueOf(this.logType))
                .request(this.request)
                .response(this.response)
                .relatedEntityId(this.relatedEntityId)
                .logStatus(ApiLog.ApiLogStatus.valueOf(this.logStatus))
                .createdAt(this.createdAt)
                .createdBy(this.createdBy)
                .updatedAt(this.updatedAt)
                .updatedBy(this.updatedBy)
                .partitionKey(this.partitionKey)
                .build();
    }
    static {
        Map<ApiLog.SortingFields, String> entityFields = Map.ofEntries(
                Map.entry(ApiLog.SortingFields.ID, "id"),
                Map.entry(ApiLog.SortingFields.LOG_TYPE, "logType"),
                Map.entry(ApiLog.SortingFields.RELATED_ENTITY_ID, "relatedEntityId"),
                Map.entry(ApiLog.SortingFields.LOG_STATUS, "logStatus"),
                Map.entry(ApiLog.SortingFields.CREATED_AT, "createdAt"),
                Map.entry(ApiLog.SortingFields.CREATED_BY, "createdBy"),
                Map.entry(ApiLog.SortingFields.UPDATED_AT, "updatedAt"),
                Map.entry(ApiLog.SortingFields.UPDATED_BY, "updatedBy")
        );

        addEntityFieldMap(ApiLog.SortingFields.class, entityFields);
    }
}
