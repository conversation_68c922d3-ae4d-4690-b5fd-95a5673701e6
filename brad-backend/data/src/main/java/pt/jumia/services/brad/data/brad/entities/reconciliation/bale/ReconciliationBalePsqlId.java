package pt.jumia.services.brad.data.brad.entities.reconciliation.bale;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class ReconciliationBalePsqlId implements Serializable {

    @Serial
    private static final long serialVersionUID = 86785385234L;


    @Column(name = "RECONCILIATION_ID")
    private Integer id;
    @Column(name = "BALE_ID")
    private Long baleId;
    @Column(name = "ID_COMPANY")
    private String partitionKey;

}
