package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import pt.jumia.services.brad.data.brad.entities.reconciliation.transaction.ReconciliationTransactionPsql;

public interface JpaReconciliationTransactionRepository extends JpaRepository<ReconciliationTransactionPsql, Integer>,
        JpaSpecificationExecutor<ReconciliationTransactionPsql> {
}
