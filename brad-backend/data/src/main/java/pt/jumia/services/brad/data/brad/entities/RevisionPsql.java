package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionEntity;
import pt.jumia.services.brad.data.brad.audit.listener.UserRevisionListener;
import pt.jumia.services.brad.domain.entities.AuditedEntity;

import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "revision_info", schema = "\"audit\"")
@RevisionEntity(UserRevisionListener.class)
@EqualsAndHashCode(callSuper = true)
public class RevisionPsql extends DefaultRevisionEntity {

    private static final long serialVersionUID = -2621042342875858176L;

    private String email;

    private LocalDateTime time;

    public AuditedEntity.RevisionInfo toEntity() {
        return AuditedEntity.RevisionInfo.builder()
                .datetime(getTime())
                .email(getEmail())
            .build();
    }
}
