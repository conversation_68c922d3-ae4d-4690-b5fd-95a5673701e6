package pt.jumia.services.brad.data.brad.entities.dto;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.data.brad.entities.TransactionPsql;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.enumerations.ReconciliationStatus;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@NoArgsConstructor
@AllArgsConstructor
public class TransactionWithReconciliationDTO extends TransactionPsql implements Serializable {

    @Serial
    private static final long serialVersionUID = 5897862743L;

    private Integer reconciliationId;
    private String reconciliationCreator;
    private LocalDateTime reconciliationCreationDate;
    private String reconciliationReviewer;
    private LocalDateTime reconciliationReviewDate;
    private String reconciliationStatus;


    @Override
    public Transaction toEntity() {
        return super.toEntity()
                .toBuilder()
                .reconciliation(Objects.nonNull(reconciliationId) ? Reconciliation.builder()
                        .id(reconciliationId)
                        .creator(reconciliationCreator)
                        .creationDate(reconciliationCreationDate)
                        .reviewer(reconciliationReviewer)
                        .reviewDate(reconciliationReviewDate)
                        .status(ReconciliationStatus.fromString(reconciliationStatus))
                        .build() : null)
                .build();
    }
}
