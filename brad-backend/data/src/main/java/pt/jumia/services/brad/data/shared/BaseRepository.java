package pt.jumia.services.brad.data.shared;

import com.querydsl.core.types.*;
import com.querydsl.core.types.OrderSpecifier.NullHandling;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.core.types.dsl.SimpleExpression;
import com.querydsl.jpa.impl.JPAQuery;
import jakarta.persistence.MappedSuperclass;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@MappedSuperclass
@NoArgsConstructor
public abstract class BaseRepository {

    @SneakyThrows
    protected <T> void applyProjectionSelect(List<String> projectionFields, JPAQuery<T> query, EntityPathBase<T> entityPathBase, Class clazz) {
        if (!Objects.isNull(projectionFields) && !projectionFields.isEmpty()) {
            Expression<?>[] expressions = new Expression<?>[projectionFields.size()];
            for (int i = 0; i < projectionFields.size(); i++) {
                String fieldName = projectionFields.get(i);
                PathBuilder<T> pathBuilder = new PathBuilder<>(clazz, entityPathBase.getMetadata());
                Field field = clazz.getDeclaredField(fieldName);
                SimpleExpression<?> expression = pathBuilder.get(field.getName(), field.getType());
                expressions[i] = expression;
            }

            query.select(Projections.bean(clazz, expressions));
        }
    }

    protected void applyPagination(PageFilters pageFilters, JPAQuery<?> query) {
        if (!Objects.isNull(pageFilters)) {
            query.offset((long) (pageFilters.getPage() - 1) * pageFilters.getSize())
                    .limit(pageFilters.getSize() + 1L); // work around to see if there is a next page
        }
    }

    protected <E extends Enum<E>, T extends BaseEntityFieldMap<E>> void applyGroup(List<String> groupFilters,
                                                                                   JPAQuery<?> query,
                                                                                   EntityPathBase<T> entityPathBase) {

        if (!Objects.isNull(groupFilters) && !groupFilters.isEmpty()) {
            Map<String, Path<?>> groupByFields = new LinkedHashMap<>();
            groupFilters.forEach(groupFilter -> {
                Path<T> path = getPath(entityPathBase, groupFilter);
                groupByFields.put(groupFilter, path);
            });
            query.select(groupByFields.values().toArray(new Path[0]));
            query.groupBy(groupByFields.values().toArray(new Path[0]));
        }
    }


    protected <E extends Enum<E>, T extends BaseEntityFieldMap<E>> void applySort(SortFilters<E> sortFilters,
                                                                                  JPAQuery<?> query,
                                                                                  EntityPathBase<T> entityPathBase,
                                                                                  Class clazz) throws EntityErrorsException {

        if (Objects.isNull(sortFilters) || (Objects.isNull(sortFilters.getField()))) {
            return;
        }

        if (!Objects.isNull(sortFilters.getDirection())) {
            sortIfFieldExists(sortFilters, query, entityPathBase, clazz);
        } else {
            sortIfFieldExistsButNotDirection(sortFilters, query, entityPathBase, clazz);
        }

    }

    private <E extends Enum<E>, T extends BaseEntityFieldMap<E>> void sortIfFieldExists(SortFilters<E> sortFilters,
                                                                                       JPAQuery<?> query,
                                                                                       EntityPathBase<T> entityPathBase,
                                                                                       Class clazz) throws EntityErrorsException {
        String fieldName = T.getEntityField(clazz, sortFilters.getField());

        Path<T> path = getPath(entityPathBase, fieldName);

        Order order = getOrder(sortFilters.getDirection());

        NullHandling nullHandling = getNullHandling(sortFilters.getDirection());

        if (Objects.nonNull(sortFilters.getSecondaryField()) && !sortFilters.getSecondaryField().equals(sortFilters.getField())) {

            String secondaryFieldName = T.getEntityField(clazz, sortFilters.getSecondaryField());

            Path<T> secondaryPath = getPath(entityPathBase, secondaryFieldName);

            query.orderBy(new OrderSpecifier(order, path, nullHandling), new OrderSpecifier(order, secondaryPath, nullHandling));

        } else {
            query.orderBy(new OrderSpecifier(order, path, nullHandling));
        }
    }


    private <E extends Enum<E>, T extends BaseEntityFieldMap<E>> void sortIfFieldExistsButNotDirection(SortFilters<E> sortFilters,
                                                                                                       JPAQuery<?> query,
                                                                                                       EntityPathBase<T> entityPathBase,
                                                                                                       Class clazz) throws EntityErrorsException {
        String fieldName = T.getEntityField(clazz, sortFilters.getField());
        Path<T> path = getPath(entityPathBase, fieldName);

        if (Objects.nonNull(sortFilters.getSecondaryField()) && !sortFilters.getSecondaryField().equals(sortFilters.getField())) {

            String secondaryFieldName = T.getEntityField(clazz, sortFilters.getSecondaryField());

            Path<T> secondaryPath = getPath(entityPathBase, secondaryFieldName);

            query.orderBy(new OrderSpecifier(Order.DESC, path, NullHandling.NullsFirst),
                new OrderSpecifier(Order.DESC, secondaryPath, NullHandling.NullsFirst));

        } else {
            query.orderBy(new OrderSpecifier(Order.DESC, path, NullHandling.NullsFirst));
        }

        query.orderBy(new OrderSpecifier(Order.DESC, path, NullHandling.NullsFirst));
    }

    private <E extends Enum<E>, T extends BaseEntityFieldMap<E>> void sortIfFilterNotExists(JPAQuery<?> query,
                                                                                          EntityPathBase<T> entityPathBase,
                                                                                          E defaultField,
                                                                                          Class clazz) throws EntityErrorsException {
        String fieldName = T.getEntityField(clazz, defaultField);
        Path<T> path = getPath(entityPathBase, fieldName);

        query.orderBy(new OrderSpecifier(Order.DESC, path, NullHandling.NullsFirst));
    }

    private <T> Path<T> getPath(EntityPathBase<T> entityPathBase, String fieldName) {
        return ExpressionUtils.path(entityPathBase.getType(), entityPathBase, fieldName);
    }

    private Order getOrder(OrderDirection direction) {
        return direction.equals(OrderDirection.ASC) ? Order.ASC : Order.DESC;
    }

    private NullHandling getNullHandling(OrderDirection direction) {
        return direction.equals(OrderDirection.ASC) ? NullHandling.NullsFirst : NullHandling.NullsLast;
    }

}
