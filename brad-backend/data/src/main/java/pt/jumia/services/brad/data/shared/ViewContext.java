package pt.jumia.services.brad.data.shared;

import pt.jumia.services.brad.domain.entities.ViewEntity;

public class ViewContext {
    private static final ThreadLocal<ViewEntity> CURRENT_CONTEXT = new ThreadLocal<>();

    public static ViewEntity getCurrentContext() {
        return CURRENT_CONTEXT.get();
    }

    public static void setCurrentContext(ViewEntity viewEntity) {
        CURRENT_CONTEXT.set(viewEntity);
    }

    public static void clear() {
        CURRENT_CONTEXT.remove();
    }
}
