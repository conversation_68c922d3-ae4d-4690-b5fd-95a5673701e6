package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.*;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.Currency;

import java.time.LocalDateTime;
import java.util.Objects;

@Setter
@Entity
@Audited
@Table(name = "currency")
@NoArgsConstructor
public class CurrencyPsql {

    @Id
    @SequenceGenerator(name = "currency_sequence_id_seq", sequenceName = "currency_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "currency_sequence_id_seq")
    @Column(name = "ID")
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "code", nullable = false)
    private String code;
    
    @Column(name = "symbol", nullable = false)
    private String symbol;

    @Column(name = "CREATED_BY", nullable = false, updatable = false)
    private String createdBy;

    @CreationTimestamp
    @Column(name = "CREATED_AT", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "UPDATED_BY", nullable = false)
    private String updatedBy;

    @UpdateTimestamp
    @Column(name = "UPDATED_AT", nullable = false)
    private LocalDateTime updatedAt;

    public CurrencyPsql(Currency currency) {

        this.id = currency.getId();
        this.name = currency.getName();
        this.code = currency.getCode();
        this.symbol = currency.getSymbol();
        this.createdBy = Objects.nonNull(currency.getCreatedBy())
                ? currency.getCreatedBy()
                : RequestContext.getUsername();
        this.createdAt = Objects.isNull(currency.getCreatedAt())
                ? LocalDateTime.now()
                : currency.getCreatedAt();
        this.updatedBy = Objects.nonNull(currency.getUpdatedBy())
                ? currency.getUpdatedBy()
                : RequestContext.getUsername();
        this.updatedAt = Objects.isNull(currency.getUpdatedAt())
                ? LocalDateTime.now()
                : currency.getUpdatedAt();
    }

    public Currency toEntity() {

        return Currency.builder()
                .id(this.id)
                .name(this.name)
                .code(this.code)
                .symbol(this.symbol)
                .createdBy(this.createdBy)
                .createdAt(this.createdAt)
                .updatedBy(this.updatedBy)
                .updatedAt(this.updatedAt)
                .build();
    }

}
