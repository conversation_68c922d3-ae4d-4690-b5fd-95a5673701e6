package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.*;
import lombok.NoArgsConstructor;
import lombok.Setter;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.entities.ExecutionLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Setter
@Entity
@NoArgsConstructor
@Table(name = "execution_log")
public class ExecutionLogPsql extends BaseEntityFieldMap<ExecutionLog.SortingFields> {

    @Id
    @SequenceGenerator(name = "execution_log_sequence_id_seq", sequenceName = "execution_log_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "execution_log_sequence_id_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "log_type")
    private String logType;

    @Column(name = "records_amount", nullable = false)
    private Integer recordsAmount;

    @Column(name = "execution_start_time")
    private LocalDateTime executionStartTime;

    @Column(name = "execution_end_time")
    private LocalDateTime executionEndTime;

    @Column(name = "applied_filters")
    private String appliedFilters;

    @Column(name = "query")
    private String query;

    @Column(name = "sync_errors")
    private String syncingErrors;

    @Column(name = "log_status")
    private String logStatus;

    public ExecutionLogPsql(ExecutionLog executionLog) {

        String nullString = null;
        this.id = executionLog.getId();
        this.logType = executionLog.getLogType().name();
        this.recordsAmount = executionLog.getRecordsAmount();
        this.executionStartTime = executionLog.getExecutionStartTime();
        this.executionEndTime = executionLog.getExecutionEndTime();
        this.appliedFilters = executionLog.getAppliedFilters();
        this.query = executionLog.getQuery();
        this.syncingErrors =
            Objects.isNull(executionLog.getErrors()) ? nullString : ExecutionLog.SyncingError.toString(executionLog.getErrors());
        this.logStatus = executionLog.getLogStatus().name();

    }

    public ExecutionLog toEntity() {
        ExecutionLog.ExecutionLogType logType = Objects.isNull(this.logType) ? null : ExecutionLog.ExecutionLogType.valueOf(this.logType);
        ExecutionLog.ExecutionLogStatus logStatus = Objects.isNull(this.logStatus) ? null : ExecutionLog.ExecutionLogStatus.valueOf(this.logStatus);
        return ExecutionLog.builder()
                .id(id)
                .logType(logType)
                .recordsAmount(recordsAmount)
                .executionStartTime(executionStartTime)
                .executionEndTime(executionEndTime)
                .appliedFilters(appliedFilters)
                .query(query)
                .errors(Objects.isNull(syncingErrors) ? List.of() : ExecutionLog.SyncingError.fromString(syncingErrors))
                .logStatus(logStatus)
                .build();
    }

    static {
        Map<ExecutionLog.SortingFields, String> entityFields = Map.of(
                ExecutionLog.SortingFields.ID, "id",
                ExecutionLog.SortingFields.LOG_TYPE, "logType",
                ExecutionLog.SortingFields.RECORDS_AMOUNT, "recordsAmount",
                ExecutionLog.SortingFields.EXECUTION_START_TIME, "executionStartTime",
                ExecutionLog.SortingFields.EXECUTION_END_TIME, "executionEndTime",
                ExecutionLog.SortingFields.LOG_STATUS, "logStatus"
        );
        addEntityFieldMap(ExecutionLog.SortingFields.class, entityFields);
    }

}
