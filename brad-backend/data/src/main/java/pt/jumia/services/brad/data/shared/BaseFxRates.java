package pt.jumia.services.brad.data.shared;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.FxRate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@MappedSuperclass
@NoArgsConstructor
@Getter
public abstract class BaseFxRates extends BaseEntityFieldMap<FxRate.SortingFields> {

    @Column(name = "`bid`", precision = 18, scale = 6)
    protected BigDecimal bid;

    @Column(name = "`bis_loaded_at`")
    protected LocalDateTime bisLoadedAt;

    @Column(name = "`SK_Aud_Insert`")
    protected Integer skAudInsert;

    @Column(name = "`SK_Aud_Update`")
    protected Integer skAudUpdate;

    @Column(name = "`Timestamp_Last_Update`")
    protected LocalDateTime timestampLastUpdate;

    public FxRate toEntity() {
        return FxRate.builder()
                .bid(bid)
                .bisLoadedAt(bisLoadedAt)
                .skAudInsert(skAudInsert)
                .skAudUpdate(skAudUpdate)
                .timestampLastUpdate(timestampLastUpdate)
                .build();
    }

    public BaseFxRates(FxRate fxRate) {
        this.bid = fxRate.getBid();
        this.bisLoadedAt = fxRate.getBisLoadedAt();
        this.skAudInsert = fxRate.getSkAudInsert();
        this.skAudUpdate = fxRate.getSkAudUpdate();
        this.timestampLastUpdate = fxRate.getTimestampLastUpdate();
    }

    static {
        Map<FxRate.SortingFields, String> entityFields = new HashMap<>();
        entityFields.put(FxRate.SortingFields.BASE_CURRENCY, "baseCurrency");
        entityFields.put(FxRate.SortingFields.QUOTE_CURRENCY, "quoteCurrency");
        entityFields.put(FxRate.SortingFields.RATE_DATE, "rateDate");
        entityFields.put(FxRate.SortingFields.BID, "bid");
        entityFields.put(FxRate.SortingFields.BIS_LOADED_AT, "bisLoadedAt");
        entityFields.put(FxRate.SortingFields.SK_AUD_INSERT, "skAudInsert");
        entityFields.put(FxRate.SortingFields.SK_AUD_UPDATE, "skAudUpdate");
        entityFields.put(FxRate.SortingFields.TIMESTAMP_LAST_UPDATE, "timestampLastUpdate");

        addEntityFieldMap(FxRate.SortingFields.class, entityFields);
    }

}
