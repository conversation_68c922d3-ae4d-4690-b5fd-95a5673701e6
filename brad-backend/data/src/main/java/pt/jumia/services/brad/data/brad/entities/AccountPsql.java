package pt.jumia.services.brad.data.brad.entities;


import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.enumerations.StatementSource;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Setter
@Entity
@Audited
@NoArgsConstructor
@Table(name = "bank_account")
public class AccountPsql extends BaseEntityFieldMap<Account.SortingFields> {

    public static final String ACCOUNT = "account";

    @Id
    @Getter
    @SequenceGenerator(name = "bank_account_id_seq", sequenceName = "bank_account_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "bank_account_sequence_id_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "company_id", nullable = false)
    private String companyID;

    @ManyToOne(optional = false)
    @JoinColumn(name = "fk_country", nullable = false)
    private CountryPsql country;

    @Column(name = "partner")
    private String partner;

    @Column(name = "nav_reference", nullable = false)
    private String navReference;

    @Column(name = "beneficiary_name")
    private String beneficiaryName;

    @Column(name = "beneficiary_address")
    private String beneficiaryAddress;

    @Column(name = "iban")
    private String iban;

    @Column(name = "bank_account_number")
    private String accountNumber;

    @Column(name = "bank_name")
    private String accountName;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "swift_code")
    private String swiftCode;

    @Column(name = "bank_routing_code")
    private String bankRoutingCode;

    @Column(name = "sort_code")
    private String sortCode;

    @Column(name = "branch_code")
    private String branchCode;

    @Column(name = "rib")
    private String rib;

    @Column(name = "type")
    private String type;

    @Column(name = "sub_type")
    private String subType;

    @Column(name = "status")
    private String status;

    @Column(name = "statement_source", nullable = false)
    private String statementSource;

    @Column(name = "statement_periodicity")
    private String statementPeriodicity;

    @Column(name = "last_processed_statement_date")
    private LocalDate lastProcessedStatementDate;

    @ManyToOne(optional = false)
    private CurrencyPsql currency;

    @Column(name = "isin")
    private String isin;

    @Column(name = "contract_id")
    private String contractId;

    @Column(name = "amount_deposited")
    private BigDecimal amountDeposited;

    @Column(name = "maturity_date")
    private LocalDate maturityDate;

    @Column(name = "nominal_amount")
    private BigDecimal nominalAmount;

    @Column(name = "coupon_payment_periodicity")
    private String couponPaymentPeriodicity;

    @Column(name = "coupon_rate")
    private BigDecimal couponRate;

    @Column(name = "interest")
    private BigDecimal interest;

    @NotAudited
    @OneToMany(mappedBy = ACCOUNT, cascade = CascadeType.REMOVE)
    private List<ContactPsql> contacts;

    @NotAudited
    @OneToMany(mappedBy = ACCOUNT, cascade = CascadeType.REMOVE)
    private List<UserPsql> users;

    @NotAudited
    @OneToMany(mappedBy = ACCOUNT, cascade = CascadeType.REMOVE)
    private List<DocumentPsql> documents;

    @NotAudited
    @OneToMany(mappedBy = ACCOUNT, cascade = CascadeType.REMOVE)
    private Set<AccountStatementPsql> accountStatements;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "created_by", nullable = false, updatable = false)
    private String createdBy;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;


    public AccountPsql(Account account) {
        this.id = account.getId();
        this.companyID = account.getCompanyID();
        this.country = new CountryPsql(account.getCountry());
        this.partner = account.getPartner();
        this.navReference = account.getNavReference();
        this.beneficiaryName = account.getBeneficiaryName();
        this.beneficiaryAddress = account.getBeneficiaryAddress();
        this.iban = account.getIban();
        this.accountNumber = account.getAccountNumber();
        this.accountName = account.getAccountName();
        this.phoneNumber = account.getPhoneNumber();
        this.swiftCode = account.getSwiftCode();
        this.bankRoutingCode = account.getBankRoutingCode();
        this.sortCode = account.getSortCode();
        this.branchCode = account.getBranchCode();
        this.rib = account.getRib();
        this.type = account.getType().name();
        this.subType = account.getSubType() != null ? account.getSubType().name() : null;
        this.status = account.getStatus().getValue();
        this.currency = new CurrencyPsql(account.getCurrency());
        this.statementSource = account.getStatementSource().getValue();
        this.statementPeriodicity = account.getStatementPeriodicity().name();
        this.isin = account.getIsin();
        this.contractId = account.getContractId();
        this.amountDeposited = account.getAmountDeposited();
        this.maturityDate = account.getMaturityDate();
        this.nominalAmount = account.getNominalAmount();
        this.couponPaymentPeriodicity = account.getCouponPaymentPeriodicity() != null ?
                account.getCouponPaymentPeriodicity().name() : null;
        this.couponRate = account.getCouponRate();
        this.interest = account.getInterest();
        this.createdBy = account.getCreatedBy() == null
                ? RequestContext.getUsername()
                : account.getCreatedBy();
        this.createdAt = account.getCreatedAt() == null
                ? LocalDateTime.now()
                : account.getCreatedAt();
        this.updatedBy = account.getUpdatedBy() == null
                ? RequestContext.getUsername()
                : account.getUpdatedBy();
        this.updatedAt = account.getUpdatedAt() == null
                ? LocalDateTime.now()
                : account.getUpdatedAt();
        this.statementSource = account.getStatementSource().getValue();
        this.statementPeriodicity = account.getStatementPeriodicity().name();
        this.lastProcessedStatementDate = account.getLastProcessedStatementDate();
    }

    public Account toEntity() {
        return Account
                .builder()
                .id(id)
                .companyID(companyID)
                .country(country.toEntity())
                .partner(partner)
                .navReference(navReference)
                .beneficiaryName(beneficiaryName)
                .beneficiaryAddress(beneficiaryAddress)
                .iban(iban)
                .accountNumber(accountNumber)
                .accountName(accountName)
                .phoneNumber(phoneNumber)
                .swiftCode(swiftCode)
                .bankRoutingCode(bankRoutingCode)
                .sortCode(sortCode)
                .branchCode(branchCode)
                .rib(rib)
                .type(Account.Type.valueOf(type))
                .subType(subType != null ? Account.SubType.valueOf(subType) : null)
                .status(Account.getStatus(status))
                .statementSource(StatementSource.valueOf(statementSource))
                .statementPeriodicity(Account.StatementPeriodicity.valueOf(statementPeriodicity))
                .lastProcessedStatementDate(lastProcessedStatementDate)
                .currency(currency.toEntity())
                .isin(isin)
                .contractId(contractId)
                .amountDeposited(amountDeposited)
                .maturityDate(maturityDate)
                .nominalAmount(nominalAmount)
                .couponPaymentPeriodicity(couponPaymentPeriodicity != null ?
                        Account.CouponPaymentPeriodicity.valueOf(couponPaymentPeriodicity) : null)
                .couponRate(couponRate)
                .interest(interest)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }


    static {
        Map<Account.SortingFields, String> entityFields = Map.ofEntries(
                Map.entry(Account.SortingFields.ID, "id"),
                Map.entry(Account.SortingFields.COMPANY_ID, "companyID"),
                Map.entry(Account.SortingFields.COUNTRY, "country"),
                Map.entry(Account.SortingFields.NAV_REFERENCE, "navReference"),
                Map.entry(Account.SortingFields.BENEFICIARY_NAME, "beneficiaryName"),
                Map.entry(Account.SortingFields.BENEFICIARY_ADDRESS, "beneficiaryAddress"),
                Map.entry(Account.SortingFields.IBAN, "iban"),
                Map.entry(Account.SortingFields.ACCOUNT_NUMBER, "accountNumber"),
                Map.entry(Account.SortingFields.ACCOUNT_NAME, "accountName"),
                Map.entry(Account.SortingFields.SWIFT_CODE, "swiftCode"),
                Map.entry(Account.SortingFields.BANK_ROUTING_CODE, "bankRoutingCode"),
                Map.entry(Account.SortingFields.SORT_CODE, "sortCode"),
                Map.entry(Account.SortingFields.BRANCH_CODE, "branchCode"),
                Map.entry(Account.SortingFields.RIB, "rib"),
                Map.entry(Account.SortingFields.TYPE, "type"),
                Map.entry(Account.SortingFields.SUB_TYPE, "subType"),
                Map.entry(Account.SortingFields.STATUS, "status"),
                Map.entry(Account.SortingFields.STATEMENT_SOURCE, "statementSource"),
                Map.entry(Account.SortingFields.STATEMENT_PERIODICITY, "statementPeriodicity"),
                Map.entry(Account.SortingFields.LAST_PROCESSED_STATEMENT_DATE, "lastProcessedStatementDate"),
                Map.entry(Account.SortingFields.CURRENCY, "currency"),
                Map.entry(Account.SortingFields.CREATED_AT, "createdAt"),
                Map.entry(Account.SortingFields.CREATED_BY, "createdBy"),
                Map.entry(Account.SortingFields.UPDATED_AT, "updatedAt"),
                Map.entry(Account.SortingFields.UPDATED_BY, "updatedBy")
        );
        addEntityFieldMap(Account.SortingFields.class, entityFields);
    }

}

