package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.PartitionKey;
import org.hibernate.envers.NotAudited;
import pt.jumia.services.brad.data.brad.entities.fxrates.transaction.FxRateTransactionPsql;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Entity
@Table(name = "transactions")
@NoArgsConstructor
public class TransactionPsql extends BaseEntityFieldMap<Transaction.SortingFields> implements Serializable {

    @Serial
    private static final long serialVersionUID = 4873905264283L;

    @Id
    @SequenceGenerator(name = "transaction_id_seq", sequenceName = "transactions_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "transactions_sequence_id_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "type", nullable = false)
    private String type;

    @ManyToOne(optional = false)
    private CurrencyPsql currency;

    @Column(name = "value_date", nullable = false)
    private LocalDate valueDate;

    @Column(name = "transaction_date", nullable = false)
    private LocalDate transactionDate;

    @Column(name = "statement_date", nullable = false)
    private LocalDate statementDate;

    @Column(name = "direction", nullable = false)
    private Integer direction;

    @Column(name = "amount", nullable = false)
    private BigDecimal amount;

    @Getter
    @NotAudited
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "transaction", cascade = CascadeType.ALL)
    private Set<FxRateTransactionPsql> fxRateTransactionSet;

    @Column(name = "reference")
    private String reference;

    @Column(name = "description", nullable = false)
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bank_statement", nullable = false)
    private AccountStatementPsql accountStatement;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    @Getter
    @PartitionKey
    @Column(name = "partition_key", nullable = false)
    private String partitionKey;

    @Column(name = "sk_aud_insert")
    private Integer skAudInsert;

    @Column(name = "timestamp_run_at")
    private LocalDateTime timestampRunAt;

    @Column(name = "Reconcile_Status")
    protected String reconcileStatus;

    @Column(name = "remittance_information")
    private String remittanceInformation;

    @Column(name = "ordering_party_name")
    private String orderingPartyName;

    public TransactionPsql(Transaction transaction) {
        this.id = transaction.getId();
        this.type = transaction.getType();
        this.currency = new CurrencyPsql(transaction.getCurrency());
        this.valueDate = transaction.getValueDate();
        this.transactionDate = transaction.getTransactionDate();
        this.statementDate = transaction.getStatementDate();
        this.direction = transaction.getDirection().getValue();
        this.amount = transaction.getAmount();
        this.reference = transaction.getReference();
        this.description = transaction.getDescription();
        this.accountStatement = new AccountStatementPsql(transaction.getAccountStatement());
        this.createdBy = transaction.getCreatedBy();
        this.createdAt = transaction.getCreatedAt();
        this.updatedBy = transaction.getUpdatedBy();
        this.updatedAt = transaction.getUpdatedAt();
        this.partitionKey = String.valueOf(transaction.getAccountStatement().getAccount().getId());
        this.skAudInsert = transaction.getSkAudInsert();
        this.timestampRunAt = transaction.getTimestampRunAt();
        this.reconcileStatus = transaction.getReconcileStatus().name();
        this.remittanceInformation = transaction.getRemittanceInformation();
        this.orderingPartyName = transaction.getOrderingPartyName();
    }

    public TransactionPsql(final Transaction transaction, String username) {

        this.id = transaction.getId();
        this.type = transaction.getType();
        this.currency = new CurrencyPsql(transaction.getCurrency());
        this.valueDate = transaction.getValueDate();
        this.transactionDate = transaction.getTransactionDate();
        this.statementDate = transaction.getStatementDate();
        this.direction = transaction.getDirection().getValue();
        this.amount = transaction.getAmount();
        this.reference = transaction.getReference();
        this.description = transaction.getDescription();
        this.accountStatement = new AccountStatementPsql(transaction.getAccountStatement());
        this.createdBy = transaction.getCreatedBy() == null
                ? username
                : transaction.getCreatedBy();
        this.createdAt = transaction.getCreatedAt() == null
                ? LocalDateTime.now()
                : transaction.getCreatedAt();
        this.updatedBy = transaction.getUpdatedBy() == null
                ? username
                : transaction.getUpdatedBy();
        this.updatedAt = transaction.getUpdatedAt() == null
                ? LocalDateTime.now()
                : transaction.getUpdatedAt();
        this.partitionKey = String.valueOf(transaction.getAccountStatement().getAccount().getId());
        this.skAudInsert = transaction.getSkAudInsert();
        this.timestampRunAt = transaction.getTimestampRunAt();
        this.reconcileStatus = transaction.getReconcileStatus().name();
        this.remittanceInformation = transaction.getRemittanceInformation();
        this.orderingPartyName = transaction.getOrderingPartyName();
    }



    public static TransactionPsql fromEntity(Transaction transaction) {
        return new TransactionPsql(transaction);
    }

    public Transaction toEntity() {
        AccountStatement nullStatement = null;
        ReconcileStatus nullStatus = null;

        Set<FxRate> fxRates = new HashSet<>();
        if (Objects.nonNull(this.fxRateTransactionSet)) {
            fxRates = this.fxRateTransactionSet.stream()
                    .map(fxRateTransactionPsql -> fxRateTransactionPsql.getFxRate().toEntity())
                    .collect(Collectors.toSet());
        }

        return Transaction.builder()
                .id(this.id)
                .type(this.type)
                .currency(this.currency.toEntity())
                .valueDate(valueDate)
                .transactionDate(transactionDate)
                .statementDate(statementDate)
                .direction(Direction.getDirection(String.valueOf(this.direction)))
                .amount(this.amount)
                .fxRates(fxRates)
                .reference(this.reference)
                .description(this.description)
                .accountStatement(Objects.isNull(this.accountStatement) ? nullStatement : this.accountStatement.toEntity())
                .createdAt(this.createdAt)
                .createdBy(this.createdBy)
                .updatedAt(this.updatedAt)
                .updatedBy(this.updatedBy)
                .skAudInsert(this.skAudInsert)
                .timestampRunAt(this.timestampRunAt)
                .reconcileStatus(Objects.isNull(this.reconcileStatus) ? nullStatus : ReconcileStatus.valueOf(this.reconcileStatus))
                .remittanceInformation(this.remittanceInformation)
                .orderingPartyName(this.orderingPartyName)
                .build();
    }

    static {
        Map<Transaction.SortingFields, String> entityFields = Map.ofEntries(
                Map.entry(Transaction.SortingFields.ID, "id"),
                Map.entry(Transaction.SortingFields.TYPE, "type"),
                Map.entry(Transaction.SortingFields.CURRENCY, "currency"),
                Map.entry(Transaction.SortingFields.VALUE_DATE, "valueDate"),
                Map.entry(Transaction.SortingFields.TRANSACTION_DATE, "transactionDate"),
                Map.entry(Transaction.SortingFields.STATEMENT_DATE, "statementDate"),
                Map.entry(Transaction.SortingFields.STATEMENT_ID, "accountStatement"),
                Map.entry(Transaction.SortingFields.DIRECTION, "direction"),
                Map.entry(Transaction.SortingFields.AMOUNT, "amount"),
                Map.entry(Transaction.SortingFields.REFERENCE, "reference"),
                Map.entry(Transaction.SortingFields.DESCRIPTION, "description"),
                Map.entry(Transaction.SortingFields.REMITTANCE_INFORMATION, "remittanceInformation"),
                Map.entry(Transaction.SortingFields.ORDERING_PARTY_NAME, "orderingPartyName"),
                Map.entry(Transaction.SortingFields.CREATED_AT, "createdAt"),
                Map.entry(Transaction.SortingFields.CREATED_BY, "createdBy"),
                Map.entry(Transaction.SortingFields.UPDATED_AT, "updatedAt"),
                Map.entry(Transaction.SortingFields.UPDATED_BY, "updatedBy")
        );
        BaseEntityFieldMap.addEntityFieldMap(Transaction.SortingFields.class, entityFields);
    }
}
