package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.DocumentPsql;
import pt.jumia.services.brad.data.brad.entities.QDocumentPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaDocumentRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.document.DocumentFilters;
import pt.jumia.services.brad.domain.entities.filter.document.DocumentSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.DocumentRepository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlDocumentRepository extends BaseRepository implements DocumentRepository {

    private final QDocumentPsql root = new QDocumentPsql("root");
    private final JpaDocumentRepository jpaDocumentRepository;
    private final EntityManager entityManager;

    @Override
    public Document upsert(Document document) {
        return jpaDocumentRepository.save(new DocumentPsql(document)).toEntity();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Document> findById(long id) {

        return jpaDocumentRepository.findById(id)
                .map(DocumentPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Document> findAll(DocumentFilters documentFilters, DocumentSortFilters documentSortFilters,
                                  PageFilters pageFilters) throws EntityErrorsException {
        JPAQuery<DocumentPsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root);

        if (Objects.nonNull(documentFilters)) {
            applyProjectionSelect(documentFilters.getSelectedFields(), query, root, DocumentPsql.class);
        }
        buildWhereClauses(documentFilters, query);
        applySort(documentSortFilters, query, root, Document.SortingFields.class);
        applyPagination(pageFilters, query);


        return query.distinct().fetch().stream()
                .map(DocumentPsql::toEntity)
                .collect(Collectors.toList());


    }

    @Override
    @Transactional(readOnly = true)
    public Integer count(DocumentFilters documentFilters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(documentFilters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    @Override
    @Transactional
    public void deleteById(long id) {
        jpaDocumentRepository.deleteById(id);
    }

    private void buildWhereClauses(DocumentFilters documentFilters, JPAQuery<?> query) {
        if (Objects.isNull(documentFilters)) {
            return;
        }
        filterByDocumentType(documentFilters, query);
        filterByName(documentFilters, query);
        filterByDescription(documentFilters, query);
        filterByAccountId(documentFilters, query);
        filterByFile(documentFilters, query);

    }

    public void filterByDocumentType(DocumentFilters documentFilters, JPAQuery<?> query) {
        if (Objects.isNull(documentFilters.getTypes())) {
            return;
        }
        query.where(root.documentType.in(documentFilters.getTypes()));
    }

    public void filterByName(DocumentFilters documentFilters, JPAQuery<?> query) {
        if (Objects.isNull(documentFilters.getName())) {
            return;
        }
        query.where(root.name.eq(documentFilters.getName()));
    }

    public void filterByDescription(DocumentFilters documentFilters, JPAQuery<?> query) {
        if (Objects.isNull(documentFilters.getDescription())) {
            return;
        }
        query.where(root.description.eq(documentFilters.getDescription()));
    }

    public void filterByAccountId(DocumentFilters documentFilters, JPAQuery<?> query) {
        if (Objects.isNull(documentFilters.getAccountId())) {
            return;
        }
        query.where(root.account.id.eq(documentFilters.getAccountId()));
    }

    public void filterByFile(DocumentFilters documentFilters, JPAQuery<?> query) {
        if (Objects.isNull(documentFilters.getFile())) {
            return;
        }
        query.where(root.file.eq(documentFilters.getFile()));
    }

}
