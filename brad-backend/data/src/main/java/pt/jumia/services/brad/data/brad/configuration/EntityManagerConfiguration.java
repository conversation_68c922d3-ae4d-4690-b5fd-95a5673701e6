package pt.jumia.services.brad.data.brad.configuration;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.spi.PersistenceUnitTransactionType;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.hibernate.dialect.PostgreSQL10Dialect;
import org.hibernate.jpa.HibernatePersistenceProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.Database;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import pt.jumia.services.brad.data.brad.utils.MigrationService;
import pt.jumia.services.brad.domain.properties.DataProperties;
import pt.jumia.services.brad.domain.properties.SpringProperties;

import javax.sql.DataSource;
import java.util.Properties;

@Configuration
@Getter
@RequiredArgsConstructor
@EnableTransactionManagement(proxyTargetClass = true)
public class EntityManagerConfiguration {

    private final SpringProperties springProperties;
    private final DataProperties dataProperties;
    private final MigrationService migrationService;

    @Bean
    @Primary
    public JpaTransactionManager transactionManager(EntityManagerFactory emf) {
        return new JpaTransactionManager(emf);
    }

    @Bean
    @Primary
    public LocalContainerEntityManagerFactoryBean entityManagerFactory() {
        // setup database
        migrateApplicationSchemas();

        LocalContainerEntityManagerFactoryBean fact = new LocalContainerEntityManagerFactoryBean();
        fact.setPackagesToScan("pt.jumia.services.brad.data.brad.entities");
        fact.setDataSource(dataSource());

        HibernateJpaVendorAdapter vendor = new HibernateJpaVendorAdapter();
        vendor.setDatabase(Database.POSTGRESQL);

        fact.setJpaVendorAdapter(vendor);
        fact.setPersistenceProvider(new HibernatePersistenceProvider());

        final Properties properties = new Properties();
        additionalProperties(properties);
        fact.setJpaProperties(properties);

        return fact;
    }

    @Bean
    @Primary
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();
        config.setDriverClassName(dataProperties.getDb().getDriver());
        config.setJdbcUrl(dataProperties.getDb().getUrl());
        config.setUsername(dataProperties.getDb().getUsername());
        config.setPassword(dataProperties.getDb().getPassword());
        config.setMaximumPoolSize(dataProperties.getDb().getMaxPoolSize());

        return new HikariDataSource(config);
    }

    //quartz datasource
    @Bean
    public DataSource quartzDataSource() {
        HikariConfig config = new HikariConfig();
        config.setDriverClassName(dataProperties.getDb().getDriver());
        config.setJdbcUrl(dataProperties.getDb().getUrl());
        config.setUsername(dataProperties.getDb().getUsername());
        config.setPassword(dataProperties.getDb().getPassword());
        config.setMaximumPoolSize(dataProperties.getDb().getMaxPoolSize());

        return new HikariDataSource(config);
    }

    private void additionalProperties(Properties properties) {
        properties.put("hibernate.dialect", PostgreSQL10Dialect.class.getName());
        properties.put("hibernate.default_schema", dataProperties.getDb().getApplicationSchema());
        properties.put("hibernate.show_sql", Boolean.FALSE.toString());
        properties.put("hibernate.format_sql", Boolean.FALSE.toString());
        properties.put("hibernate.transaction.auto_close_session", Boolean.FALSE.toString());
        properties.put("hibernate.use_sql_comments", Boolean.FALSE.toString());
        properties.put("hibernate.jdbc.lob.non_contextual_creation", Boolean.TRUE.toString());
        properties.put("hibernate.connection.release_mode", "auto");
        properties.put("org.hibernate.envers.store_data_at_delete", Boolean.TRUE.toString());
        properties.put("org.hibernate.envers.default_schema", dataProperties.getDb().getAuditSchema());
        properties.put("jakarta.persistence.transactionType", PersistenceUnitTransactionType.RESOURCE_LOCAL.name());
    }

    private void migrateApplicationSchemas() {
        for (String schemaDir : springProperties.getFlyway().getSchemas()) {
            migrationService.migrateDatabaseSchema(dataSource(), schemaDir);
        }
    }
}
