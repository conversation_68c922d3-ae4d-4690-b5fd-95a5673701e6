package pt.jumia.services.brad.data.brad.entities.reconciliation.transaction;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.data.brad.entities.TransactionPsql;
import pt.jumia.services.brad.data.brad.entities.reconciliation.ReconciliationPsql;

@Getter
@Entity
@NoArgsConstructor
@Table(name = "RECONCILIATIONS_TRANSACTIONS")
public class ReconciliationTransactionPsql {

    @EmbeddedId
    private ReconciliationTransactionPsqlId id;

    @MapsId("reconciliationKey")
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumns({
            @JoinColumn(name = "RECONCILIATION_ID", referencedColumnName = "ID"),
            @JoinColumn(name = "ID_COMPANY", referencedColumnName = "ID_COMPANY")
    })
    private ReconciliationPsql reconciliation;

    @MapsId("transactionKey")
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumns({
            @JoinColumn(name = "TRANSACTION_ID", referencedColumnName = "ID"),
            @JoinColumn(name = "PARTITION_KEY", referencedColumnName = "PARTITION_KEY")
    })
    private TransactionPsql transactionPsql;

    @SuppressFBWarnings("EI_EXPOSE_REP2")
    public ReconciliationTransactionPsql(ReconciliationPsql reconciliation, TransactionPsql transaction, String idCompany) {
        this.reconciliation = reconciliation;
        this.transactionPsql = transaction;
        this.id = new ReconciliationTransactionPsqlId(reconciliation.toEntity().getId(),
                transaction.toEntity().getId(), transaction.getPartitionKey(), idCompany);
    }

}
