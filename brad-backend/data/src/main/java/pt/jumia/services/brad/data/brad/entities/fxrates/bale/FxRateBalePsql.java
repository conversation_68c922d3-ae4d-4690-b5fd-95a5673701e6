package pt.jumia.services.brad.data.brad.entities.fxrates.bale;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.data.brad.entities.BalePsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.FxRatePsql;

@Getter
@Entity
@NoArgsConstructor
@Table(name = "FX_RATES_BALE")
public class FxRateBalePsql {

    @EmbeddedId
    private FxRateBalePsqlId id;

    @MapsId("fxRateKey")
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumns({
            @JoinColumn(name = "FX_RATES_ID", referencedColumnName = "ID")
    })
    private FxRatePsql fxRate;

    @MapsId("baleKey")
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumns({
            @JoinColumn(name = "BALE_ID", referencedColumnName = "ID"),
            @JoinColumn(name = "ID_COMPANY", referencedColumnName = "ID_COMPANY")
    })
    private BalePsql bale;

    @SuppressFBWarnings("EI_EXPOSE_REP2")
    public FxRateBalePsql(FxRatePsql fxRate, BalePsql bale) {
        this.fxRate = fxRate;
        this.bale = bale;
        this.id = new FxRateBalePsqlId(fxRate.toEntity().getId(),
                bale.toEntity().getId(), bale.getIdCompany());
    }

}
