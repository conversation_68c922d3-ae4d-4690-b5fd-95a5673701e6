package pt.jumia.services.brad.data.bale.configuration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;

import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.transaction.PlatformTransactionManager;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.exceptions.InvalidEntityException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.usecases.bale.BaleProcessingException;
import pt.jumia.services.brad.domain.usecases.bale.batch.BaleJobExecutionListener;
import pt.jumia.services.brad.domain.usecases.bale.batch.BaleSkipListener;
import pt.jumia.services.brad.domain.usecases.bale.batch.BaleStepExecutionListener;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.CurrencyResolutionException;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.FxRateUnavailableException;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class BaleBatchConfig {
    @Bean("baleJob")
    public Job baleJob(JobRepository jobRepository, 
                       Step baleProcessingStep,
                       BaleJobExecutionListener baleJobExecutionListener) {
        return new JobBuilder("baleSyncJob", jobRepository)
                .incrementer(new RunIdIncrementer())
                .listener(baleJobExecutionListener)
                .start(baleProcessingStep)
                .build();
    }

    @Bean
    public Step baleProcessingStep(JobRepository jobRepository,
                                   PlatformTransactionManager transactionManager,
                                   ItemReader<Bale> baleItemReader,
                                   ItemProcessor<Bale, Bale> baleItemProcessor,
                                   ItemWriter<Bale> baleItemWriter,
                                   BaleSkipListener baleSkipListener,
                                   BaleStepExecutionListener baleStepExecutionListener) {
        return new StepBuilder("baleProcessingStep", jobRepository)
                .<Bale, Bale>chunk(500, transactionManager)
                .reader(baleItemReader)
                .processor(baleItemProcessor)
                .writer(baleItemWriter)
                .faultTolerant()
                .retryLimit(3)
                .retry(TransientDataAccessException.class)
                .retryPolicy(createRetryPolicy())
                .backOffPolicy(createBackOffPolicy())
                .skipLimit(50)
                .skip(InvalidEntityException.class)
                .skip(NotFoundException.class)
                .skip(CurrencyResolutionException.class)
                .skip(FxRateUnavailableException.class)
                .noSkip(BaleProcessingException.class)
                .listener(baleSkipListener)
                .listener(baleStepExecutionListener)
                .build();
    }

    private SimpleRetryPolicy createRetryPolicy() {
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(3);
        return retryPolicy;
    }

    private ExponentialBackOffPolicy createBackOffPolicy() {
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(1000L); // 1 second
        backOffPolicy.setMaxInterval(10000L);    // 10 seconds max
        backOffPolicy.setMultiplier(2.0);        // Double each retry
        return backOffPolicy;
    }
}
