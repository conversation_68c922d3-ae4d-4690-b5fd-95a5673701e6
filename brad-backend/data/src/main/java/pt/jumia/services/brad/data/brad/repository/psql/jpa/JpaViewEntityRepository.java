package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import pt.jumia.services.brad.data.brad.entities.ViewEntityPsql;

import java.util.List;

public interface JpaViewEntityRepository extends JpaRepository<ViewEntityPsql, Long>, JpaSpecificationExecutor<ViewEntityPsql> {

    List<ViewEntityPsql> findAllByEntityType(String entityType);

}
