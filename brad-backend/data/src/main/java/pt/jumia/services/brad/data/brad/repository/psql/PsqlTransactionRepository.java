package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.QTransactionPsql;
import pt.jumia.services.brad.data.brad.entities.TransactionPsql;
import pt.jumia.services.brad.data.brad.entities.dto.TransactionWithErrorDTO;
import pt.jumia.services.brad.data.brad.entities.dto.TransactionWithReconciliationDTO;
import pt.jumia.services.brad.data.brad.entities.fxrates.FxRatePsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.transaction.FxRateTransactionPsql;
import pt.jumia.services.brad.data.brad.entities.reconciliation.transaction.QReconciliationTransactionPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaFxRateRepository;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaFxRateTransactionRepository;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaTransactionRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionGroupFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionSortFilters;
import pt.jumia.services.brad.domain.entities.group.GroupInfo;
import pt.jumia.services.brad.domain.entities.group.TransactionGroup;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.TransactionRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Repository
@RequiredArgsConstructor
public class PsqlTransactionRepository extends BaseRepository implements TransactionRepository {


    private final QTransactionPsql root = new QTransactionPsql("root");
    private final QReconciliationTransactionPsql reconciliationTransaction = new QReconciliationTransactionPsql("reconciliationTransaction");
    private final JpaTransactionRepository jpaTransactionRepository;
    private final EntityManager entityManager;

    private final JpaFxRateTransactionRepository jpaFxRateTransactionRepository;
    private final JpaFxRateRepository jpaFxRateRepository;

    @Async
    @Override
    @Transactional
    public CompletableFuture<Transaction> insert(Transaction transaction, String username) {

        final TransactionPsql transactionPsql = jpaTransactionRepository.save(new TransactionPsql(transaction, username));

        if (Objects.isNull(transaction.getFxRates())) {
            return CompletableFuture.completedFuture(this.fetch(transactionPsql));
        }

        addFxRates(Map.of(transaction, transactionPsql));

        return CompletableFuture.completedFuture(this.fetch(transactionPsql));
    }

    private void addFxRates(Map<Transaction, TransactionPsql> transactionMap) {
        List<FxRateTransactionPsql> fxRateTransactionPsqls = new ArrayList<>();
        transactionMap.forEach((transaction, transactionPsql) -> {
            Set<FxRate> fxRates = transaction.getFxRates();
            if (Objects.isNull(fxRates)) {
                return;
            }
            fxRates.forEach(fxRate -> {
                FxRatePsql existingFxRate = jpaFxRateRepository.findById(fxRate.getId())
                        .orElseThrow(() -> new RuntimeException("FxRate not found while adding fx rates"));
                FxRateTransactionPsql fxRateTransactionPsql = new FxRateTransactionPsql(
                        existingFxRate, transactionPsql
                );
                fxRateTransactionPsqls.add(fxRateTransactionPsql);
            });
        });

        jpaFxRateTransactionRepository.saveAll(fxRateTransactionPsqls);

    }

    @Override
    @Transactional
    public void addFxRates(List<Transaction> transactions) {
        Map<Transaction, TransactionPsql> transactionMap = new HashMap<>();
        transactions.forEach(transaction -> {
            TransactionPsql transactionPsql = jpaTransactionRepository.findById(transaction.getId())
                    .orElseThrow(() -> new RuntimeException("Transaction not found while adding fx rates"));
            transactionMap.put(transaction, transactionPsql);
        });

        this.addFxRates(transactionMap);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Transaction> findAllWithReconciliation(TransactionFilters transactionFilters, TransactionSortFilters transactionSortFilters,
                                                       PageFilters pageFilters) throws EntityErrorsException {

        JPAQuery<TransactionWithReconciliationDTO> query = new JPAQueryFactory(entityManager)
                .select(Projections.fields(TransactionWithReconciliationDTO.class,
                        root.id.as("id"),
                        root.type.as("type"),
                        root.currency.as("currency"),
                        root.valueDate.as("valueDate"),
                        root.transactionDate.as("transactionDate"),
                        root.statementDate.as("statementDate"),
                        root.direction.as("direction"),
                        root.amount.as("amount"),
                        root.reference.as("reference"),
                        root.description.as("description"),
                        root.accountStatement.as("accountStatement"),
                        root.remittanceInformation.as("remittanceInformation"),
                        root.orderingPartyName.as("orderingPartyName"),
                        root.createdAt.as("createdAt"),
                        root.createdBy.as("createdBy"),
                        root.updatedAt.as("updatedAt"),
                        root.updatedBy.as("updatedBy"),
                        root.partitionKey.as("partitionKey"),
                        root.reconcileStatus.as("reconcileStatus"),
                        reconciliationTransaction.reconciliation.id.as("reconciliationId"),
                        reconciliationTransaction.reconciliation.creator.as("reconciliationCreator"),
                        reconciliationTransaction.reconciliation.creationDate.as("reconciliationCreationDate"),
                        reconciliationTransaction.reconciliation.reviewer.as("reconciliationReviewer"),
                        reconciliationTransaction.reconciliation.reviewDate.as("reconciliationReviewDate"),
                        reconciliationTransaction.reconciliation.status.as("reconciliationStatus")))
                .from(root)
                .leftJoin(reconciliationTransaction).on(reconciliationTransaction.transactionPsql.id.eq(root.id))
                .leftJoin(reconciliationTransaction.reconciliation).on(reconciliationTransaction.reconciliation.id
                        .eq(reconciliationTransaction.reconciliation.id));

        buildWhereClauses(transactionFilters, query);
        applySort(transactionSortFilters, query, root, Transaction.SortingFields.class);
        applyPagination(pageFilters, query);

        return query.fetch().stream()
                .map(this::fetch)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Transaction> findAll(TransactionFilters transactionFilters, TransactionSortFilters transactionSortFilters,
        PageFilters pageFilters) throws EntityErrorsException {

        JPAQuery<TransactionPsql> query = new JPAQueryFactory(entityManager)
            .select(root)
            .from(root);

        buildWhereClauses(transactionFilters, query);
        applySort(transactionSortFilters, query, root, Transaction.SortingFields.class);
        applyPagination(pageFilters, query);

        return query.fetch().stream().map(TransactionPsql::toEntity).collect(Collectors.toList());

    }

    @Override
    @Transactional(readOnly = true)
    public List<Transaction> findAllWithErrors(
            TransactionFilters transactionFilters,
            TransactionSortFilters transactionSortFilters,
            PageFilters pageFilters)
            throws EntityErrorsException {
        JPAQuery<TransactionWithErrorDTO> query = new JPAQueryFactory(entityManager)
                .select(Projections.fields(TransactionWithErrorDTO.class,
                        root.id.as("id"),
                        root.type.as("type"),
                        root.currency.as("currency"),
                        root.valueDate.as("valueDate"),
                        root.transactionDate.as("transactionDate"),
                        root.statementDate.as("statementDate"),
                        root.direction.as("direction"),
                        root.amount.as("amount"),
                        root.reference.as("reference"),
                        root.description.as("description"),
                        root.accountStatement.as("accountStatement"),
                        root.remittanceInformation.as("remittanceInformation"),
                        root.orderingPartyName.as("orderingPartyName"),
                        root.createdAt.as("createdAt"),
                        root.createdBy.as("createdBy"),
                        root.updatedAt.as("updatedAt"),
                        root.updatedBy.as("updatedBy"),
                        root.partitionKey.as("partitionKey"),
                        root.reconcileStatus.as("reconcileStatus"),
                        root.accountStatement.status.as("statementIsInError")))
                .from(root);

        buildWhereClauses(transactionFilters, query);
        applySort(transactionSortFilters, query, root, Transaction.SortingFields.class);
        applyPagination(pageFilters, query);

        return query.fetch().stream()
                .map(TransactionWithErrorDTO::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<Transaction> findLastTransactionDate(TransactionFilters transactionFilters) {
        return jpaTransactionRepository.findFirstByAccountStatementStatementIdOrderByTransactionDateDesc(transactionFilters.getAccountStatementID())
                .map(TransactionPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Long> findAllIds(TransactionFilters transactionFilters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id)
                .from(root);

        buildWhereClauses(transactionFilters, query);

        return query.fetch();
    }


    @Override
    @Transactional(readOnly = true)
    public TransactionGroup findTransactionGroups(TransactionFilters transactionFilters, TransactionGroupFilters transactionGroupFilters) {

        List<String> groupingFields = transactionGroupFilters.getFields().stream()
                .filter(Transaction.GroupingFields::isBelongsToMainEntity).map(fields -> fields.groupField).toList();

        List<String> reconciliationGroupingFields = transactionGroupFilters.getFields().stream()
                .filter(transaction -> !transaction.isBelongsToMainEntity()).map(fields -> fields.groupField).toList();

        JPAQuery<TransactionWithReconciliationDTO> query = new JPAQueryFactory(entityManager)
                .select(Projections.fields(TransactionWithReconciliationDTO.class,
                        root.id.as("id"),
                        root.type.as("type"),
                        root.currency.as("currency"),
                        root.valueDate.as("valueDate"),
                        root.transactionDate.as("transactionDate"),
                        root.statementDate.as("statementDate"),
                        root.direction.as("direction"),
                        root.amount.as("amount"),
                        root.reference.as("reference"),
                        root.description.as("description"),
                        root.accountStatement.as("accountStatement"),
                        root.remittanceInformation.as("remittanceInformation"),
                        root.orderingPartyName.as("orderingPartyName"),
                        root.createdAt.as("createdAt"),
                        root.createdBy.as("createdBy"),
                        root.updatedAt.as("updatedAt"),
                        root.updatedBy.as("updatedBy"),
                        root.partitionKey.as("partitionKey"),
                        root.reconcileStatus.as("reconcileStatus"),
                        reconciliationTransaction.reconciliation.id.as("reconciliationId"),
                        reconciliationTransaction.reconciliation.creator.as("reconciliationCreator"),
                        reconciliationTransaction.reconciliation.creationDate.as("reconciliationCreationDate"),
                        reconciliationTransaction.reconciliation.reviewer.as("reconciliationReviewer"),
                        reconciliationTransaction.reconciliation.reviewDate.as("reconciliationReviewDate"),
                        reconciliationTransaction.reconciliation.status.as("reconciliationStatus")))
                .from(root)
                .leftJoin(reconciliationTransaction).on(reconciliationTransaction.transactionPsql.id.eq(root.id))
                .leftJoin(reconciliationTransaction.reconciliation).on(reconciliationTransaction.reconciliation.id
                        .eq(reconciliationTransaction.reconciliation.id));

        applyGroup(groupingFields, query, root);
        if (!reconciliationGroupingFields.isEmpty()) {
            applyGroup(reconciliationGroupingFields, query, reconciliationTransaction.reconciliation);
        }
        buildWhereClauses(transactionFilters, query);

        String queryStr = query.fetch().toString();

        return new TransactionGroup(queryStr, transactionGroupFilters.getFields());
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getTotalAmountOfTransactions(Long accountID, List<Long> transactionIds) {
        int batchSize = 2000;
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (int start = 0; start < transactionIds.size(); start += batchSize) {
            int end = Math.min(transactionIds.size(), start + batchSize);
            List<Long> chunk = new ArrayList<>(transactionIds.subList(start, end));

            BigDecimal chunkTotal = jpaTransactionRepository.getTotalAmountOfTransactions(accountID, chunk);

            if (chunkTotal != null) {
                totalAmount = totalAmount.add(chunkTotal);
            }
        }
        return totalAmount;
    }

    @Override
    public void deleteById(Long id) {
        jpaTransactionRepository.deleteById(id);
    }

    @Override
    @Transactional
    public void deleteByAccountStatementId(Long statementId) {

        jpaFxRateTransactionRepository.deleteAllByTransactionAccountStatementId(statementId);
        jpaTransactionRepository.deleteAllByAccountStatementId(statementId);
    }

    @Override
    @Transactional(readOnly = true)
    public GroupInfo fetchGroupInfo(TransactionFilters transactionFilters) {
        JPAQuery<GroupInfo> query = new JPAQueryFactory(entityManager)
                .select(Projections.bean(GroupInfo.class,
                        root.amount.sum().as("totalBalance"),
                        root.id.count().as("totalQuantity"),
                        root.reconcileStatus.countDistinct().as("statusAmount"),
                        root.reconcileStatus.min().as("status")
                ))
                .from(root);

        buildWhereClauses(transactionFilters, query);

        GroupInfo groupInfo = query.fetchOne();

        if (Objects.isNull(groupInfo)) {
            return GroupInfo.builder().build();
        }

        groupInfo = groupInfo.toBuilder()
                .allIds(findAllIds(transactionFilters))
                .build();

        //"Avoid using Literals in Conditional Statements" - PMD... thanks pmd
        int maxSizeForSharedStatus = 1;

        if (groupInfo.getStatusAmount() == maxSizeForSharedStatus) {
            groupInfo.setIsStatusShared(true);
            groupInfo.setIsReconciliationShared(
                    groupInfo.getStatus().equals(ReconcileStatus.NOT_RECONCILED.name()) ||
                            isReconciliationSharedByTransactions(transactionFilters)
            );
        } else {
            groupInfo.setIsStatusShared(false);
            groupInfo.setIsReconciliationShared(false);
        }

        return groupInfo;
    }

    @Override
    public Boolean isReconciliationSharedByTransactions(TransactionFilters transactionFilters) {
        JPAQuery<Integer> query = new JPAQueryFactory(entityManager)
                .select(reconciliationTransaction.reconciliation.id)
                .from(root)
                .leftJoin(reconciliationTransaction).on(reconciliationTransaction.transactionPsql.id.eq(root.id))
                .distinct();

        buildWhereClauses(transactionFilters, query);

        return query.fetch().size() <= 1;
    }

    private Transaction fetch(TransactionPsql transactionPsql) {
        Set<FxRate> fxRates = jpaFxRateTransactionRepository.findByTransaction(transactionPsql).stream()
                .map(FxRateTransactionPsql::getFxRate)
                .map(FxRatePsql::toEntity)
                .collect(Collectors.toSet());

        return transactionPsql.toEntity().toBuilder().fxRates(fxRates).build();
    }

    private Transaction fetch(TransactionWithReconciliationDTO transactionWithReconciliationDTO) {
        Set<FxRate> fxRates = jpaFxRateTransactionRepository.findByTransactionId(transactionWithReconciliationDTO.toEntity().getId()).stream()
                .map(FxRateTransactionPsql::getFxRate)
                .map(FxRatePsql::toEntity)
                .collect(Collectors.toSet());

        return transactionWithReconciliationDTO.toEntity().toBuilder().fxRates(fxRates).build();
    }

    @Override
    @Transactional(readOnly = true)
    public long count(TransactionFilters filters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(filters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    @Override
    public boolean areTransactionsAvailableToReconcile(List<Long> transactionIds) {
        //"Avoid using Literals in Conditional Statements" - PMD... thanks pmd
        int maxSizeToReconcile = 0;

        return jpaTransactionRepository.areTransactionsAvailable(transactionIds).compareTo(transactionIds.size()) == maxSizeToReconcile;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Transaction> findTransactionsWithoutFxRate(FxRate fxRate) {
        Currency quoteCurrency = fxRate.getQuoteCurrency();

        if (quoteCurrency.getCode().equals("USD")) {
            return findTransactionWithoutFxRateUsd(fxRate);
        }
        return findTransactionWithoutFxRateLcy(fxRate);
    }

    @SuppressWarnings("PMD.AvoidDuplicateLiterals")
    private List<Transaction> findTransactionWithoutFxRateUsd(FxRate fxRate) {
        LocalDate rateDate = fxRate.getRateDate();
        Long baseCurrencyId = fxRate.getBaseCurrency().getId();

        String sql = "SELECT t.* " +
                "FROM transactions t " +
                "LEFT JOIN ( " +
                "    SELECT DISTINCT frt.transaction_id, fxr.id as fx_rate_id, fxr.\"Rate_Date\", c.code as quote_currency_code " +
                "    FROM fx_rates_transaction frt " +
                "    LEFT JOIN fx_rates fxr ON frt.fx_rates_id = fxr.id " +
                "    LEFT JOIN currency c ON fxr.\"Quote_Currency\" = c.id " +
                "    WHERE fxr.id IS NOT NULL " +
                "    UNION ALL " +
                "    SELECT DISTINCT t.id as transaction_id, fxr.id as fx_rate_id, fxr.\"Rate_Date\", c.code as quote_currency_code " +
                "    FROM transactions t " +
                "    LEFT JOIN bank_statement bs ON t.bank_statement = bs.id " +
                "    LEFT JOIN fx_rates_bank_statement frs ON bs.id = frs.bank_statement_id " +
                "    LEFT JOIN fx_rates fxr ON frs.fx_rates_id = fxr.id " +
                "    LEFT JOIN currency c ON fxr.\"Quote_Currency\" = c.id " +
                "    WHERE fxr.id IS NOT NULL " +
                ") all_fx_rates ON t.id = all_fx_rates.transaction_id " +
                "WHERE t.value_date = :rateDate AND " +
                "t.currency_id = :baseCurrencyId AND ( " +
                "all_fx_rates.quote_currency_code != 'USD' OR " +
                "all_fx_rates.quote_currency_code IS NULL) ";

        Query query = entityManager.createNativeQuery(sql, TransactionPsql.class);
        query.setParameter("rateDate", rateDate);
        query.setParameter("baseCurrencyId", baseCurrencyId);

        List<TransactionPsql> transactions = query.getResultList();
        return transactions.stream()
                .map(TransactionPsql::toEntity)
                .collect(Collectors.toList());

    }

    @SuppressWarnings("PMD.AvoidDuplicateLiterals")
    private List<Transaction> findTransactionWithoutFxRateLcy(FxRate fxRate) {
        LocalDate rateDate = fxRate.getRateDate();
        Long baseCurrencyId = fxRate.getBaseCurrency().getId();

        String sql = "SELECT t.* " +
                "FROM transactions t " +
                "LEFT JOIN ( " +
                "    SELECT DISTINCT frt.transaction_id, fxr.id as fx_rate_id, fxr.\"Rate_Date\", c.code as quote_currency_code " +
                "    FROM fx_rates_transaction frt " +
                "    LEFT JOIN fx_rates fxr ON frt.fx_rates_id = fxr.id " +
                "    LEFT JOIN currency c ON fxr.\"Quote_Currency\" = c.id " +
                "    WHERE fxr.id IS NOT NULL " +
                "    UNION ALL " +
                "    SELECT DISTINCT  t.id as transaction_id, fxr.id as fx_rate_id, fxr.\"Rate_Date\", c.code as quote_currency_code " +
                "    FROM transactions t " +
                "    LEFT JOIN bank_statement bs ON t.bank_statement = bs.id " +
                "    LEFT JOIN bank_account ba ON bs.bank_account_id = ba.id " +
                "    LEFT JOIN country co ON ba.fk_country = co.id " +
                "    LEFT JOIN currency local_currency ON co.currency_id = local_currency.id " +
                "    LEFT JOIN fx_rates_bank_statement frs ON bs.id = frs.bank_statement_id " +
                "    LEFT JOIN fx_rates fxr ON frs.fx_rates_id = fxr.id " +
                "    LEFT JOIN currency c ON fxr.\"Quote_Currency\" = c.id " +
                "    WHERE fxr.id IS NOT NULL " +
                ") all_fx_rates ON t.id = all_fx_rates.transaction_id " +
                "LEFT JOIN bank_statement bs ON t.bank_statement = bs.id " +
                "LEFT JOIN bank_account ba ON bs.bank_account_id = ba.id " +
                "LEFT JOIN country co ON ba.fk_country = co.id " +
                "LEFT JOIN currency local_currency ON co.currency_id = local_currency.id " +
                "WHERE t.value_date = :rateDate AND " +
                "t.currency_id = :baseCurrencyId AND ( " +
                "all_fx_rates.quote_currency_code != local_currency.code OR " +
                "all_fx_rates.quote_currency_code IS NULL) ";

        Query query = entityManager.createNativeQuery(sql, TransactionPsql.class);
        query.setParameter("rateDate", rateDate);
        query.setParameter("baseCurrencyId", baseCurrencyId);

        List<TransactionPsql> transactions = query.getResultList();

        return transactions.stream().map(TransactionPsql::toEntity).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Optional<Transaction> findLastTransactionWithTimestampRunAt() {
        return jpaTransactionRepository.findFirstByOrderByTimestampRunAtDesc().map(TransactionPsql::toEntity);
    }

    private void buildWhereClauses(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (Objects.isNull(transactionFilters)) {
            return;
        }
        filterByPartitionKey(transactionFilters, query);

        filterByFilterText(transactionFilters, query);
        filterByType(transactionFilters, query);
        filterByAccountId(transactionFilters, query);
        filterByCurrency(transactionFilters, query);
        filterByValueDate(transactionFilters, query);
        filterByTransactionDate(transactionFilters, query);
        filterByStatementDate(transactionFilters, query);
        filterByDirection(transactionFilters, query);
        filterByAmount(transactionFilters, query);
        filterByReference(transactionFilters, query);
        filterByDescription(transactionFilters, query);
        filterByAccountStatementID(transactionFilters, query);
        filterByRemittanceInformation(transactionFilters, query);
        filterByOrderingPartyName(transactionFilters, query);
        filterByCreatedAt(transactionFilters, query);
        filterByReconciliationId(transactionFilters, query);
        filterByReconciliationCreator(transactionFilters, query);
        filterByReconciliationCreationDate(transactionFilters, query);
        filterByReconciliationReviewer(transactionFilters, query);
        filterByReconciliationReviewDate(transactionFilters, query);
        filterByReconciliationStatus(transactionFilters, query);
        filterByImportedStatementOnly(transactionFilters, query);


    }

    private void filterByPartitionKey(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getPartitionKey())) {
            query.where(root.partitionKey.eq(transactionFilters.getPartitionKey()));
        }
    }

    private void filterByFilterText(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getFilterText())) {
            query.where(root.reference.likeIgnoreCase("%" + transactionFilters.getFilterText() + "%")
                    .or(root.description.likeIgnoreCase("%" + transactionFilters.getFilterText() + "%")));
        }
    }

    private void filterByType(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getType())) {
            if (!transactionFilters.isExactFilters()) {
                query.where(root.type.likeIgnoreCase("%" + (transactionFilters.getType()) + "%"));
            } else {
                query.where(root.type.eq(transactionFilters.getType()));
            }
        }
    }

    private void filterByAccountId(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getAccountId())) {
            if (!transactionFilters.isExactFilters()) {
                query.where(root.accountStatement.account.accountNumber
                        .likeIgnoreCase("%" + (transactionFilters.getAccountId()) + "%"));
            } else {
                query.where(root.accountStatement.account.accountNumber.eq(transactionFilters.getAccountId()));
            }
        }
    }

    private void filterByCurrency(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getCurrency())) {
            query.where(root.currency.code.in(transactionFilters.getCurrency()));
        }
    }

    private void filterByValueDate(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getValueDateStart())) {
            LocalDate startDate = transactionFilters.getValueDateStart();
            LocalDate endDate = Objects.isNull(transactionFilters.getValueDateEnd()) ? startDate : transactionFilters.getValueDateEnd();
            query.where(root.valueDate.between(startDate, endDate));
        }
    }

    private void filterByTransactionDate(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getTransactionDateStart())) {
            LocalDate startDate = transactionFilters.getTransactionDateStart();
            LocalDate endDate = Objects.isNull(transactionFilters.getTransactionDateEnd()) ? startDate : transactionFilters.getTransactionDateEnd();
            query.where(root.transactionDate.between(startDate, endDate));
        }
    }

    private void filterByStatementDate(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getStatementDateStart())) {
            LocalDate startDate = transactionFilters.getStatementDateStart();
            LocalDate endDate = Objects.isNull(transactionFilters.getStatementDateEnd()) ? startDate : transactionFilters.getStatementDateEnd();
            query.where(root.statementDate.between(startDate, endDate));
        }
    }

    private void filterByDirection(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getDirection())) {
            List<Integer> directions = transactionFilters.getDirection().stream()
                    .map(Direction::valueOf)
                    .map(Direction::getValue)
                    .collect(Collectors.toList());
            query.where(root.direction.in(directions));
        }
    }

    private void filterByAmount(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getAmount())) {
            query.where(root.amount.in(transactionFilters.getAmount()));
        }
    }

    private void filterByReference(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getReference())) {
            if (!transactionFilters.isExactFilters()) {
                query.where(root.reference.likeIgnoreCase("%" + (transactionFilters.getReference()) + "%"));
            } else {
                query.where(root.reference.eq(transactionFilters.getReference()));
            }
        }
    }

    private void filterByDescription(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getDescription())) {
            if (!transactionFilters.isExactFilters()) {
                query.where(root.description.likeIgnoreCase("%" + (transactionFilters.getDescription()) + "%"));
            } else {
                query.where(root.description.eq(transactionFilters.getDescription()));
            }
        }
    }

    private void filterByAccountStatementID(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getAccountStatementID())) {
            query.where(root.accountStatement.id.eq(Long.valueOf(transactionFilters.getAccountStatementID())));
        }
    }

    private void filterByRemittanceInformation(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getRemittanceInformation())) {
            if (!transactionFilters.isExactFilters()) {
                query.where(root.remittanceInformation.likeIgnoreCase("%" + (transactionFilters.getRemittanceInformation()) + "%"));
            } else {
                query.where(root.remittanceInformation.eq(transactionFilters.getRemittanceInformation()));
            }
        }
    }

    private void filterByOrderingPartyName(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getOrderingPartyName())) {
            if (!transactionFilters.isExactFilters()) {
                query.where(root.orderingPartyName.likeIgnoreCase("%" + (transactionFilters.getOrderingPartyName()) + "%"));
            } else {
                query.where(root.orderingPartyName.eq(transactionFilters.getOrderingPartyName()));
            }
        }
    }

    public void filterByCreatedAt(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (!Objects.isNull(transactionFilters.getCreatedAtStart())) {
            LocalDateTime startDate = transactionFilters.getCreatedAtStart().with(LocalTime.MIN);
            LocalDateTime endDate = Objects.isNull(transactionFilters.getCreatedAtEnd()) ? startDate.with(LocalTime.MAX) :
                    transactionFilters.getCreatedAtEnd().with(LocalTime.MAX);
            query.where(root.createdAt.between(startDate, endDate));
        }
    }

    private void filterByReconciliationId(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (Objects.nonNull(transactionFilters.getReconciliationId())) {
            query.leftJoin(reconciliationTransaction).on(reconciliationTransaction.transactionPsql.id.eq(root.id));
            query.where(reconciliationTransaction.reconciliation.id.eq(transactionFilters.getReconciliationId()));
        }
    }

    private void filterByReconciliationCreator(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (Objects.nonNull(transactionFilters.getReconciliationCreator())) {
            query.leftJoin(reconciliationTransaction).on(reconciliationTransaction.transactionPsql.id.eq(root.id))
                    .leftJoin(reconciliationTransaction.reconciliation)
                    .on(reconciliationTransaction.reconciliation.id.eq(reconciliationTransaction.reconciliation.id));

            if (!transactionFilters.isExactFilters()) {
                query.where(reconciliationTransaction.reconciliation.creator
                        .likeIgnoreCase("%" + transactionFilters.getReconciliationCreator() + "%"));
            } else {
                query.where(reconciliationTransaction.reconciliation.creator.eq(transactionFilters.getReconciliationCreator()));
            }
        }
    }

    private void filterByReconciliationCreationDate(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (Objects.nonNull(transactionFilters.getReconciliationCreationDateStart())) {
            LocalDateTime startDate = transactionFilters.getReconciliationCreationDateStart().atStartOfDay();
            LocalDateTime endDate = Objects.isNull(transactionFilters.getReconciliationCreationDateEnd())
                    ? startDate.with(LocalTime.MAX)
                    : transactionFilters.getReconciliationCreationDateEnd().atTime(23, 59, 59);

            query.leftJoin(reconciliationTransaction).on(reconciliationTransaction.transactionPsql.id.eq(root.id))
                    .leftJoin(reconciliationTransaction.reconciliation)
                    .on(reconciliationTransaction.reconciliation.id.eq(reconciliationTransaction.reconciliation.id));

            query.where(reconciliationTransaction.reconciliation.creationDate.between(startDate, endDate));
        }
    }

    private void filterByReconciliationReviewer(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (Objects.nonNull(transactionFilters.getReconciliationReviewer())) {
            query.leftJoin(reconciliationTransaction).on(reconciliationTransaction.transactionPsql.id.eq(root.id))
                    .leftJoin(reconciliationTransaction.reconciliation)
                    .on(reconciliationTransaction.reconciliation.id.eq(reconciliationTransaction.reconciliation.id));

            if (!transactionFilters.isExactFilters()) {
                query.where(reconciliationTransaction.reconciliation.reviewer
                        .likeIgnoreCase("%" + transactionFilters.getReconciliationReviewer() + "%"));
            } else {
                query.where(reconciliationTransaction.reconciliation.reviewer.eq(transactionFilters.getReconciliationReviewer()));
            }
        }
    }

    private void filterByReconciliationReviewDate(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (Objects.nonNull(transactionFilters.getReconciliationReviewDateStart())) {
            LocalDateTime startDate = transactionFilters.getReconciliationReviewDateStart().atStartOfDay();
            LocalDateTime endDate = Objects.isNull(transactionFilters.getReconciliationReviewDateEnd())
                    ? startDate.with(LocalTime.MAX)
                    : transactionFilters.getReconciliationReviewDateEnd().atTime(23, 59, 59);

            query.leftJoin(reconciliationTransaction).on(reconciliationTransaction.transactionPsql.id.eq(root.id))
                    .leftJoin(reconciliationTransaction.reconciliation)
                    .on(reconciliationTransaction.reconciliation.id.eq(reconciliationTransaction.reconciliation.id));

            query.where(reconciliationTransaction.reconciliation.reviewDate.between(startDate, endDate));
        }
    }

    private void filterByReconciliationStatus(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (Objects.nonNull(transactionFilters.getReconciliationStatus())) {
            query.leftJoin(reconciliationTransaction).on(reconciliationTransaction.transactionPsql.id.eq(root.id))
                    .leftJoin(reconciliationTransaction.reconciliation)
                    .on(reconciliationTransaction.reconciliation.id.eq(reconciliationTransaction.reconciliation.id));

            query.where(reconciliationTransaction.reconciliation.status.in(transactionFilters.getReconciliationStatus()));
        }
    }

    private void filterByImportedStatementOnly(TransactionFilters transactionFilters, JPAQuery<?> query) {
        if (transactionFilters.isImportedStatementOnly()) {
            query.where(root.accountStatement.status.eq(AccountStatementStatus.IMPORTED));
        }
    }

    //for testing purposes

    @Transactional
    @Override
    public void deleteAllFxRatesTransactions() {
        jpaFxRateTransactionRepository.deleteAll();
    }


}
