package pt.jumia.services.brad.data.brad.entities.audit;


import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class DefaultAudIdPsql implements Serializable {

    @Serial
    private static final long serialVersionUID = 4107776899765126790L;

    protected long id;
    protected int rev;

}
