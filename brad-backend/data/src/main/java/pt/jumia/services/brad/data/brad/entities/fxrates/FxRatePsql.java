package pt.jumia.services.brad.data.brad.entities.fxrates;

import jakarta.persistence.*;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.data.brad.entities.CurrencyPsql;
import pt.jumia.services.brad.data.shared.BaseFxRates;
import pt.jumia.services.brad.domain.entities.FxRate;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;


@Entity
@Table(name = "FX_RATES")
@NoArgsConstructor
public class FxRatePsql extends BaseFxRates implements Serializable {

    @Serial
    private static final long serialVersionUID = 3168762675378L;

    @Id
    @SequenceGenerator(name = "fx_rates_sequence_id_seq", sequenceName = "fx_rates_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "fx_rates_sequence_id_seq")
    @Column(name = "id")
    private Integer id;

    @JoinColumn(name = "`Base_Currency`")
    @ManyToOne
    protected CurrencyPsql baseCurrency;

    @JoinColumn(name = "`Quote_Currency`")
    @ManyToOne
    protected CurrencyPsql quoteCurrency;

    @Column(name = "`Rate_Date`")
    protected LocalDate rateDate;

    public FxRatePsql(FxRate fxRate) {
        super(fxRate);
        this.baseCurrency = new CurrencyPsql(fxRate.getBaseCurrency());
        this.quoteCurrency = new CurrencyPsql(fxRate.getQuoteCurrency());
        this.rateDate = fxRate.getRateDate();
    }

    @Override
    public FxRate toEntity(){
        FxRate fxRate = super.toEntity();

        return FxRate.builder()
                .id(id)
                .baseCurrency(baseCurrency.toEntity())
                .quoteCurrency(quoteCurrency.toEntity())
                .rateDate(rateDate)
                .bid(fxRate.getBid())
                .bisLoadedAt(fxRate.getBisLoadedAt())
                .skAudInsert(fxRate.getSkAudInsert())
                .skAudUpdate(fxRate.getSkAudUpdate())
                .timestampLastUpdate(fxRate.getTimestampLastUpdate())
                .build();
    }

}
