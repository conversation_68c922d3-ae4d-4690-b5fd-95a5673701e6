package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.PartitionKey;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import pt.jumia.services.brad.data.brad.entities.fxrates.statement.FxRateStatementPsql;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.enumerations.AccountStatementFlow;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Setter
@Entity
@Audited
@Table(name = "bank_statement")
@NoArgsConstructor
public class AccountStatementPsql extends BaseEntityFieldMap<AccountStatement.SortingFields> implements Serializable {

    @Serial
    private static final long serialVersionUID = 84564727854362L;

    @Id
    @SequenceGenerator(name = "bank_statement_id_seq", sequenceName = "bank_statement_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "bank_statement_id_seq")
    @Column(name = "ID")
    Long id;

    @ManyToOne(optional = false)
    CurrencyPsql currency;

    @Column(name = "STATEMENT_ID")
    String statementId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PREVIOUS_STATEMENT_ID")
    private AccountStatementPsql previousStatement;

    @Column(name = "INITIAL_DATE")
    private LocalDate initialDate;
    @Column(name = "FINAL_DATE")
    private LocalDate finalDate;

    @Column(name = "INITIAL_DIRECTION")
    private String initialDirection;

    @Column(name = "FINAL_DIRECTION")
    private String finalDirection;

    @Column(name = "INITIAL_AMOUNT")
    private BigDecimal initialAmount;

    @Column(name = "FINAL_AMOUNT")
    private BigDecimal finalAmount;

    @Getter
    @NotAudited
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "statement", cascade = CascadeType.ALL)
    private Set<FxRateStatementPsql> fxRateStatementSet;

    @Column(name = "STATUS")
    @Enumerated(EnumType.STRING)
    private AccountStatementStatus status;

    @Column(name = "STATUS_DESCRIPTION")
    @Enumerated(EnumType.STRING)
    private AccountStatementStatus.Description statusDescription;

    @Column(name = "DESCRIPTION", nullable = false)
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bank_account_id", nullable = false)
    private AccountPsql account;

    @Column(name = "FLOW")
    private AccountStatementFlow accountStatementFlow;

    @Column(name = "CREATED_BY", nullable = false)
    private String createdBy;

    @CreationTimestamp
    @Column(name = "CREATED_AT", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "UPDATED_BY", nullable = false)
    private String updatedBy;

    @UpdateTimestamp
    @Column(name = "UPDATED_AT", nullable = false)
    private LocalDateTime updatedAt;

    @Getter
    @PartitionKey
    @Column(name = "PARTITION_KEY", nullable = false)
    private String partitionKey;

    public AccountStatementPsql(AccountStatement accountStatement) {

        this.id = accountStatement.getId();
        this.currency = new CurrencyPsql(accountStatement.getCurrency());
        this.statementId = accountStatement.getStatementId();
        this.previousStatement = accountStatement.getPreviousStatement() == null ? null :
                AccountStatementPsql.fromEntity(accountStatement.getPreviousStatement());
        this.initialDate = accountStatement.getInitialDate();
        this.finalDate = accountStatement.getFinalDate();
        this.initialDirection = accountStatement.getInitialDirection().toString();
        this.finalDirection = accountStatement.getFinalDirection().toString();
        this.initialAmount = accountStatement.getInitialAmount();
        this.finalAmount = accountStatement.getFinalAmount();
        this.status = accountStatement.getStatus();
        this.statusDescription = accountStatement.getStatusDescription();
        this.description = accountStatement.getDescription();
        this.account = new AccountPsql(accountStatement.getAccount());
        this.accountStatementFlow = accountStatement.getFlow();
        this.createdBy = Objects.nonNull(accountStatement.getCreatedBy()) ? accountStatement.getCreatedBy() : RequestContext.getUsername();
        this.updatedBy = Objects.nonNull(RequestContext.getUsername()) ? RequestContext.getUsername() : accountStatement.getUpdatedBy();
        this.createdAt = Objects.isNull(accountStatement.getCreatedAt()) ? LocalDateTime.now()
            : accountStatement.getCreatedAt();
        this.updatedAt = Objects.isNull(accountStatement.getUpdatedAt()) ? LocalDateTime.now()
            : accountStatement.getUpdatedAt();
        this.partitionKey = String.valueOf(accountStatement.getAccount().getId());
    }

    public AccountStatement toEntity() {

        Set<FxRate> fxRates = new HashSet<>();
        if (Objects.nonNull(this.fxRateStatementSet)) {
            fxRates = this.fxRateStatementSet.stream()
                .map(fxRateStatementPsql -> fxRateStatementPsql.getFxRate().toEntity())
                .collect(Collectors.toSet());
        }

        return AccountStatement.builder()
            .id(this.id)
            .currency(this.currency.toEntity())
            .statementId(this.statementId)
            .previousStatement(this.previousStatement == null ? null : this.previousStatement.toEntityWithoutPreviousStatement())
            .initialDate(this.initialDate)
            .finalDate(this.finalDate)
            .initialDirection(Direction.valueOf(this.initialDirection))
            .finalDirection(Direction.valueOf(this.finalDirection))
            .initialAmount(this.initialAmount)
            .finalAmount(this.finalAmount)
            .fxRates(fxRates)
            .status(this.status)
            .statusDescription(this.statusDescription)
            .description(this.description)
            .account(this.account.toEntity())
            .flow(this.accountStatementFlow)
            .createdBy(this.createdBy)
            .createdAt(this.createdAt)
            .updatedBy(this.updatedBy)
            .updatedAt(this.updatedAt)
            .build();
    }

    public AccountStatement toEntityWithoutPreviousStatement() {

        return AccountStatement.builder()
            .id(this.id)
            .currency(this.currency.toEntity())
            .statementId(this.statementId)
            .initialDate(this.initialDate)
            .finalDate(this.finalDate)
            .initialDirection(Direction.valueOf(this.initialDirection))
            .finalDirection(Direction.valueOf(this.finalDirection))
            .initialAmount(this.initialAmount)
            .finalAmount(this.finalAmount)
            .status(this.status)
            .statusDescription(this.statusDescription)
            .description(this.description)
            .account(this.account.toEntity())
            .flow(this.accountStatementFlow)
            .createdBy(this.createdBy)
            .createdAt(this.createdAt)
            .updatedBy(this.updatedBy)
            .updatedAt(this.updatedAt)
            .build();
    }


    public static AccountStatementPsql fromEntity(AccountStatement accountStatement) {

        return new AccountStatementPsql(accountStatement);
    }


    static {
        Map<AccountStatement.SortingFields, String> entityFields = Map.ofEntries(
                Map.entry(AccountStatement.SortingFields.ID, "id"),
                Map.entry(AccountStatement.SortingFields.CURRENCY, "currency"),
                Map.entry(AccountStatement.SortingFields.STATEMENT_ID, "statementId"),
                Map.entry(AccountStatement.SortingFields.PREVIOUS_STATEMENT_ID, "previousStatement"),
                Map.entry(AccountStatement.SortingFields.INITIAL_DATE, "initialDate"),
                Map.entry(AccountStatement.SortingFields.FINAL_DATE, "finalDate"),
                Map.entry(AccountStatement.SortingFields.INITIAL_DIRECTION, "initialDirection"),
                Map.entry(AccountStatement.SortingFields.FINAL_DIRECTION, "finalDirection"),
                Map.entry(AccountStatement.SortingFields.INITIAL_AMOUNT, "initialAmount"),
                Map.entry(AccountStatement.SortingFields.FINAL_AMOUNT, "finalAmount"),
                Map.entry(AccountStatement.SortingFields.STATUS, "status"),
                Map.entry(AccountStatement.SortingFields.STATUS_DESCRIPTION, "statusDescription"),
                Map.entry(AccountStatement.SortingFields.DESCRIPTION, "description"),
                Map.entry(AccountStatement.SortingFields.FLOW, "flow"),
                Map.entry(AccountStatement.SortingFields.CREATED_AT, "createdAt"),
                Map.entry(AccountStatement.SortingFields.CREATED_BY, "createdBy"),
                Map.entry(AccountStatement.SortingFields.UPDATED_AT, "updatedAt"),
                Map.entry(AccountStatement.SortingFields.UPDATED_BY, "updatedBy")
        );

        addEntityFieldMap(AccountStatement.SortingFields.class, entityFields);
    }
}
