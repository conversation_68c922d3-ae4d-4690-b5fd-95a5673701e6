package pt.jumia.services.brad.data.shared;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import pt.jumia.services.brad.data.brad.repository.psql.PsqlExecutionLogRepository;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.usecases.FindExceptionRootCauseUseCase;

import java.time.LocalDateTime;

@Slf4j
@NoArgsConstructor
public abstract class BaseMssqlRepository {


    protected EntityManagerConfiguration entityManagerConfiguration;
    protected PsqlExecutionLogRepository psqlExecutionLogRepository;

    @SuppressWarnings({"PMD"})
    @SuppressFBWarnings("EI_EXPOSE_REP2")
    public BaseMssqlRepository(EntityManagerConfiguration entityManagerConfiguration,
                               PsqlExecutionLogRepository psqlExecutionLogRepository) {
        this.entityManagerConfiguration = entityManagerConfiguration;
        this.psqlExecutionLogRepository = psqlExecutionLogRepository;
    }


    public void refresh() {
        entityManagerConfiguration.refresh();
    }

    protected void handleException(Exception e, boolean retrying, ViewEntity viewEntity, ExecutionLog executionLog)
            throws DatabaseErrorsException {
        Throwable ex = FindExceptionRootCauseUseCase.findRootCause(e);
        this.logExecutionFailure(ex.getMessage(), executionLog);
        log.error(ex.getMessage());
        if (!retrying && ex.getMessage().contains("Cannot determine target DataSource for lookup key")) {
            entityManagerConfiguration.refresh();
            return;
        }
        throw DatabaseErrorsException.createDatabaseUnavailable(viewEntity.getDatabaseName());
    }

    protected void logExecution(LocalDateTime startTime,
                              LocalDateTime endTime,
                              int recordsAmount,
                              String appliedFilterString,
                              String query,
                              ExecutionLog executionLog) {
        executionLog = executionLog.toBuilder()
                .recordsAmount(executionLog.getRecordsAmount() == null ? recordsAmount : executionLog.getRecordsAmount() + recordsAmount)
                .executionStartTime(executionLog.getExecutionStartTime() == null ? startTime : executionLog.getExecutionStartTime())
                .executionEndTime(endTime)
                .appliedFilters(appliedFilterString)
                .query(query)
                .build();
        try {
            psqlExecutionLogRepository.upsert(executionLog);
        } catch (Exception e) {
            log.error("Error inserting executionLog: {}", e.getMessage());
        }
    }

    protected void logExecutionFailure(String error, ExecutionLog executionLog) {
        try {
            executionLog.getErrors()
                    .add(new ExecutionLog.SyncingError(error));

            psqlExecutionLogRepository.upsert(executionLog.toBuilder().logStatus(ExecutionLog.ExecutionLogStatus.ERROR).build());
        } catch (Exception e) {
            log.error("Error inserting executionLog: {}", e.getMessage());
        }
    }
}
