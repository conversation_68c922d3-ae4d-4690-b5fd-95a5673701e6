package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import pt.jumia.services.brad.data.brad.entities.CurrencyPsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.FxRatePsql;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface JpaFxRateRepository extends JpaRepository<FxRatePsql, Integer>, JpaSpecificationExecutor<FxRatePsql> {

    Optional<FxRatePsql> findFirstByOrderByTimestampLastUpdateDesc();

    List<FxRatePsql> findAllByRateDateAndBaseCurrency(LocalDate rateDate, CurrencyPsql baseCurrency);

    Optional<FxRatePsql> findByRateDateAndBaseCurrencyAndQuoteCurrency(LocalDate rateDate, CurrencyPsql baseCurrency, CurrencyPsql quoteCurrency);

}
