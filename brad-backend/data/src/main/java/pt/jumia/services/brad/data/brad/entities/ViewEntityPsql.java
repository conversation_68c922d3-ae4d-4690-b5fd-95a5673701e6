package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.*;
import lombok.NoArgsConstructor;
import lombok.Setter;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.ViewEntity;

import java.time.LocalDateTime;

@Setter
@Entity
@NoArgsConstructor
@Table(name = "view_entity")
public class ViewEntityPsql {

    @Id
    @SequenceGenerator(name = "view_entity_sequence_id_seq", sequenceName = "view_entity_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "view_entity_sequence_id_seq")
    @Column(name = "id")
    private Long id;
    
    @Column(name = "entity_type")
    private String entityType;

    @Column(name = "driver")
    private String driver;

    @Column(name = "jdbc_connection_url", nullable = false)
    private String jdbcConnectionUrl;

    @Column(name = "database_name", nullable = false)
    private String databaseName;

    @Column(name = "schema_name", nullable = false)
    private String schemaName;

    @Column(name = "view_name", nullable = false)
    private String viewName;

    @Column(name = "CREATED_AT", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "CREATED_BY", nullable = false)
    private String createdBy;

    @Column(name = "UPDATED_AT", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "UPDATED_BY", nullable = false)
    private String updatedBy;



    public ViewEntityPsql(ViewEntity viewEntity) {
        this.id = viewEntity.getId();
        this.entityType = viewEntity.getEntityType().name();
        this.driver = viewEntity.getDriver();
        this.jdbcConnectionUrl = viewEntity.getJdbcConnectionUrl();
        this.databaseName = viewEntity.getDatabaseName();
        this.schemaName = viewEntity.getSchemaName();
        this.viewName = viewEntity.getViewName();
        this.createdBy = viewEntity.getCreatedBy() == null
                ? RequestContext.getUsername()
                : viewEntity.getCreatedBy();
        this.createdAt = viewEntity.getCreatedAt() == null
                ? LocalDateTime.now()
                : viewEntity.getCreatedAt();
        this.updatedBy = viewEntity.getUpdatedBy() == null
                ? RequestContext.getUsername()
                : viewEntity.getUpdatedBy();
        this.updatedAt = viewEntity.getUpdatedAt() == null
                ? LocalDateTime.now()
                : viewEntity.getUpdatedAt();
    }

    public ViewEntity toEntity() {
        return ViewEntity.builder()
                .id(id)
                .entityType(ViewEntity.EntityType.valueOf(entityType))
                .driver(driver)
                .jdbcConnectionUrl(jdbcConnectionUrl)
                .databaseName(databaseName)
                .schemaName(schemaName)
                .viewName(viewName)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}
