package pt.jumia.services.brad.data.fxrates.mssql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.brad.data.brad.repository.psql.PsqlExecutionLogRepository;
import pt.jumia.services.brad.data.fxrates.configuration.FxRateEntityManagerConfiguration;
import pt.jumia.services.brad.data.fxrates.entities.FxRatesMssql;
import pt.jumia.services.brad.data.fxrates.entities.QFxRatesMssql;
import pt.jumia.services.brad.data.shared.BaseMssqlRepository;
import pt.jumia.services.brad.data.shared.ViewContext;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.FxRateRepository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class MssqlFxRateRepository extends BaseMssqlRepository implements FxRateRepository {

    private final QFxRatesMssql root = new QFxRatesMssql("root");
    private final PsqlExecutionLogRepository psqlExecutionLogRepository;
    private final EntityManager entityManager;

    @SuppressWarnings({"PMD"})
    @SuppressFBWarnings("EI_EXPOSE_REP2")
    public MssqlFxRateRepository(@Qualifier("fxRateEntityManager") EntityManager entityManager,
                                 PsqlExecutionLogRepository psqlExecutionLogRepository,
                                 @Qualifier("fxRateEntityManagerConfiguration") FxRateEntityManagerConfiguration fxRateEntityManagerConfiguration) {
        super(fxRateEntityManagerConfiguration, psqlExecutionLogRepository);
        this.psqlExecutionLogRepository = psqlExecutionLogRepository;
        this.entityManager = entityManager;
    }

    @Override
    public List<FxRate> findAll(LocalDateTime lastUpdateTimestamp, ViewEntity fxRateViewEntity, boolean retrying,
                                ExecutionLog executionLog)
            throws DatabaseErrorsException, EntityErrorsException {
        if (Objects.isNull(fxRateViewEntity)) {
            throw EntityErrorsException.createNullClassError(ViewEntity.class);
        }
        return processFxRateQuery(null, lastUpdateTimestamp, fxRateViewEntity, retrying, executionLog);
    }

    @Override
    public List<FxRate> findByCurrency(List<String> currencies, LocalDateTime lastUpdateTimestamp, ViewEntity fxRateViewEntity, boolean retrying,
                                       ExecutionLog executionLog)
            throws DatabaseErrorsException, EntityErrorsException {
        if (Objects.isNull(fxRateViewEntity)) {
            throw EntityErrorsException.createNullClassError(ViewEntity.class);
        }
        return processFxRateQuery(currencies, lastUpdateTimestamp, fxRateViewEntity, retrying, executionLog);
    }

    @Override
    public LocalDateTime findLastFxRateWithOffset(ViewEntity fxRateViewEntity, Integer offset) throws EntityErrorsException {
        if (Objects.isNull(fxRateViewEntity)) {
            throw EntityErrorsException.createNullClassError(ViewEntity.class);
        }
        try {
            ViewContext.setCurrentContext(fxRateViewEntity);

            JPAQuery<FxRatesMssql> query = new JPAQuery<>(entityManager)
                    .select(root)
                    .from(root)
                    .orderBy(root.timestampLastUpdate.desc())
                    .offset(offset)
                    .limit(1);
            FxRatesMssql fxRatesMssql = query.fetchFirst();
            return Objects.isNull(fxRatesMssql) ? null : fxRatesMssql.getTimestampLastUpdate();
        } catch (Exception e) {
            log.error("Error fetching last fx rate with offset: {}", e.getMessage());
        } finally {
            ViewContext.clear();
        }
        return null;
    }

    @Override
    public List<FxRate> findAllLastFxRateWithOffset(ViewEntity fxRateViewEntity, Integer offset) throws EntityErrorsException {
        if (Objects.isNull(fxRateViewEntity)) {
            throw EntityErrorsException.createNullClassError(ViewEntity.class);
        }
        try {
            ViewContext.setCurrentContext(fxRateViewEntity);

            JPAQuery<FxRatesMssql> query = new JPAQuery<>(entityManager)
                    .select(root)
                    .from(root)
                    .orderBy(root.timestampLastUpdate.desc())
                    .offset(offset);
            return query.fetch().stream()
                    .map(FxRatesMssql::toEntity)
                    .toList();
        } catch (Exception e) {
            log.error("Error fetching all last fx rates with offset: {}", e.getMessage());
        } finally {
            ViewContext.clear();
        }
        return List.of();
    }

    private void filterByLastUpdateTimestamp(LocalDateTime lastUpdateTimestamp, JPAQuery<FxRatesMssql> query) {
        query.where(root.timestampLastUpdate.gt(lastUpdateTimestamp));
    }

    private List<FxRate> processFxRateQuery(List<String> currencies, LocalDateTime lastUpdateTimestamp, ViewEntity fxRateViewEntity, boolean retrying,
                                            ExecutionLog executionLog) throws DatabaseErrorsException {
        try {
            Optional<ExecutionLog> optionalExecutionLog = this.psqlExecutionLogRepository.findById(executionLog.getId());
            if (optionalExecutionLog.isPresent()) {
                executionLog = optionalExecutionLog.get();
            }
            ViewContext.setCurrentContext(fxRateViewEntity);
            int pageSize = 2;
            int page = 0;

            JPAQuery<FxRatesMssql> query = new JPAQueryFactory(entityManager)
                    .select(root)
                    .from(root);

            if (!CollectionUtils.isEmpty(currencies)) {
                query.where(root.quoteCurrency.in(currencies));
            }

            if (!Objects.isNull(lastUpdateTimestamp)) {
                filterByLastUpdateTimestamp(lastUpdateTimestamp, query);
            }

            List<FxRatesMssql> allFxRatesMssqlList = new ArrayList<>();
            LocalDateTime startTime = LocalDateTime.now();
            List<FxRatesMssql> fetchedFxRatesMssqlList;
            do {
                fetchedFxRatesMssqlList = query.distinct()
                        .offset((long) page * pageSize)
                        .limit(pageSize)
                        .fetch();
                page++;
                allFxRatesMssqlList.addAll(fetchedFxRatesMssqlList);
            } while (!fetchedFxRatesMssqlList.isEmpty());

            LocalDateTime endTime = LocalDateTime.now();

            String appliedFilterString = "ViewEntity: " + fxRateViewEntity.getDatabaseName() + " - " + fxRateViewEntity.getViewName() + "\n" +
                    (lastUpdateTimestamp == null ? "" : "lastUpdateTimestamp: " + lastUpdateTimestamp);
            logExecution(startTime, endTime, allFxRatesMssqlList.size(), appliedFilterString, query.toString(), executionLog);
            return allFxRatesMssqlList.stream().map(FxRatesMssql::toEntity).collect(Collectors.toList());
        } catch (Exception e) {
            this.handleException(e, retrying, fxRateViewEntity, executionLog);
        } finally {
            ViewContext.clear();
        }
        return List.of();
    }

}
