package pt.jumia.services.brad.data.fxrates.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import pt.jumia.services.brad.data.shared.BaseFxRates;
import pt.jumia.services.brad.data.shared.BaseFxRatesId;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;

import java.time.LocalDate;


@Entity
@Table(name = "`RPT_FX_RATES_DAILY`", schema = "dbo")
@Getter
@Setter
@NoArgsConstructor
@IdClass(BaseFxRatesId.class)
public class FxRatesMssql extends BaseFxRates {

    @Id
    @Column(name = "`Base_Currency`", length = 3)
    protected String baseCurrency;

    @Id
    @Column(name = "`Quote_Currency`", length = 3)
    protected String quoteCurrency;

    @Id
    @Column(name = "`Rate_Date`")
    protected LocalDate rateDate;

    public FxRatesMssql(FxRate fxRate) {
        super(fxRate);
    }

    @Override
    public FxRate toEntity(){
        FxRate fxRate = super.toEntity();

        return FxRate.builder()
                .baseCurrency(Currency.builder().code(baseCurrency).build())
                .quoteCurrency(Currency.builder().code(quoteCurrency).build())
                .rateDate(rateDate)
                .bid(fxRate.getBid())
                .bisLoadedAt(fxRate.getBisLoadedAt())
                .skAudInsert(fxRate.getSkAudInsert())
                .skAudUpdate(fxRate.getSkAudUpdate())
                .timestampLastUpdate(fxRate.getTimestampLastUpdate())
                .build();
    }

}
