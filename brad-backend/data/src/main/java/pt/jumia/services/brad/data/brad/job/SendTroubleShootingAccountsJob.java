package pt.jumia.services.brad.data.brad.job;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jetbrains.annotations.NotNull;
import org.quartz.JobExecutionContext;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import pt.jumia.services.brad.domain.usecases.accounts.SendAccountsInTroubleShootingUseCase;


@Slf4j
public class SendTroubleShootingAccountsJob extends QuartzJobBean {

    @Override
    protected void executeInternal(@NotNull JobExecutionContext jobExecutionContext) {

        try {
            ApplicationContext applicationContext = (ApplicationContext)
                jobExecutionContext.getScheduler().getContext().get("applicationContext");

            SendAccountsInTroubleShootingUseCase sendAccountsInTroubleShootingUseCase
                = applicationContext.getBean(SendAccountsInTroubleShootingUseCase.class);

            sendAccountsInTroubleShootingUseCase.execute();

        } catch (Exception e) {
            log.error("Error while Sending troubleshooting account emails \n\t{}", ExceptionUtils.getStackTrace(e));
        }

    }

}
