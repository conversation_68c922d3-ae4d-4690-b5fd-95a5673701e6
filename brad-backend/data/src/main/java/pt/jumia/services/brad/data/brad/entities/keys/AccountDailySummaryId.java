package pt.jumia.services.brad.data.brad.entities.keys;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.data.brad.entities.AccountPsql;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public class AccountDailySummaryId implements Serializable {

    @Serial
    private static final long serialVersionUID = 13565663733497651L;

    private LocalDate transactionDate;
    private AccountPsql account;

    @Override
    public boolean equals(Object anotherId) {

        if (this == anotherId) {
            return true;
        }
        if (!(anotherId instanceof AccountDailySummaryId)) {
            return false;
        }

        AccountDailySummaryId otherId = (AccountDailySummaryId) anotherId;

        return Objects.equals(this.transactionDate, otherId.transactionDate) &&
            Objects.equals(this.account.getId(), otherId.getAccount().getId());
    }

    @Override
    public int hashCode() {

        return Objects.hash(this.transactionDate, this.account);
    }

}
