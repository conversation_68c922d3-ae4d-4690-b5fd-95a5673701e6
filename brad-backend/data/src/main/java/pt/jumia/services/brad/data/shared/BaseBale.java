package pt.jumia.services.brad.data.shared;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import pt.jumia.services.brad.domain.entities.Bale;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

@Getter
@Setter
@MappedSuperclass
@NoArgsConstructor
public abstract class BaseBale extends BaseEntityFieldMap<Bale.SortingFields>{

    @Column(name = "`id_company`", length = 5)
    protected String idCompany;

    @Column(name = "`Document No_`", length = 50)
    protected String documentNo;

    @Column(name = "`Document Type`", length = 50)
    protected String documentType;

    @Column(name = "`Posting Date`")
    protected LocalDate postingDate;

    @Column(name = "`Bank Account Posting Group`", length = 50)
    protected String bankAccountPostingGroup;

    @Column(name = "`Description`", length = 255)
    protected String description;

    @Column(name = "`Source Code`", length = 50)
    protected String sourceCode;

    @Column(name = "`Reason Code`", length = 50)
    protected String reasonCode;

    @Column(name = "`Busline`", length = 50)
    protected String busLine;

    @Column(name = "`Department`", length = 50)
    protected String department;

    @Column(name = "`Amount`")
    protected BigDecimal amount;

    @Column(name = "`Remaining Amount`")
    protected BigDecimal remainingAmount;

    @Column(name = "`Amount (LCY)`")
    protected BigDecimal amountLcy;

    @Column(name = "`Bal_ Account Type`", length = 12)
    protected String balanceAccountType;

    @Column(name = "`Open`")
    protected Boolean isOpen;

    @Column(name = "`Reversed`")
    protected Boolean isReversed;

    @Column(name = "`Posted by`", length = 50)
    protected String postedBy;

    @Column(name = "`External Document No_`", length = 50)
    protected String externalDocumentNo;

    @Column(name = "`BALE_Timestamp`")
    protected String baleTimestamp;

    @Column(name = "`BA_Timestamp`")
    protected String bankAccountTimestamp;

    @Column(name = "`Bal_ Account No_`", length = 50)
    protected String balanceAccountNumber;

    @SneakyThrows
    public Bale toEntity() {
        return Bale.builder()
                .idCompany(idCompany)
                .documentNo(documentNo)
                .documentType(documentType)
                .postingDate(postingDate)
                .accountPostingGroup(bankAccountPostingGroup)
                .description(description)
                .sourceCode(sourceCode)
                .reasonCode(reasonCode)
                .busLine(busLine)
                .department(department)
                .amount(amount)
                .remainingAmount(remainingAmount)
                .amountLcy(amountLcy)
                .balanceAccountNumber(balanceAccountNumber)
                .balanceAccountType(balanceAccountType)
                .isOpen(isOpen)
                .isReversed(isReversed)
                .postedBy(postedBy)
                .externalDocumentNo(externalDocumentNo)
                .baleTimestamp(baleTimestamp)
                .accountTimestamp(bankAccountTimestamp)
                .build();
    }

    public BaseBale(Bale bale) {
        this.idCompany = bale.getIdCompany();
        this.documentNo = bale.getDocumentNo();
        this.documentType = bale.getDocumentType();
        this.postingDate = bale.getPostingDate();
        this.bankAccountPostingGroup = bale.getAccountPostingGroup();
        this.description = bale.getDescription();
        this.sourceCode = bale.getSourceCode();
        this.reasonCode = bale.getReasonCode();
        this.busLine = bale.getBusLine();
        this.department = bale.getDepartment();
        this.amount = bale.getAmount();
        this.remainingAmount = bale.getRemainingAmount();
        this.amountLcy = bale.getAmountLcy();
        this.balanceAccountNumber = bale.getBalanceAccountNumber();
        this.balanceAccountType = bale.getBalanceAccountType();
        this.isOpen = bale.getIsOpen();
        this.isReversed = bale.getIsReversed();
        this.postedBy = bale.getPostedBy();
        this.externalDocumentNo = bale.getExternalDocumentNo();
        this.baleTimestamp = bale.getBaleTimestamp();
        this.bankAccountTimestamp = bale.getAccountTimestamp();
    }

    static {
        Map<Bale.SortingFields, String> entityFields = Map.ofEntries(
            Map.entry(Bale.SortingFields.ID, "id"),
            Map.entry(Bale.SortingFields.ID_COMPANY, "idCompany"),
            Map.entry(Bale.SortingFields.ACCOUNT_NUMBER, "accountNumber"),
            Map.entry(Bale.SortingFields.ACCOUNT_NAME, "accountName"),
            Map.entry(Bale.SortingFields.ACCOUNT_TYPE, "accountType"),
            Map.entry(Bale.SortingFields.CURRENCY, "currency"),
            Map.entry(Bale.SortingFields.KYRIBA_REFERENCE, "kyribaReference"),
            Map.entry(Bale.SortingFields.ENTRY_NO, "entryNo"),
            Map.entry(Bale.SortingFields.DOCUMENT_NO, "documentNo"),
            Map.entry(Bale.SortingFields.DOCUMENT_TYPE, "documentType"),
            Map.entry(Bale.SortingFields.POSTING_DATE, "postingDate"),
            Map.entry(Bale.SortingFields.BANK_ACCOUNT, "account"),
            Map.entry(Bale.SortingFields.BANK_ACCOUNT_POSTING_GROUP, "bankAccountPostingGroup"),
            Map.entry(Bale.SortingFields.DESCRIPTION, "description"),
            Map.entry(Bale.SortingFields.SOURCE_CODE, "sourceCode"),
            Map.entry(Bale.SortingFields.REASON_CODE, "reasonCode"),
            Map.entry(Bale.SortingFields.BUS_LINE, "busLine"),
            Map.entry(Bale.SortingFields.DEPARTMENT, "department"),
            Map.entry(Bale.SortingFields.DIRECTION, "direction"),
            Map.entry(Bale.SortingFields.AMOUNT, "amount"),
            Map.entry(Bale.SortingFields.REMAINING_AMOUNT, "remainingAmount"),
            Map.entry(Bale.SortingFields.TRANSACTION_CURRENCY, "transactionCurrency"),
            Map.entry(Bale.SortingFields.AMOUNT_LCY, "amountLcy"),
            Map.entry(Bale.SortingFields.BALANCE_ACCOUNT_NO, "balanceAccountNumber"),
            Map.entry(Bale.SortingFields.BALANCE_ACCOUNT_TYPE, "balanceAccountType"),
            Map.entry(Bale.SortingFields.IS_OPEN, "isOpen"),
            Map.entry(Bale.SortingFields.REVERSED, "isReversed"),
            Map.entry(Bale.SortingFields.POSTED_BY, "postedBy"),
            Map.entry(Bale.SortingFields.EXTERNAL_DOCUMENT_NO, "externalDocumentNo"),
            Map.entry(Bale.SortingFields.BALE_TIMESTAMP, "baleTimestamp"),
            Map.entry(Bale.SortingFields.BANK_ACCOUNT_TIMESTAMP, "bankAccountTimestamp")
        );
        addEntityFieldMap(Bale.SortingFields.class, entityFields);
    }


}
