package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.Setting;

@Audited
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "setting")
public class SettingPsql extends BaseEntityFieldMap<Setting.SortingFields> {

    @Id
    @SequenceGenerator(name = "setting_seq", sequenceName = "setting_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "setting_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "property", nullable = false, unique = true)
    private String property;

    @Column(name = "type", nullable = false)
    private String type;

    @Column(name = "override_key")
    private String overrideKey;

    @Column(name = "value", nullable = false)
    private String value;

    @Column(name = "description")
    private String description;

    @Column(name = "created_by", updatable = false)
    protected String createdBy;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    protected LocalDateTime createdAt;

    @Column(name = "updated_by")
    protected String updatedBy;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    protected LocalDateTime updatedAt;

    public SettingPsql(Setting setting) {

        this.id = setting.getId();
        this.property = setting.getProperty();
        this.type = setting.getType().name();
        this.overrideKey = setting.getOverrideKey();
        this.value = setting.getValue();
        this.description = setting.getDescription();
        this.createdBy = setting.getCreatedBy() == null
            ? RequestContext.getUsername()
            : setting.getCreatedBy();
        this.createdAt = setting.getCreatedAt() == null
            ? LocalDateTime.now(ZoneOffset.UTC)
            : setting.getCreatedAt();
        this.updatedBy = setting.getUpdatedBy() == null
            ? RequestContext.getUsername()
            : setting.getUpdatedBy();
        this.updatedAt = setting.getUpdatedAt() == null
            ? LocalDateTime.now(ZoneOffset.UTC)
            : setting.getUpdatedAt();
    }

    public Setting toEntity() {

        return Setting.builder()
            .id(this.id)
            .property(property)
            .type(Setting.Type.valueOf(type))
            .overrideKey(overrideKey)
            .value(value)
            .description(description)
            .createdBy(this.createdBy)
            .createdAt(this.createdAt)
            .updatedAt(this.updatedAt)
            .updatedBy(this.updatedBy)
            .build();
    }

    static {
        Map<Setting.SortingFields, String> entityFields = Map.ofEntries(
            Map.entry(Setting.SortingFields.ID, "id"),
            Map.entry(Setting.SortingFields.PROPERTY, "property"),
            Map.entry(Setting.SortingFields.TYPE, "type"),
            Map.entry(Setting.SortingFields.OVERRIDE_KEY, "overrideKey"),
            Map.entry(Setting.SortingFields.DESCRIPTION, "description"),
            Map.entry(Setting.SortingFields.VALUE, "value"),
            Map.entry(Setting.SortingFields.CREATED_AT, "createdAt"),
            Map.entry(Setting.SortingFields.CREATED_BY, "createdBy"),
            Map.entry(Setting.SortingFields.UPDATED_AT, "updatedAt"),
            Map.entry(Setting.SortingFields.UPDATED_BY, "updatedBy")
        );

        addEntityFieldMap(Setting.SortingFields.class, entityFields);
    }

}
