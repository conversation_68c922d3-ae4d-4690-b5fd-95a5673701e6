package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import pt.jumia.services.brad.data.brad.entities.AccountStatementPsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.statement.FxRateStatementPsql;

import java.util.Set;

public interface JpaFxRateAccountStatementRepository extends JpaRepository<FxRateStatementPsql, Integer>,
        JpaSpecificationExecutor<FxRateStatementPsql> {

    void deleteAllByStatementId(Long statementId);

    Set<FxRateStatementPsql> findByStatement(AccountStatementPsql accountStatementPsql);

}
