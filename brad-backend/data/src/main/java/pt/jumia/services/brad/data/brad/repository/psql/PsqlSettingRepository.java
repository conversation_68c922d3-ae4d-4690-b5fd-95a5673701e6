package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.QSettingPsql;
import pt.jumia.services.brad.data.brad.entities.SettingPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaSettingRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.Setting;
import pt.jumia.services.brad.domain.entities.filter.setting.SettingFilters;
import pt.jumia.services.brad.domain.entities.filter.setting.SettingSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.SettingRepository;


@Repository
@RequiredArgsConstructor
public class PsqlSettingRepository extends BaseRepository implements SettingRepository {


    private final QSettingPsql root = new QSettingPsql("root");
    private final EntityManager entityManager;
    private final JpaSettingRepository jpaSettingRepository;


    @Override
    @Transactional(readOnly = true)
    public Optional<Setting> findById(long id) {

        return jpaSettingRepository.findById(id)
            .map(SettingPsql::toEntity);
    }

    @Override
    @Transactional
    public Setting upsert(Setting setting) {

        return jpaSettingRepository.save(new SettingPsql(setting)).toEntity();
    }

    @Override
    @Transactional
    public void deleteById(long id) {

        jpaSettingRepository.deleteById(id);
    }

    @Override
    public List<Setting> findAll(SettingFilters filters, SettingSortFilters sortFilters) throws EntityErrorsException {

        JPAQuery<SettingPsql> query = new JPAQueryFactory(entityManager).selectFrom(root);
        buildWhereClauses(filters, query);
        applySort(sortFilters, query, root, Setting.SortingFields.class);

        return query.distinct().fetch().stream()
            .map(SettingPsql::toEntity)
            .collect(Collectors.toList());
    }

    @Override
    public long count(SettingFilters filters) {

        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
            .select(root.id.count())
            .from(root);

        buildWhereClauses(filters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));

    }

    private void buildWhereClauses(SettingFilters filters, JPAQuery<?> query) {

        if (!Objects.isNull(filters)) {
            filterProperty(filters, query);
            filterType(filters, query);
            filterOverrideKey(filters, query);
            filterValue(filters, query);
        }
    }

    private void filterValue(SettingFilters filters, JPAQuery<?> query) {

        if (filters.getValue() != null) {
            query.where(root.value.likeIgnoreCase("%".concat(filters.getValue().concat("%"))));
        }
    }

    private void filterOverrideKey(SettingFilters filters, JPAQuery<?> query) {

        if (filters.getOverrideKey() != null) {
            query.where(root.overrideKey.likeIgnoreCase("%".concat(filters.getOverrideKey().concat("%"))));
        }
    }

    private void filterType(SettingFilters filters, JPAQuery<?> query) {

        if (filters.getType() != null) {
            query.where(root.type.eq(filters.getType().name()));
        }
    }

    private void filterProperty(SettingFilters filters, JPAQuery<?> query) {

        if (filters.getProperty() != null) {
            query.where(root.property.likeIgnoreCase("%".concat(filters.getProperty().concat("%"))));
        }
    }

}
