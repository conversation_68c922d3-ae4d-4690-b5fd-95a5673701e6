package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.CurrencyPsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.FxRatePsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.QFxRatePsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaFxRateRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.fxrate.FxRateFilters;
import pt.jumia.services.brad.domain.entities.filter.fxrate.FxRateSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.BradFxRateRepository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlFxRateRepository extends BaseRepository implements BradFxRateRepository {

    private final QFxRatePsql root = new QFxRatePsql("root");
    private final JpaFxRateRepository jpaFxRateRepository;
    private final EntityManager entityManager;

    @Override
    @Transactional
    public List<FxRate> sync(List<FxRate> fxRates) {
        return fxRates.stream()
                .distinct()
                .map(fxRate -> jpaFxRateRepository.save(new FxRatePsql(fxRate)))
                .map(FxRatePsql::toEntity)
                .collect(Collectors.toList());
     }

    @Override
    public List<FxRate> findAll(FxRateFilters fxRateFilters, FxRateSortFilters fxRateSortFilters, PageFilters pageFilters)
            throws EntityErrorsException {
        JPAQuery<FxRatePsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root);

        if (Objects.nonNull(fxRateFilters)) {
            applyProjectionSelect(fxRateFilters.getSelectedFields(), query, root, FxRatePsql.class);
        }
        buildWhereClauses(fxRateFilters, query);
        applySort(fxRateSortFilters, query, root, FxRate.SortingFields.class);

        return query.fetch().stream()
                .map(FxRatePsql::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<FxRate> findById(Integer id) {
        return jpaFxRateRepository.findById(id).map(FxRatePsql::toEntity);
    }

    @Override
    public List<FxRate> findAllFxRateInRateDateOfBaseCurrency(LocalDate rateDate, Currency currency) {
        return jpaFxRateRepository.findAllByRateDateAndBaseCurrency(rateDate, new CurrencyPsql(currency)).stream()
                .map(FxRatePsql::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<FxRate> findFxRateByRateDateAndBaseCurrencyAndQuoteCurrency(LocalDate rateDate, Currency baseCurrency,
                                                                                Currency quoteCurrency) {
        return jpaFxRateRepository.findByRateDateAndBaseCurrencyAndQuoteCurrency(rateDate,
                        new CurrencyPsql(baseCurrency),
                        new CurrencyPsql(quoteCurrency))
                .map(FxRatePsql::toEntity);
    }

    @Override
    public Optional<FxRate> findLastFxRateInBrad() {
        return jpaFxRateRepository.findFirstByOrderByTimestampLastUpdateDesc().map(FxRatePsql::toEntity);
    }

    @Override
    public long count(FxRateFilters fxRateFilters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(fxRateFilters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    @Override
    public FxRate deleteById(Integer id) {
        Optional<FxRatePsql> fxRatePsql = jpaFxRateRepository.findById(id);
        jpaFxRateRepository.deleteById(id);
        return fxRatePsql.map(FxRatePsql::toEntity).orElse(null);
    }

    private void buildWhereClauses(FxRateFilters fxRateFilters, JPAQuery<?> query) {
        if (Objects.isNull(fxRateFilters)) {
            return;
        }
        filterByBaseCurrency(fxRateFilters, query);
        filterByQuoteCurrency(fxRateFilters, query);
        filterByRateDate(fxRateFilters, query);
        filterByBid(fxRateFilters, query);
        filterByBisLoadedAt(fxRateFilters, query);
        filterBySkAudInsert(fxRateFilters, query);
        filterBySkAudUpdate(fxRateFilters, query);
        filterByTimestampLastUpdate(fxRateFilters, query);
    }

    private void filterByBaseCurrency(FxRateFilters fxRateFilters, JPAQuery<?> query) {
        if (Objects.nonNull(fxRateFilters.getBaseCurrency())) {
            query.where(root.baseCurrency.code.in(fxRateFilters.getBaseCurrency()));
        }
    }

    private void filterByQuoteCurrency(FxRateFilters fxRateFilters, JPAQuery<?> query) {
        if (Objects.nonNull(fxRateFilters.getQuoteCurrency())) {
            query.where(root.quoteCurrency.code.in(fxRateFilters.getQuoteCurrency()));
        }
    }

    private void filterByRateDate(FxRateFilters fxRateFilters, JPAQuery<?> query) {
        if (Objects.nonNull(fxRateFilters.getRateDate())) {
            query.where(root.rateDate.eq(fxRateFilters.getRateDate()));
        }
    }

    private void filterByBid(FxRateFilters fxRateFilters, JPAQuery<?> query) {
        if (Objects.nonNull(fxRateFilters.getBid())) {
            query.where(root.bid.eq(fxRateFilters.getBid()));
        }
    }

    private void filterByBisLoadedAt(FxRateFilters fxRateFilters, JPAQuery<?> query) {
        if (Objects.nonNull(fxRateFilters.getBisLoadedAt())) {
            LocalDateTime bisLoadedAt = fxRateFilters.getBisLoadedAt();
            LocalDateTime startOfDay = bisLoadedAt.with(LocalTime.MIN);
            LocalDateTime endOfDay = bisLoadedAt.with(LocalTime.MAX);
            query.where(root.bisLoadedAt.between(startOfDay, endOfDay));
        }
    }

    private void filterBySkAudInsert(FxRateFilters fxRateFilters, JPAQuery<?> query) {
        if (Objects.nonNull(fxRateFilters.getSkAudInsert())) {
            query.where(root.skAudInsert.eq(fxRateFilters.getSkAudInsert()));
        }
    }

    private void filterBySkAudUpdate(FxRateFilters fxRateFilters, JPAQuery<?> query) {
        if (Objects.nonNull(fxRateFilters.getSkAudUpdate())) {
            query.where(root.skAudUpdate.eq(fxRateFilters.getSkAudUpdate()));
        }
    }

    private void filterByTimestampLastUpdate(FxRateFilters fxRateFilters, JPAQuery<?> query) {
        if (Objects.nonNull(fxRateFilters.getTimestampLastUpdate())) {
            LocalDateTime timestampLastUpdate = fxRateFilters.getTimestampLastUpdate();
            LocalDateTime startOfDay = timestampLastUpdate.with(LocalTime.MIN);
            LocalDateTime endOfDay = timestampLastUpdate.with(LocalTime.MAX);
            query.where(root.timestampLastUpdate.between(startOfDay, endOfDay));
        }
    }
}
