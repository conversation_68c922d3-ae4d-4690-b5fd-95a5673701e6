package pt.jumia.services.brad.data.brad.repository.psql.events.channels;

import com.impossibl.postgres.jdbc.PGDataSource;
import java.sql.SQLException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.data.brad.configuration.EntityManagerConfiguration;
import pt.jumia.services.brad.data.brad.repository.psql.PsqlSettingRepository;
import pt.jumia.services.brad.domain.usecases.setting.ReloadSettingUseCase;


@Component
@RequiredArgsConstructor
public class PsqlChannelFactory {

    private final PGDataSource pgDataSource;
    private final PsqlSettingRepository settingRepository;
    private final ReloadSettingUseCase reloadSettingsUseCase;
    private final EntityManagerConfiguration entityManagerConfiguration;

    public PsqlSettingsChangeChannel createSettingsChangeChannel() throws SQLException {

        return new PsqlSettingsChangeChannel(
            pgDataSource, settingRepository, reloadSettingsUseCase,
            (int) entityManagerConfiguration.getDataProperties().getEvents().getCheckConnectionTimeout().toMillis());
    }

}
