package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.ContactPsql;
import pt.jumia.services.brad.data.brad.entities.QContactPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaContactRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.account.Contact;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.contact.ContactFilters;
import pt.jumia.services.brad.domain.entities.filter.contact.ContactSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.ContactRepository;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlContactRepository extends BaseRepository implements ContactRepository{

    private final QContactPsql root = new QContactPsql("root");
    private final JpaContactRepository jpaContactRepository;
    private final EntityManager entityManager;


    @Override
    public Contact upsert(Contact contact) {
        return jpaContactRepository.save(new ContactPsql(contact)).toEntity();
    }

    @Override
    public Optional<Contact> findById(long id) {
        return jpaContactRepository.findById(id).map(ContactPsql::toEntity);
    }

    @Override
    public Optional<Contact> findByEmail(String email) {
        JPAQuery<ContactPsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root)
                .where(root.email.eq(email));

        return Optional.ofNullable(query.fetchOne()).map(ContactPsql::toEntity);
    }

    @Override
    public List<Contact> findAll(ContactFilters contactFilters, ContactSortFilters contactSortFilters,
                                 PageFilters pageFilters) throws EntityErrorsException {
        JPAQuery<ContactPsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root);

        if (Objects.nonNull(contactFilters)) {
            applyProjectionSelect(contactFilters.getSelectedFields(), query, root, ContactPsql.class);
        }
        buildWhereClauses(contactFilters, query);
        applySort(contactSortFilters, query, root, Contact.SortingFields.class);
        applyPagination(pageFilters, query);


        return query.distinct().fetch().stream()
                .map(ContactPsql::toEntity)
                .collect(Collectors.toList());
    }


    @Override
    @Transactional
    public void deleteById(long id) {
        jpaContactRepository.deleteById(id);
    }

    @Override
    public Integer count(ContactFilters filters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(filters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    private void buildWhereClauses(ContactFilters contactFilters, JPAQuery<?> query) {
        if (Objects.isNull(contactFilters)) {
            return;
        }
        filterByContactType(contactFilters, query);
        filterByName(contactFilters, query);
        filterByEmail(contactFilters, query);
        filterByAccountID(contactFilters, query);
        filterByAccountIds(contactFilters, query);
        filterByAccountID(contactFilters, query);
        filterByCreatedAt(contactFilters, query);

    }

    public void filterByContactType(ContactFilters contactFilters, JPAQuery<?> query) {
        if (!Objects.isNull(contactFilters.getContactType())) {
            query.where(root.contactType.in(contactFilters.getContactType()));
        }
    }

    public void filterByName(ContactFilters contactFilters, JPAQuery<?> query) {
        if (!Objects.isNull(contactFilters.getName())) {
            query.where(root.name.likeIgnoreCase("%" + contactFilters.getName() + "%"));
        }
    }

    public void filterByEmail(ContactFilters contactFilters, JPAQuery<?> query) {
        if (!Objects.isNull(contactFilters.getEmail())) {
            query.where(root.email.likeIgnoreCase("%" + contactFilters.getEmail() + "%"));
        }
    }

    public void filterByAccountID(ContactFilters contactFilters, JPAQuery<?> query) {
        if (!Objects.isNull(contactFilters.getAccountID())) {
            query.where(root.account.id.eq(contactFilters.getAccountID()));
        }
    }

    public void filterByAccountIds(ContactFilters contactFilters, JPAQuery<?> query) {
        if (!Objects.isNull(contactFilters.getAccountIds())) {
            query.where(root.account.id.in(contactFilters.getAccountIds()));
        }
    }

    public void filterByCreatedAt(ContactFilters contactFilters, JPAQuery<?> query) {
        if (!Objects.isNull(contactFilters.getCreatedAt())) {
            LocalDateTime createdAt = contactFilters.getCreatedAt();
            LocalDateTime startOfDay = createdAt.with(LocalTime.MIN);
            LocalDateTime endOfDay = createdAt.with(LocalTime.MAX);
            query.where(root.createdAt.between(startOfDay, endOfDay));
        }

    }

}
