package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.envers.NotAudited;
import pt.jumia.services.brad.data.brad.entities.fxrates.bale.FxRateBalePsql;
import lombok.Setter;
import pt.jumia.services.brad.data.shared.BaseBale;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Setter
@Entity
@Table(name = "bale")
@NoArgsConstructor
public class BalePsql extends BaseBale implements Serializable {

    @Serial
    private static final long serialVersionUID = 46841268426864L;

    @Id
    @SequenceGenerator(name = "bale_id_seq", sequenceName = "bale_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "bale_id_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "`Entry No_`", nullable = false)
    protected Integer entryNo;

    @ManyToOne
    @JoinColumn(name = "`BankAccount`", referencedColumnName = "id")
    protected AccountPsql account;

    @Getter
    @NotAudited
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "bale", cascade = CascadeType.ALL)
    private Set<FxRateBalePsql> fxRateBaleSet;

    @ManyToOne
    @JoinColumn(name = "`Currency`", referencedColumnName = "id")
    protected CurrencyPsql transactionCurrency;

    @Column(name = "direction", nullable = false)
    private Integer direction;

    @Column(name = "Reconcile_Status")
    protected String reconcileStatus;

    public BalePsql(Bale bale) {
        super(bale);
        this.id = bale.getId();
        this.account = new AccountPsql(bale.getAccount());
        this.transactionCurrency = new CurrencyPsql(bale.getTransactionCurrency());
        this.direction = bale.getDirection().getValue();
        this.entryNo = bale.getEntryNo();
        this.reconcileStatus = bale.getReconcileStatus().name();
    }

    public static BalePsql fromEntity(Bale bale) {
        return new BalePsql(bale);
    }

    @Override
    public Bale toEntity() {

        Set<FxRate> fxRates = new HashSet<>();
        if (Objects.nonNull(this.fxRateBaleSet)) {
            fxRates = this.fxRateBaleSet.stream()
                    .map(fxRateBalePsql -> fxRateBalePsql.getFxRate().toEntity())
                    .collect(Collectors.toSet());
        }

        Bale bale = super.toEntity();
        return bale.toBuilder()
                .id(id)
                .account(account.toEntity())
                .fxRates(fxRates)
                .entryNo(entryNo)
                .transactionCurrency(transactionCurrency.toEntity())
                .direction(Direction.getDirection(String.valueOf(this.direction)))
                .reconcileStatus(ReconcileStatus.valueOf(reconcileStatus))
                .build();

    }
}
