package pt.jumia.services.brad.data.brad.repository.psql;


import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.brad.data.brad.entities.ExportLogPsql;
import pt.jumia.services.brad.data.brad.entities.QExportLogPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaExportLogDataRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.ExportLog;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.exportlog.ExportLogFilters;
import pt.jumia.services.brad.domain.entities.filter.exportlog.ExportLogSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.ExportLogRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlExportLogRepository extends BaseRepository implements ExportLogRepository {

    private final EntityManager entityManager;
    private final QExportLogPsql root = new QExportLogPsql("root");
    private final PsqlCountryRepository psqlCountryRepository;
    private final JpaExportLogDataRepository jpaExportLogDataRepository;

    @Override
    @Transactional(readOnly = true)
    public List<ExportLog> findAll(ExportLogFilters filters, ExportLogSortFilters sortFilters, PageFilters pageFilters)
            throws EntityErrorsException {
        JPAQuery<ExportLogPsql> query = new JPAQueryFactory(entityManager).selectFrom(root);

        buildWhereClauses(filters, query);
        applySort(sortFilters, query, root, ExportLog.SortingFields.class);
        applyPagination(pageFilters, query);

        return query.fetch().stream()
                .map(ExportLogPsql::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ExportLog> findById(long id) {
        return jpaExportLogDataRepository.findById(id)
                .map(ExportLogPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public long count(ExportLogFilters filters) {
        JPAQuery<ExportLogPsql> query = new JPAQueryFactory(entityManager).selectFrom(root);

        buildWhereClauses(filters, query);

        return query.fetchCount();
    }

    @Override
    public ExportLog insert(ExportLog entity) {
        return jpaExportLogDataRepository.save(new ExportLogPsql(entity)).toEntity();
    }

    @Override
    public ExportLog update(long id, ExportLog entity) {
        ExportLogPsql exportLogPsql = jpaExportLogDataRepository.findById(id)
                .orElseThrow(() -> NotFoundException.createNotFound(ExportLog.class, id));

        exportLogPsql.setStatus(entity.getStatus().name());
        exportLogPsql.setFileUrl(entity.getFileUrl());
        exportLogPsql.setFileName(entity.getFileName());
        exportLogPsql.setExecutionTime(entity.getExecutionTime());
        exportLogPsql.setRowCount(entity.getRowCount());
        exportLogPsql.setUpdatedAt(LocalDateTime.now(ZoneOffset.UTC));

        return jpaExportLogDataRepository.save(exportLogPsql).toEntity();

    }

    @Override
    public void deleteById(long id) {
        jpaExportLogDataRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ExportLog> executeByFiltersAndStatusNotContainingAndCreatedAtAfter(String filters,
                                                                          String status,
                                                                          LocalDateTime date) {
        return this.jpaExportLogDataRepository
                .findByFiltersAndStatusNotContainingAndCreatedAtAfter(filters, status, date)
                .map(ExportLogPsql::toEntity);
    }

    private void buildWhereClauses(ExportLogFilters filters, JPAQuery<ExportLogPsql> query) {
        if (filters != null) {
            filterFilterText(filters, query);
            filterType(filters, query);
            filterStatus(filters, query);
            filterCreatedAtFrom(filters, query);
            filterCreatedAtTo(filters, query);
            filterCountries(filters, query);
        }
    }

    private void filterFilterText(ExportLogFilters filters, JPAQuery<ExportLogPsql> query) {
        if (filters.getFilterText() != null) {
            query.where(root.fileName.likeIgnoreCase("%".concat(filters.getFilterText()).concat("%")));
        }
    }

    private void filterType(ExportLogFilters filters, JPAQuery<ExportLogPsql> query) {
        if (!CollectionUtils.isEmpty(filters.getTypes())) {
            query.where(root.type.in(filters.getTypes().stream().map(ExportLog.Type::name).collect(Collectors.toList())));
        }
    }

    private void filterStatus(ExportLogFilters filters, JPAQuery<ExportLogPsql> query) {
        if (!CollectionUtils.isEmpty(filters.getStatus())) {
            query.where(root.status.in(filters.getStatus().stream()
                    .map(ExportLog.Status::name)
                    .collect(Collectors.toList()).get(0)));
        }
    }

    private void filterCreatedAtFrom(ExportLogFilters filters, JPAQuery<ExportLogPsql> query) {
        if (filters.getCreatedAtFrom() != null) {
            query.where(root.createdAt.goe(filters.getCreatedAtFrom()));
        }
    }

    private void filterCreatedAtTo(ExportLogFilters filters, JPAQuery<ExportLogPsql> query) {
        if (filters.getCreatedAtTo() != null) {
            query.where(root.createdAt.loe(filters.getCreatedAtTo()));
        }
    }
    private void filterCountries(ExportLogFilters filters, JPAQuery<ExportLogPsql> query) {
        if (!CollectionUtils.isEmpty(filters.getCountryCodes())) {
            BooleanBuilder builder = new BooleanBuilder();
            for (String countryCode : filters.getCountryCodes()) {
                builder.or(root.countries.contains(countryCode));
            }
            query.where(builder);

        }
    }
}
