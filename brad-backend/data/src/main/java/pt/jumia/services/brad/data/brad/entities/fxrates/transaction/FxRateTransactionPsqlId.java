package pt.jumia.services.brad.data.brad.entities.fxrates.transaction;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class FxRateTransactionPsqlId implements Serializable {

    @Serial
    private static final long serialVersionUID = 52741523496L;

    @Column(name = "FX_RATES_ID")
    private Integer fxRateId;
    @Column(name = "TRANSACTION_ID")
    private Long transactionId;
    @Column(name = "PARTITION_KEY")
    private String partitionKey;

}
