package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import pt.jumia.services.brad.data.brad.entities.ExecutionLogPsql;
import pt.jumia.services.brad.data.brad.entities.QExecutionLogPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaExecutionLogRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.executionlog.ExecutionLogFilters;
import pt.jumia.services.brad.domain.entities.filter.executionlog.ExecutionLogSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.ExecutionLogRepository;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class PsqlExecutionLogRepository extends BaseRepository implements ExecutionLogRepository {

    private final QExecutionLogPsql root = new QExecutionLogPsql("root");
    private final JpaExecutionLogRepository jpaExecutionLogRepository;
    private final EntityManager entityManager;

    @Override
    public ExecutionLog upsert(ExecutionLog executionLog) {
        return jpaExecutionLogRepository.save(new ExecutionLogPsql(executionLog)).toEntity();
    }

    @Override
    public Optional<ExecutionLog> findById(long id) {
        return jpaExecutionLogRepository.findById(id)
                .map(ExecutionLogPsql::toEntity);
    }

    @Override
    public List<ExecutionLog> findAll(ExecutionLogFilters executionLogFilters, ExecutionLogSortFilters executionLogSortFilters,
                                      PageFilters pageFilters)
        throws EntityErrorsException {

        JPAQuery<ExecutionLogPsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root);

        if (Objects.nonNull(executionLogFilters)) {
            applyProjectionSelect(executionLogFilters.getSelectedFields(), query, root, ExecutionLogPsql.class);
        }
        buildWhereClauses(executionLogFilters, query);
        applySort(executionLogSortFilters, query, root, ExecutionLog.SortingFields.class);
        applyPagination(pageFilters, query);

        return query.distinct().fetch().stream()
                .map(ExecutionLogPsql::toEntity)
                .toList();
    }

    @Override
    public void deleteById(long id) {
        jpaExecutionLogRepository.deleteById(id);
    }

    @Override
    public Integer count(ExecutionLogFilters filters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(filters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    @Override
    public void deleteAll() {
        jpaExecutionLogRepository.deleteAll();
    }

    private void buildWhereClauses(ExecutionLogFilters executionLogFilters, JPAQuery<?> query) {
        if (Objects.isNull(executionLogFilters)) {
            return;
        }
        filterByLogType(executionLogFilters, query);
        filterByLogStatus(executionLogFilters, query);
        filterByRecordsAmount(executionLogFilters, query);
        filterByExecutionStartTime(executionLogFilters, query);
        filterByExecutionEndTime(executionLogFilters, query);
        filterByRelatedEntity(executionLogFilters, query);


    }

    public void filterByLogType(ExecutionLogFilters executionLogFilters, JPAQuery<?> query) {
        if (!Objects.isNull(executionLogFilters.getLogType())) {
            query.where(root.logType.in(executionLogFilters.getLogType()));
        }
    }

    public void filterByLogStatus(ExecutionLogFilters executionLogFilters, JPAQuery<?> query) {
        if (!Objects.isNull(executionLogFilters.getLogStatus())) {
            query.where(root.logStatus.in(executionLogFilters.getLogStatus()));
        }
    }

    public void filterByRecordsAmount(ExecutionLogFilters executionLogFilters, JPAQuery<?> query) {
        if (!Objects.isNull(executionLogFilters.getRecordsAmount())) {
            query.where(root.recordsAmount.eq(executionLogFilters.getRecordsAmount()));
        }
    }

    public void filterByExecutionStartTime(ExecutionLogFilters executionLogFilters, JPAQuery<?> query) {
        if (!Objects.isNull(executionLogFilters.getExecutionStartTime())) {
            LocalDateTime createdAt = executionLogFilters.getExecutionStartTime();
            LocalDateTime startOfDay = createdAt.with(LocalTime.MIN);
            LocalDateTime endOfDay = createdAt.with(LocalTime.MAX);
            query.where(root.executionStartTime.between(startOfDay, endOfDay));
        }
    }

    public void filterByExecutionEndTime(ExecutionLogFilters executionLogFilters, JPAQuery<?> query) {
        if (!Objects.isNull(executionLogFilters.getExecutionEndTime())) {
            LocalDateTime createdAt = executionLogFilters.getExecutionEndTime();
            LocalDateTime startOfDay = createdAt.with(LocalTime.MIN);
            LocalDateTime endOfDay = createdAt.with(LocalTime.MAX);
            query.where(root.executionEndTime.between(startOfDay, endOfDay));
        }
    }

    public void filterByRelatedEntity(ExecutionLogFilters executionLogFilters, JPAQuery<?> query) {
        if (!Objects.isNull(executionLogFilters.getRelatedEntity())) {
            query.where(root.syncingErrors.likeIgnoreCase("%" + executionLogFilters.getRelatedEntity() + "%"));
        }
    }
}
