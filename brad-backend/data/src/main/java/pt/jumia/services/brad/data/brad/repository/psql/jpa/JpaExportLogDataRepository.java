package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import pt.jumia.services.brad.data.brad.entities.ExportLogPsql;

import java.time.LocalDateTime;
import java.util.Optional;

public interface JpaExportLogDataRepository extends JpaRepository<ExportLogPsql, Long> {

    Optional<ExportLogPsql> findById(Long id);

    void deleteById(Long id);

    Optional<ExportLogPsql> findByFiltersAndStatusNotContainingAndCreatedAtAfter(String filters, String status, LocalDateTime date);
}
