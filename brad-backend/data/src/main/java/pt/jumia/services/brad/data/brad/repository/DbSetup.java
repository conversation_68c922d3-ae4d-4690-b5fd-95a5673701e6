package pt.jumia.services.brad.data.brad.repository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.data.brad.utils.MigrationService;
import pt.jumia.services.brad.domain.Profiles;
import pt.jumia.services.brad.domain.properties.SpringProperties;
import pt.jumia.services.brad.domain.usecases.accounts.CreateAccountsUseCase;

import javax.sql.DataSource;

/**
 * Fills up the DB with data on boot and provides utility methods to reset the DB in tests
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Profile({Profiles.DB_SETUP})
public class DbSetup implements ApplicationListener<ContextRefreshedEvent> {

    private final SpringProperties springProperties;
    private final CreateAccountsUseCase createAccountsUseCase;
    private final MigrationService migrationService;
    private final DataSource dataSource;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {}

    public void cleanDataBase() {
        cleanApplicationSchemas();
        migrateApplicationSchemas();
    }

    public void resetDataBase() {
        cleanDataBase();
    }

    private void cleanApplicationSchemas() {
        for (String schemaDir : springProperties.getFlyway().getSchemas()) {
            try {
                migrationService.cleanDatabaseSchema(dataSource, schemaDir);
            } catch (Exception e) {
                log.warn("Error cleaning database: {}", ExceptionUtils.getStackTrace(e));
            }
        }
    }

    private void migrateApplicationSchemas() {
        for (String schemaDir : springProperties.getFlyway().getSchemas()) {
            try {
                migrationService.migrateDatabaseSchema(dataSource, schemaDir);
            } catch (Exception e) {
                log.warn("Error migrating database: {}", ExceptionUtils.getStackTrace(e));
            }
        }
    }
}
