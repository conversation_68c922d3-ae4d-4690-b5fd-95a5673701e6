package pt.jumia.services.brad.data.brad.entities.fxrates.transaction;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.data.brad.entities.TransactionPsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.FxRatePsql;

@Getter
@Entity
@NoArgsConstructor
@Table(name = "FX_RATES_TRANSACTION")
public class FxRateTransactionPsql {

    @EmbeddedId
    private FxRateTransactionPsqlId id;

    @MapsId("fxRateKey")
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumns({
            @JoinColumn(name = "FX_RATES_ID", referencedColumnName = "ID")
    })
    private FxRatePsql fxRate;

    @MapsId("transactionKey")
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumns({
            @JoinColumn(name = "TRANSACTION_ID", referencedColumnName = "ID"),
            @JoinColumn(name = "PARTITION_KEY", referencedColumnName = "PARTITION_KEY")
    })
    private TransactionPsql transaction;

    @SuppressFBWarnings("EI_EXPOSE_REP2")
    public FxRateTransactionPsql(FxRatePsql fxRate, TransactionPsql transaction) {
        this.fxRate = fxRate;
        this.transaction = transaction;
        this.id = new FxRateTransactionPsqlId(fxRate.toEntity().getId(),
                transaction.toEntity().getId(), transaction.getPartitionKey());
    }

}
