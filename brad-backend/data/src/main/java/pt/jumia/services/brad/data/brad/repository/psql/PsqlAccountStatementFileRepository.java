package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.brad.data.brad.entities.AccountStatementFilePsql;
import pt.jumia.services.brad.data.brad.entities.QAccountStatementFilePsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaAccountStatementFileRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementFileRepository;

@Repository
@RequiredArgsConstructor
public class PsqlAccountStatementFileRepository extends BaseRepository implements AccountStatementFileRepository {

    private final QAccountStatementFilePsql root = new QAccountStatementFilePsql("root");
    private final JpaAccountStatementFileRepository jpaAccountStatementFileRepository;
    private final EntityManager entityManager;


    @Override
    @Transactional
    public AccountStatementFile upsert(AccountStatementFile statementFile) {

        return jpaAccountStatementFileRepository.save(AccountStatementFilePsql.fromEntity(statementFile)).toEntity();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AccountStatementFile> findById(long id) {

        return jpaAccountStatementFileRepository.findById(id).map(AccountStatementFilePsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AccountStatementFile> findAll(AccountStatementFileFilters statementFileFilters,
        AccountStatementFileSortFilters accountStatementFileSortFilters, PageFilters pageFilters) throws EntityErrorsException {

        JPAQuery<AccountStatementFilePsql> query = new JPAQueryFactory(entityManager)
            .selectFrom(root);

        buildWhereClauses(statementFileFilters, query);
        applySort(accountStatementFileSortFilters, query, root, AccountStatementFile.SortingFields.class);
        applyPagination(pageFilters, query);

        return query.distinct().fetch().stream()
            .map(AccountStatementFilePsql::toEntity)
            .toList();
    }

    @Override
    public Integer count(AccountStatementFileFilters filters) {

        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
            .select(root.id.count())
            .from(root);

        buildWhereClauses(filters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    private void buildWhereClauses(AccountStatementFileFilters statementFileFilters, JPAQuery<?> query) {

        if (Objects.isNull(statementFileFilters)) {
            return;
        }
        filterByIds(statementFileFilters, query);
        filterByName(statementFileFilters, query);
        filterByProcessingStatus(statementFileFilters, query);
        filterByAccountID(statementFileFilters, query);
        filterByStatementID(statementFileFilters, query);
        filterByCreatedAt(statementFileFilters, query);

    }

    public void filterByProcessingStatus(AccountStatementFileFilters statementFileFilters, JPAQuery<?> query) {

        if (Objects.nonNull(statementFileFilters.getProcessingStatus())) {
            query.where(root.processingStatus.eq(statementFileFilters.getProcessingStatus().name()));
        }
    }

    public void filterByIds(AccountStatementFileFilters statementFileFilters, JPAQuery<?> query) {

        if (!CollectionUtils.isEmpty(statementFileFilters.getIds())) {
            query.where(root.id.in(statementFileFilters.getIds()));
        }
    }

    public void filterByName(AccountStatementFileFilters statementFileFilters, JPAQuery<?> query) {

        if (!Objects.isNull(statementFileFilters.getName())) {
            query.where(root.name.likeIgnoreCase("%" + statementFileFilters.getName() + "%"));
        }
    }

    public void filterByAccountID(AccountStatementFileFilters statementFileFilters, JPAQuery<?> query) {

        if (!Objects.isNull(statementFileFilters.getAccountId())) {
            query.where(root.statement.account.id.eq(statementFileFilters.getAccountId()));
        }
    }

    public void filterByStatementID(AccountStatementFileFilters statementFileFilters, JPAQuery<?> query) {

        if (!Objects.isNull(statementFileFilters.getStatementId())) {
            query.where(root.statement.id.eq(statementFileFilters.getStatementId()));
        }
    }

    public void filterByCreatedAt(AccountStatementFileFilters statementFileFilters, JPAQuery<?> query) {

        if (Objects.nonNull(statementFileFilters.getCreatedAtFrom()) && Objects.nonNull(statementFileFilters.getCreatedAtTo())) {
            LocalDateTime startTime = statementFileFilters.getCreatedAtFrom().with(LocalTime.MIN);
            LocalDateTime endTime = statementFileFilters.getCreatedAtFrom().with(LocalTime.MAX);
            query.where(root.createdAt.between(startTime, endTime));
        } else if (Objects.nonNull(statementFileFilters.getCreatedAtFrom())) {
            LocalDateTime startTime = statementFileFilters.getCreatedAtFrom().with(LocalTime.MIN);
            query.where(root.createdAt.goe(startTime));
        } else if (Objects.nonNull(statementFileFilters.getCreatedAtTo())) {
            LocalDateTime endTime = statementFileFilters.getCreatedAtTo().with(LocalTime.MAX);
            query.where(root.createdAt.loe(endTime));
        }
    }

}
