package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.*;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.account.Contact;

import java.time.LocalDateTime;
import java.util.Map;

@Setter
@Entity
@Audited
@NoArgsConstructor
@Table(name = "contact")
public class ContactPsql extends BaseEntityFieldMap<Contact.SortingFields> {

    @Id
    @SequenceGenerator(name = "contact_id_seq", sequenceName = "contact_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "contact_sequence_id_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "contact_type", nullable = false)
    private String contactType;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "email", nullable = false)
    private String email;

    @Column(name = "mobile_phone_number", nullable = false)
    private String mobilePhoneNumber;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "bank_account_id", nullable = false)
    private AccountPsql account;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "created_by", updatable = false)
    private String createdBy;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    public ContactPsql(Contact contact) {
        this.id = contact.getId();
        this.contactType = contact.getContactType();
        this.name = contact.getName();
        this.email = contact.getEmail();
        this.mobilePhoneNumber = contact.getMobilePhoneNumber();
        this.account = new AccountPsql(contact.getAccount());
        this.createdBy = contact.getCreatedBy() == null
                ? RequestContext.getUsername()
                : contact.getCreatedBy();
        this.createdAt = contact.getCreatedAt() == null
                ? LocalDateTime.now()
                : contact.getCreatedAt();
        this.updatedBy = contact.getUpdatedBy() == null
                ? RequestContext.getUsername()
                : contact.getUpdatedBy();
        this.updatedAt = contact.getUpdatedAt() == null
                ? LocalDateTime.now()
                : contact.getUpdatedAt();

    }

    public Contact toEntity(){
        return Contact.builder()
                .id(this.id)
                .contactType(this.contactType)
                .name(this.name)
                .email(this.email)
                .mobilePhoneNumber(this.mobilePhoneNumber)
                .account(this.account.toEntity())
                .createdAt(this.createdAt)
                .createdBy(this.createdBy)
                .updatedAt(this.updatedAt)
                .updatedBy(this.updatedBy)
                .build();
    }

    static {
        Map<Contact.SortingFields, String> entityFields = Map.of(
                Contact.SortingFields.ID, "id",
                Contact.SortingFields.CONTACT_TYPE, "contactType",
                Contact.SortingFields.NAME, "name",
                Contact.SortingFields.EMAIL, "email",
                Contact.SortingFields.MOBILE_PHONE_NUMBER, "mobilePhoneNumber",
                Contact.SortingFields.CREATED_AT, "createdAt",
                Contact.SortingFields.CREATED_BY, "createdBy",
                Contact.SortingFields.UPDATED_AT, "updatedAt",
                Contact.SortingFields.UPDATED_BY, "updatedBy"
        );
        addEntityFieldMap(Contact.SortingFields.class, entityFields);
    }

}
