package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import pt.jumia.services.brad.data.brad.entities.AccountDailySummaryPsql;
import pt.jumia.services.brad.data.brad.entities.keys.AccountDailySummaryId;

public interface JpaAccountDailySummaryRepository extends JpaRepository<AccountDailySummaryPsql, AccountDailySummaryId>,
    JpaSpecificationExecutor<AccountDailySummaryPsql> {

}
