package pt.jumia.services.brad.data.brad.audit;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.hibernate.envers.RevisionType;
import pt.jumia.services.brad.domain.entities.AuditedEntity;

import java.util.Objects;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RevisionTypeConverter {

    public static AuditedEntity.OperationType toOperationType(RevisionType type) {
        if (Objects.isNull(type)) {
            return null;
        }
        switch (type) {
            case ADD:
                return AuditedEntity.OperationType.CREATE;
            case MOD:
                return AuditedEntity.OperationType.UPDATE;
            case DEL:
                return AuditedEntity.OperationType.DELETE;
            default:
                return null;
        }
    }

}
