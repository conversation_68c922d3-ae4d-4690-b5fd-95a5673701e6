package pt.jumia.services.brad.data.bale.configuration;

import org.hibernate.EmptyInterceptor;
import pt.jumia.services.brad.data.shared.ViewContext;
import pt.jumia.services.brad.domain.entities.ViewEntity;

import java.io.Serial;
import java.util.regex.Matcher;

public class <PERSON><PERSON>Interceptor extends EmptyInterceptor {
    @Serial
    private static final long serialVersionUID = 112452346531252L;
    private static final String SCHEMA = "BRAD";
    private static final String REGEX = String.format("(%s.)[^\\s]+", SCHEMA);

    @Override
    public String onPrepareStatement(String sql) {
        ViewEntity context = ViewContext.getCurrentContext();
        if (context == null) {
            throw new IllegalStateException("ViewContext not initialized for current thread. " +
                    "Ensure ViewContext.setCurrentContext() is called before database operations.");
        }
        return sql.replaceFirst(REGEX, Matcher.quoteReplacement(
                String.format("%s.[%s]", context.getSchemaName(), context.getViewName())));
    }
}
