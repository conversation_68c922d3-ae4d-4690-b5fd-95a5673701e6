package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.BalePsql;
import pt.jumia.services.brad.data.brad.entities.TransactionPsql;
import pt.jumia.services.brad.data.brad.entities.reconciliation.QReconciliationPsql;
import pt.jumia.services.brad.data.brad.entities.reconciliation.ReconciliationPsql;
import pt.jumia.services.brad.data.brad.entities.reconciliation.bale.QReconciliationBalePsql;
import pt.jumia.services.brad.data.brad.entities.reconciliation.bale.ReconciliationBalePsql;
import pt.jumia.services.brad.data.brad.entities.reconciliation.transaction.QReconciliationTransactionPsql;
import pt.jumia.services.brad.data.brad.entities.reconciliation.transaction.ReconciliationTransactionPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.*;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.reconciliation.ReconciliationFilters;
import pt.jumia.services.brad.domain.entities.filter.reconciliation.ReconciliationSortFilters;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;
import pt.jumia.services.brad.domain.enumerations.ReconciliationStatus;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.ReconciliationRepository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@Slf4j
@RequiredArgsConstructor
public class PsqlReconciliationRepository extends BaseRepository implements ReconciliationRepository {

    private final QReconciliationPsql root = new QReconciliationPsql("root");

    private final QReconciliationBalePsql reconciliationBalePsqlRoot =
            new QReconciliationBalePsql("reconciliationBalePsql");
    private final QReconciliationTransactionPsql reconciliationTransactionPsqlRoot =
            new QReconciliationTransactionPsql("reconciliationTransactionPsql");

    private final JpaReconciliationRepository jpaReconciliationRepository;

    private final JpaReconciliationBaleRepository jpaReconciliationBaleRepository;
    private final JpaReconciliationTransactionRepository jpaReconciliationTransactionRepository;

    private final JpaBaleRepository jpaBaleRepository;
    private final JpaTransactionRepository jpaTransactionRepository;

    private final EntityManager entityManager;

    @Override
    @Transactional
    public Reconciliation insert(Reconciliation reconciliation,
                                 BigDecimal amountTransaction,
                                 BigDecimal amountBale,
                                 BigDecimal amountThreshold,
                                 Account account) {

        ReconciliationPsql savedReconciliation = jpaReconciliationRepository.save(
                new ReconciliationPsql(reconciliation.toBuilder()
                        .amountTransaction(amountTransaction)
                        .amountBale(amountBale)
                        .amountThreshold(amountThreshold)
                        .build(),
                        new HashSet<>(),
                        new HashSet<>())
        );


        List<ReconciliationBalePsql> reconciliationBalePsqlList = new ArrayList<>();

        for (Long bale : reconciliation.getBaleIds()) {
            reconciliationBalePsqlList.add(
                    new ReconciliationBalePsql(savedReconciliation,
                            jpaBaleRepository.getReferenceById(bale)
                    )
            );
        }

        List<ReconciliationTransactionPsql> reconciliationTransactionPsqlList = new ArrayList<>();

        for (Long transactionId : reconciliation.getTransactionIds()) {
            reconciliationTransactionPsqlList.add(
                    new ReconciliationTransactionPsql(
                            savedReconciliation,
                            jpaTransactionRepository.getReferenceById(transactionId),
                            account.getCompanyID()
                    )
            );
        }

        jpaReconciliationTransactionRepository.saveAll(reconciliationTransactionPsqlList);
        jpaReconciliationBaleRepository.saveAll(reconciliationBalePsqlList);

        return savedReconciliation.toEntityWithList(reconciliation.getBaleIds(), reconciliation.getTransactionIds(), null);
    }

    @Override
    @Transactional
    public List<Reconciliation> findAll(
            ReconciliationFilters reconciliationFilters,
            ReconciliationSortFilters reconciliationSortFilters,
            PageFilters pageFilters) throws EntityErrorsException {

        JPAQuery<ReconciliationPsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root)
                .distinct()
                .leftJoin(root.balePsqlList, reconciliationBalePsqlRoot)
                .leftJoin(root.transactionPsqlList, reconciliationTransactionPsqlRoot);



        buildWhereClauses(reconciliationFilters, query);
        applySort(reconciliationSortFilters, query, root, Reconciliation.SortingFields.class);
        applyPagination(pageFilters, query);

        List<ReconciliationPsql> reconciliationPsqlList = query.fetch();

        List<Reconciliation> reconciliationList = new ArrayList<>();
        for (ReconciliationPsql reconciliationPsql : reconciliationPsqlList) {
            reconciliationList.add(fetch(reconciliationPsql));
        }

        return reconciliationList;

    }

    @Override
    @Transactional
    public Optional<Reconciliation> findById(Integer id) {
        JPAQuery<ReconciliationPsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root)
                .distinct()
                .leftJoin(root.balePsqlList, reconciliationBalePsqlRoot);

        ReconciliationPsql reconciliationPsql = query.where(root.id.eq(id)).fetchOne();
        if (Objects.isNull(reconciliationPsql)) {
            return Optional.empty();
        }

        return Optional.ofNullable(fetch(reconciliationPsql));
    }

    @Override
    @Transactional
    public Optional<Reconciliation> findReconciliationByTransactionId(Long transactionId) {
        ReconciliationPsql reconciliationPsql = new JPAQuery<>(entityManager)
                .select(reconciliationTransactionPsqlRoot.reconciliation)
                .from(reconciliationTransactionPsqlRoot)
                .where(reconciliationTransactionPsqlRoot.transactionPsql.id.eq(transactionId))
                .where(reconciliationTransactionPsqlRoot.reconciliation.status.ne(ReconciliationStatus.REJECTED.name()))
                .fetchOne();

        if (Objects.isNull(reconciliationPsql)) {
            return Optional.empty();
        }

        Reconciliation reconciliation = fetch(reconciliationPsql);

        return Optional.of(reconciliation);
    }

    @Override
    public Optional<Reconciliation> findReconciliationByBaleId(Long baleId) {
        ReconciliationPsql reconciliationPsql = new JPAQuery<>(entityManager)
                .select(reconciliationBalePsqlRoot.reconciliation)
                .from(reconciliationBalePsqlRoot)
                .where(reconciliationBalePsqlRoot.balePsql.id.eq(baleId))
                .where(reconciliationBalePsqlRoot.reconciliation.status.ne(ReconciliationStatus.REJECTED.name()))
                .fetchOne();

        if (Objects.isNull(reconciliationPsql)) {
            return Optional.empty();
        }

        Reconciliation reconciliation = fetch(reconciliationPsql);

        return Optional.of(reconciliation);

    }

    private Reconciliation fetch(ReconciliationPsql reconciliationPsql) {
        Account account = reconciliationPsql.getBalePsqlList().stream()
                .map(reconciliationBalePsql -> reconciliationBalePsql.getBalePsql().toEntity().getAccount())
                .findFirst().orElse(null);

        List<Long> transactionIds = reconciliationPsql.getTransactionPsqlList().stream()
                .map(reconciliationFinrecPsql -> reconciliationFinrecPsql.getTransactionPsql().toEntity().getId())
                .collect(Collectors.toList());

        List<Long> baleEntryNoList = reconciliationPsql.getBalePsqlList().stream()
                .map(reconciliationBalePsql -> reconciliationBalePsql.getBalePsql().toEntity().getId())
                .collect(Collectors.toList());

        return reconciliationPsql.toEntityWithList(baleEntryNoList, transactionIds, account);
    }

    @Override
    public long count(ReconciliationFilters reconciliationFilters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(reconciliationFilters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    @Override
    @Transactional
    public Reconciliation approve(Reconciliation reconciliation) {

        List<Bale> baleList = new ArrayList<>();
        List<Transaction> transactionList = new ArrayList<>();

        List<ReconciliationBalePsql> reconciliationBalePsqlList = new ArrayList<>();
        List<ReconciliationTransactionPsql> reconciliationTransactionPsqlList = new ArrayList<>();

        fetchReconciliationBaleAndTransaction(reconciliation, baleList, transactionList,
                reconciliationBalePsqlList, reconciliationTransactionPsqlList);


        List<BalePsql> balePsqlList = baleList.stream().map(bale -> BalePsql.fromEntity(
                bale.toBuilder()
                        .reconcileStatus(ReconcileStatus.RECONCILED)
                        .build()
        )).toList();

        List<TransactionPsql> transactionPsqlList = transactionList.stream().map(transaction -> TransactionPsql.fromEntity(
                transaction.toBuilder()
                        .reconcileStatus(ReconcileStatus.RECONCILED)
                        .build()
        )).toList();


        jpaBaleRepository.saveAll(balePsqlList);
        jpaTransactionRepository.saveAll(transactionPsqlList);

        Set<ReconciliationBalePsql> reconciliationBalePsqlSet = new HashSet<>(reconciliationBalePsqlList);
        Set<ReconciliationTransactionPsql> reconciliationTransactionPsqlSet = new HashSet<>(reconciliationTransactionPsqlList);

        return jpaReconciliationRepository.save(new ReconciliationPsql(reconciliation.toBuilder()
                .status(ReconciliationStatus.APPROVED)
                .build(),
                reconciliationTransactionPsqlSet,
                reconciliationBalePsqlSet
        )).toEntity();

    }

    @Override
    @Transactional
    public void unmatch(Reconciliation reconciliation) {

        List<Bale> baleList = new ArrayList<>();
        List<Transaction> transactionList = new ArrayList<>();

        fetchReconciliationBaleAndTransaction(reconciliation, baleList, transactionList,
                new ArrayList<>(), new ArrayList<>());

        List<BalePsql> balePsqlList = baleList.stream().map(bale -> BalePsql.fromEntity(
                bale.toBuilder()
                        .reconcileStatus(ReconcileStatus.NOT_RECONCILED)
                        .build()
        )).toList();

        List<TransactionPsql> transactionPsqlList = transactionList.stream().map(transaction -> TransactionPsql.fromEntity(
                transaction.toBuilder()
                        .reconcileStatus(ReconcileStatus.NOT_RECONCILED)
                        .build()
        )).toList();


        jpaBaleRepository.saveAll(balePsqlList);
        jpaTransactionRepository.saveAll(transactionPsqlList);

        jpaReconciliationRepository.save(new ReconciliationPsql(reconciliation.toBuilder()
                .status(ReconciliationStatus.REJECTED)
                .build(),
                null,
                null)
        );
    }

    private void fetchReconciliationBaleAndTransaction(Reconciliation reconciliation,
                                                       List<Bale> baleList,
                                                       List<Transaction> transactionList,
                                                       List<ReconciliationBalePsql> reconciliationBalePsqlList,
                                                       List<ReconciliationTransactionPsql> reconciliationTransactionPsqlList) {
        reconciliationBalePsqlList.addAll(new JPAQuery<>(entityManager)
                .select(reconciliationBalePsqlRoot)
                .from(reconciliationBalePsqlRoot)
                .where(reconciliationBalePsqlRoot.reconciliation.id.eq(reconciliation.getId()))
                .fetch());

        reconciliationTransactionPsqlList.addAll(new JPAQuery<>(entityManager)
                .select(reconciliationTransactionPsqlRoot)
                .from(reconciliationTransactionPsqlRoot)
                .where(reconciliationTransactionPsqlRoot.reconciliation.id.eq(reconciliation.getId()))
                .fetch());

        for (ReconciliationBalePsql reconciliationBalePsql : reconciliationBalePsqlList) {
            baleList.add(reconciliationBalePsql.getBalePsql().toEntity());
        }
        for (ReconciliationTransactionPsql reconciliationTransactionPsql : reconciliationTransactionPsqlList) {
            transactionList.add(reconciliationTransactionPsql.getTransactionPsql().toEntity());
        }
    }

    @Override
    @Transactional
    public void updateEntitiesToPendingApproval(Reconciliation entity) {
        jpaBaleRepository.updateBalesToPendingApproval(entity.getBaleIds());
        jpaTransactionRepository.updateTransactionsToPendingApproval(entity.getTransactionIds());
    }

    @Override
    @Transactional
    public void delete(Integer id) {

        List<ReconciliationBalePsql> reconciliationBalePsqlList = new JPAQuery<>(entityManager)
                .select(reconciliationBalePsqlRoot)
                .from(reconciliationBalePsqlRoot)
                .where(reconciliationBalePsqlRoot.reconciliation.id.eq(id))
                .fetch();

        List <ReconciliationTransactionPsql> reconciliationTransactionPsqlList = new JPAQuery<>(entityManager)
                .select(reconciliationTransactionPsqlRoot)
                .from(reconciliationTransactionPsqlRoot)
                .where(reconciliationTransactionPsqlRoot.reconciliation.id.eq(id))
                .fetch();

        jpaReconciliationBaleRepository.deleteAll(reconciliationBalePsqlList);
        jpaReconciliationTransactionRepository.deleteAll(reconciliationTransactionPsqlList);
        jpaReconciliationRepository.deleteById(id);

    }

    private void buildWhereClauses(ReconciliationFilters reconciliationFilters, JPAQuery<?> query) {
        if (Objects.isNull(reconciliationFilters)) {
            return;
        }

        filterById(reconciliationFilters, query);
        filterByStatus(reconciliationFilters, query);
        filterByCreator(reconciliationFilters, query);
        filterByCreationDate(reconciliationFilters, query);
        filterByReviewer(reconciliationFilters, query);
        filterByReviewDate(reconciliationFilters, query);
        filterByAmountTransaction(reconciliationFilters, query);
        filterByAmountBale(reconciliationFilters, query);
        filterByAmountThreshold(reconciliationFilters, query);

    }



    private void filterById(ReconciliationFilters reconciliationFilters, JPAQuery<?> query) {
        if (!Objects.isNull(reconciliationFilters.getId())) {
            query.where(root.id.eq(reconciliationFilters.getId()));
        }
    }

    private void filterByStatus(ReconciliationFilters reconciliationFilters, JPAQuery<?> query) {
        if (!Objects.isNull(reconciliationFilters.getStatus())) {
            query.where(root.status.eq(reconciliationFilters.getStatus().name()));
        }
    }

    private void filterByCreator(ReconciliationFilters reconciliationFilters, JPAQuery<?> query) {
        if (!Objects.isNull(reconciliationFilters.getCreator())) {
            query.where(root.creator.eq(reconciliationFilters.getCreator()));
        }
    }

    private void filterByCreationDate(ReconciliationFilters reconciliationFilters, JPAQuery<?> query) {
        if (!Objects.isNull(reconciliationFilters.getCreationDateStart())) {
            LocalDateTime startDate = reconciliationFilters.getCreationDateStart().atStartOfDay();
            LocalDateTime endDate = Objects.isNull(reconciliationFilters.getCreationDateEnd()) ? startDate :
                    reconciliationFilters.getCreationDateEnd().atTime(23, 59, 59);
            query.where(root.creationDate.between(startDate, endDate));
        }
    }

    private void filterByReviewer(ReconciliationFilters reconciliationFilters, JPAQuery<?> query) {
        if (!Objects.isNull(reconciliationFilters.getReviewer())) {
            query.where(root.reviewer.eq(reconciliationFilters.getReviewer()));
        }
    }

    private void filterByReviewDate(ReconciliationFilters reconciliationFilters, JPAQuery<?> query) {
        if (!Objects.isNull(reconciliationFilters.getReviewDateStart())) {
            LocalDateTime startDate = reconciliationFilters.getReviewDateStart().atStartOfDay();
            LocalDateTime endDate = Objects.isNull(reconciliationFilters.getReviewDateEnd()) ? startDate :
                    reconciliationFilters.getReviewDateEnd().atTime(23, 59, 59);
            query.where(root.reviewDate.between(startDate, endDate));
        }
    }

    private void filterByAmountTransaction(ReconciliationFilters reconciliationFilters, JPAQuery<?> query) {
        if (!Objects.isNull(reconciliationFilters.getAmountTransaction())) {
            query.where(root.amountTransaction.eq(reconciliationFilters.getAmountTransaction()));
        }
    }

    private void filterByAmountBale(ReconciliationFilters reconciliationFilters, JPAQuery<?> query) {
        if (!Objects.isNull(reconciliationFilters.getAmountBale())) {
            query.where(root.amountBale.eq(reconciliationFilters.getAmountBale()));
        }
    }

    private void filterByAmountThreshold(ReconciliationFilters reconciliationFilters, JPAQuery<?> query) {
        if (!Objects.isNull(reconciliationFilters.getAmountThreshold())) {
            query.where(root.amountThreshold.eq(reconciliationFilters.getAmountThreshold()));
        }
    }
}
