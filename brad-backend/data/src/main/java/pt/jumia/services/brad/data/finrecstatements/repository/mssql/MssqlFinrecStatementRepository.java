package pt.jumia.services.brad.data.finrecstatements.repository.mssql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import pt.jumia.services.brad.data.brad.repository.psql.PsqlExecutionLogRepository;
import pt.jumia.services.brad.data.finrecstatements.entities.FinrecStatementMssql;
import pt.jumia.services.brad.data.finrecstatements.entities.QFinrecStatementMssql;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.FinrecStatement;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.repository.FinrecStatementRepository;
import pt.jumia.services.brad.domain.usecases.FindExceptionRootCauseUseCase;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Slf4j
@Repository
public class MssqlFinrecStatementRepository implements FinrecStatementRepository {

    private final QFinrecStatementMssql root = new QFinrecStatementMssql("root");
    private final PsqlExecutionLogRepository psqlExecutionLogRepository;

    private final EntityManager entityManager;

    @SuppressWarnings({"PMD"})
    @SuppressFBWarnings("EI_EXPOSE_REP2")
    public MssqlFinrecStatementRepository(@Qualifier("finrecStatementsEntityManager") EntityManager entityManager,
                                          PsqlExecutionLogRepository psqlExecutionLogRepository) {
        this.psqlExecutionLogRepository = psqlExecutionLogRepository;
        EntityManagerFactory entityManagerFactory = entityManager.getEntityManagerFactory();
        this.entityManager = entityManagerFactory.createEntityManager();
    }


    @Override
    public List<FinrecStatement> findAll(LocalDateTime timestamp, ExecutionLog executionLog) throws DatabaseErrorsException {
        try {

            JPAQuery<FinrecStatementMssql> query = new JPAQueryFactory(entityManager)
                    .select(root)
                    .from(root);


            if (!Objects.isNull(timestamp)) {
                filterByTimestamp(timestamp, query);
            }

            query.orderBy(root.initialDate.asc(), root.finalDate.asc(), root.accountNumber.asc());

            LocalDateTime startTime = LocalDateTime.now();
            List<FinrecStatementMssql> finrecStatementMssqls = query.fetch();
            LocalDateTime endTime = LocalDateTime.now();

            executionLog = executionLog.toBuilder()
                    .recordsAmount(finrecStatementMssqls.size())
                    .executionStartTime(startTime)
                    .executionEndTime(endTime)
                    .appliedFilters("timestamp: " + timestamp)
                    .query(query.toString())
                    .logStatus(ExecutionLog.ExecutionLogStatus.FETCHED)
                    .build();

            try {
                psqlExecutionLogRepository.upsert(executionLog);
            } catch (Exception e) {
                log.info("Error updating executionLog: {}", e.getMessage());
            }

            return finrecStatementMssqls.stream().map(FinrecStatementMssql::toEntity).toList();
        } catch (Exception e) {

            Throwable ex = FindExceptionRootCauseUseCase.findRootCause(e);
            log.error(ex.getMessage());
            throw DatabaseErrorsException.createDatabaseUnavailable("Finrec Statements");
        }

    }

    private void filterByTimestamp(LocalDateTime timestamp, JPAQuery<FinrecStatementMssql> query) {
        if (!Objects.isNull(timestamp)) {
            Timestamp timestampSql = Timestamp.valueOf(timestamp);
            query.where(root.timestampRunAt.gt(timestampSql));
        }
    }
}
