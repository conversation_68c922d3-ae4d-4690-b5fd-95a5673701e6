package pt.jumia.services.brad.data.brad.job;

import org.jetbrains.annotations.NotNull;
import org.quartz.JobExecutionContext;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import pt.jumia.services.brad.domain.usecases.fxrates.ReadFxRateUseCase;

import java.util.List;


public class FxRateSyncJob extends QuartzJobBean {

    @Override
    protected void executeInternal(@NotNull JobExecutionContext jobExecutionContext){
        try {
            ApplicationContext applicationContext = (ApplicationContext)
                    jobExecutionContext.getScheduler().getContext().get("applicationContext");

            ReadFxRateUseCase readFxRateUseCase = applicationContext.getBean(ReadFxRateUseCase.class);

            readFxRateUseCase.executeByCurrency(List.of());

        } catch (Exception e){
            e.printStackTrace();
        }
    }
}
