package pt.jumia.services.brad.data.finrecstatements.configuration;

import com.zaxxer.hikari.HikariConfig;
import jakarta.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.dialect.SQLServer2016Dialect;
import org.hibernate.jpa.HibernatePersistenceProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.Database;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import pt.jumia.services.brad.data.brad.configuration.RetryableDatasource;
import pt.jumia.services.brad.domain.properties.DataProperties;

import javax.sql.DataSource;
import java.util.Properties;

@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableTransactionManagement(proxyTargetClass = true)
public class FinrecStatementsEntityManagerConfiguration {

    private final DataProperties dataProperties;


    @Bean(name = "finrecStatementsTransactionManager")
    public JpaTransactionManager transactionManager(@Qualifier("finrecStatementsEntityManager") EntityManagerFactory emf) {
        return new JpaTransactionManager(emf);
    }

    @Bean(name = "finrecStatementsEntityManager")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory() {



        LocalContainerEntityManagerFactoryBean fact = new LocalContainerEntityManagerFactoryBean();
        fact.setPackagesToScan("pt.jumia.services.brad.data.finrecstatements.entities");
        fact.setDataSource(dataSource());

        HibernateJpaVendorAdapter vendor = new HibernateJpaVendorAdapter();
        vendor.setDatabase(Database.SQL_SERVER);

        fact.setJpaVendorAdapter(vendor);
        fact.setPersistenceProvider(new HibernatePersistenceProvider());

        final Properties properties = new Properties();
        additionalProperties(properties);
        fact.setJpaProperties(properties);

        return fact;
    }

    @Bean(name = "finrecStatementsDataSource")
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();
        config.setDriverClassName(dataProperties.getFinrecStatements().getDriver());
        config.setJdbcUrl(dataProperties.getFinrecStatements().getUrl());
        config.setUsername(dataProperties.getFinrecStatements().getUsername());
        config.setPassword(dataProperties.getFinrecStatements().getPassword());
        config.setMaximumPoolSize(dataProperties.getFinrecStatements().getMaxPoolSize());
        return new RetryableDatasource(config);
    }

    private void additionalProperties(Properties properties) {
        properties.put("hibernate.dialect", SQLServer2016Dialect.class.getName());
        properties.put("hibernate.default_schema", dataProperties.getFinrecStatements().getApplicationSchema());
        properties.put("hibernate.show_sql", "false");
    }


}
