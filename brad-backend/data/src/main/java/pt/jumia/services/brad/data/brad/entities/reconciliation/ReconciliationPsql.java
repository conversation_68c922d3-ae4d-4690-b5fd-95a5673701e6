package pt.jumia.services.brad.data.brad.entities.reconciliation;

import jakarta.persistence.*;
import lombok.Getter;
import pt.jumia.services.brad.data.brad.entities.reconciliation.bale.ReconciliationBalePsql;
import pt.jumia.services.brad.data.brad.entities.reconciliation.transaction.ReconciliationTransactionPsql;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.enumerations.ReconciliationStatus;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@Getter
@Entity
@Table(name = "RECONCILIATIONS")
public class ReconciliationPsql extends BaseEntityFieldMap<Reconciliation.SortingFields> implements Serializable {

    @Serial
    private static final long serialVersionUID = 71835928768937653L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "AMOUNT_TRANSACTION")
    private BigDecimal amountTransaction;

    @Column(name = "AMOUNT_BALE")
    private BigDecimal amountBale;

    @Column(name = "AMOUNT_THRESHOLD")
    private BigDecimal amountThreshold;

    @Column(name = "CREATOR")
    private String creator;

    @Column(name = "CREATION_DATE")
    private LocalDateTime creationDate;

    @Column(name = "REVIEWER")
    private String reviewer;

    @Column(name = "REVIEW_DATE")
    private LocalDateTime reviewDate;

    @Column(name = "ID_COMPANY")
    private String idCompany;

    @Getter
    @OneToMany(fetch = FetchType.EAGER, mappedBy = "reconciliation")
    private Set<ReconciliationTransactionPsql> transactionPsqlList;

    @Getter
    @OneToMany(fetch = FetchType.EAGER, mappedBy = "reconciliation")
    private Set<ReconciliationBalePsql> balePsqlList;

    public ReconciliationPsql() {
    }

    public ReconciliationPsql(Reconciliation reconciliation,
                              Set<ReconciliationTransactionPsql> reconciliationTransactionPsqls,
                              Set<ReconciliationBalePsql> reconciliationBalePsqls) {
        this.id = reconciliation.getId();
        this.status = reconciliation.getStatus().toString();
        this.amountTransaction = reconciliation.getAmountTransaction();
        this.amountBale = reconciliation.getAmountBale();
        this.amountThreshold = reconciliation.getAmountThreshold();
        this.creator =   Objects.isNull(reconciliation.getCreator()) ?
                RequestContext.getUsername() : reconciliation.getCreator();
        this.creationDate = reconciliation.getCreationDate();
        this.reviewer = reconciliation.getReviewer();
        this.reviewDate = reconciliation.getReviewDate();
        this.idCompany = reconciliation.getIdCompany();
        if (Objects.nonNull(reconciliationTransactionPsqls)) {
            this.transactionPsqlList = new HashSet<>(reconciliationTransactionPsqls);
        }
        if (Objects.nonNull(reconciliationBalePsqls)) {
            this.balePsqlList = new HashSet<>(reconciliationBalePsqls);
        }
    }
    public Reconciliation toEntity(){
        return Reconciliation
                .builder()
                .id(id)
                .status(ReconciliationStatus.valueOf(status))
                .amountTransaction(amountTransaction)
                .amountBale(amountBale)
                .amountThreshold(amountThreshold)
                .creator(creator)
                .creationDate(creationDate)
                .reviewer(reviewer)
                .reviewDate(reviewDate)
                .idCompany(idCompany)
                .build();
    }

    public Reconciliation toEntityWithList(List<Long> baleEntryNoList, List<Long> transactionIds, Account account) {
        return toEntity().toBuilder()
                .baleIds(baleEntryNoList)
                .transactionIds(transactionIds)
                .account(account)
                .build();
    }

    static {
        Map<Reconciliation.SortingFields, String> entityFields = Map.ofEntries(
                Map.entry(Reconciliation.SortingFields.ID, "id"),
                Map.entry(Reconciliation.SortingFields.STATUS, "status"),
                Map.entry(Reconciliation.SortingFields.CREATOR, "creator"),
                Map.entry(Reconciliation.SortingFields.CREATION_DATE, "creationDate"),
                Map.entry(Reconciliation.SortingFields.REVIEWER, "reviewer"),
                Map.entry(Reconciliation.SortingFields.REVIEW_DATE, "reviewDate"),
                Map.entry(Reconciliation.SortingFields.AMOUNT_TRANSACTION, "amountTransaction"),
                Map.entry(Reconciliation.SortingFields.AMOUNT_BALE, "amountBale"),
                Map.entry(Reconciliation.SortingFields.AMOUNT_THRESHOLD, "amountThreshold")
        );
        BaseEntityFieldMap.addEntityFieldMap(Reconciliation.SortingFields.class, entityFields);
    }

}
