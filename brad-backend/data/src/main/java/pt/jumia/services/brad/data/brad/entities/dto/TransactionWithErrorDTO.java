package pt.jumia.services.brad.data.brad.entities.dto;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.data.brad.entities.TransactionPsql;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@NoArgsConstructor
@AllArgsConstructor
public class TransactionWithErrorDTO extends TransactionPsql implements Serializable {

    @Serial
    private static final long serialVersionUID = 8246644563465L;

    private AccountStatementStatus statementIsInError;

    @Override
    public Transaction toEntity() {
        return super.toEntity()
                .toBuilder()
                .statementIsInError(!Objects.equals(statementIsInError, AccountStatementStatus.IMPORTED))
                .build();
    }
}
