package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinColumns;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import lombok.NoArgsConstructor;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.SortingFields;


@NoArgsConstructor(force = true)
@Entity
@Audited
@Table(name = "bank_statement_file")
public class AccountStatementFilePsql extends BaseEntityFieldMap<SortingFields> {

    static final String FILE = "file";

    @Id
    @SequenceGenerator(name = "bank_statement_file_id_seq_gen", sequenceName = "bank_statement_file_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "bank_statement_file_id_seq_gen")
    @Column(name = "id")
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "url", nullable = false)
    private String url;

    @Column(name = "processing_status", nullable = false)
    private String processingStatus;

    @Column(name = "status_description", nullable = true)
    private String statusDescription;

    @Column(name = "checksum")
    private String checksum;

    @NotAudited
    @OneToOne(optional = false)
    @JoinColumn(name = "fk_execution_log", nullable = false)
    private ExecutionLogPsql executionLog;

    @NotAudited
    @OneToOne()
    @JoinColumns({
        @JoinColumn(name = "statement_id", referencedColumnName = "id"),
        @JoinColumn(name = "partition_key", referencedColumnName = "partition_key")
    })    private AccountStatementPsql statement;

    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    static {
        Map<AccountStatementFile.SortingFields, String> entityFields = Map.of(
            AccountStatementFile.SortingFields.ID, "id",
            AccountStatementFile.SortingFields.CREATED_AT, "createdAt",
            AccountStatementFile.SortingFields.UPDATED_AT, "updatedAt"
        );
        addEntityFieldMap(AccountStatementFile.SortingFields.class, entityFields);
    }

    private AccountStatementFilePsql(AccountStatementFile accountStatementFile) {

        this.id = accountStatementFile.getId();
        this.name = accountStatementFile.getName();
        this.url = accountStatementFile.getUrl();
        this.processingStatus = accountStatementFile.getProcessingStatus().name();
        this.statusDescription = accountStatementFile.getStatusDescription();
        this.checksum = accountStatementFile.getChecksum();
        this.executionLog = new ExecutionLogPsql(accountStatementFile.getExecutionLog());
        this.statement = accountStatementFile.getStatement() == null ? null : new AccountStatementPsql(accountStatementFile.getStatement());
        this.createdBy = getCreateUser(accountStatementFile);
        this.updatedBy = getUpdateUser(accountStatementFile);
        this.createdAt = Objects.isNull(accountStatementFile.getCreatedAt()) ? LocalDateTime.now()
            : accountStatementFile.getCreatedAt();
        this.updatedAt = Objects.isNull(accountStatementFile.getUpdatedAt()) ? LocalDateTime.now()
            : accountStatementFile.getUpdatedAt();

    }

    public static AccountStatementFilePsql fromEntity(AccountStatementFile accountStatementFile) {

        return new AccountStatementFilePsql(accountStatementFile);
    }

    public AccountStatementFile toEntity() {

        return AccountStatementFile.builder()
            .id(id)
            .name(this.name)
            .url(this.url)
            .processingStatus(ProcessingStatus.valueOf(this.processingStatus))
            .statusDescription(this.statusDescription)
            .checksum(this.checksum)
            .executionLog(this.executionLog.toEntity())
            .statement(this.statement == null ? null : this.statement.toEntity())
            .createdAt(this.createdAt)
            .createdBy(this.createdBy)
            .updatedAt(this.updatedAt)
            .updatedBy(this.updatedBy)
            .build();
    }

    private static String getUpdateUser(final AccountStatementFile accountStatementFile) {

        final String user = RequestContext.getUsername() == null ? "System" : RequestContext.getUsername();

        return accountStatementFile.getUpdatedBy() == null
            ? user
            : accountStatementFile.getUpdatedBy();
    }

    private static String getCreateUser(final AccountStatementFile accountStatementFile) {

        final String user = RequestContext.getUsername() == null ? "System" : RequestContext.getUsername();

        return accountStatementFile.getCreatedBy() == null
            ? user
            : accountStatementFile.getCreatedBy();
    }


}
