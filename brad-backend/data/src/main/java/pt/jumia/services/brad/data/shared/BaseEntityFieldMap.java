package pt.jumia.services.brad.data.shared;

import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.util.HashMap;
import java.util.Map;

public abstract class BaseEntityFieldMap<E extends Enum<E>> {

    private static final Map<Class<?>, Map<Enum<?>, String>> ENTITY_FIELDS = new HashMap<>();

    protected static <E extends Enum<E>> void addEntityFieldMap(Class<?> clazz, Map<E, String> entityFields) {
        ENTITY_FIELDS.computeIfAbsent(clazz, k -> new HashMap<>()).putAll(entityFields);
    }

    protected static <E extends Enum<E>> String getEntityField(Class<?> clazz, Enum<?> field) throws EntityErrorsException {
        Map<Enum<?>, String> fields = ENTITY_FIELDS.get(clazz);
        String entityField = fields != null ? fields.get(field) : null;
        if (entityField == null) {
            throw EntityErrorsException.createSortError(field.name(), clazz, fields != null ? fields.keySet().toString() : "[]");
        }
        return fields.get(field);
    }

    public static <E extends Enum<E>> Map<E, String> getEntityFields(Class<?> clazz) {
        Map<Enum<?>, String> fields = ENTITY_FIELDS.get(clazz);
        Map<E, String> result = new HashMap<>();
        return fields != null ? (Map<E, String>) fields : result;
    }
}
