package pt.jumia.services.brad.data.brad.repository.psql.audit;

import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.jpa.impl.JPAQuery;
import pt.jumia.services.brad.data.brad.entities.QRevisionPsql;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.audit.AuditRevisionFilters;
import pt.jumia.services.brad.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.brad.domain.entities.filter.audit.BaseAuditFilter;

import java.text.MessageFormat;

public class AuditRepositoryHelper {
    public static void applyGenericFiltersAndSorting(JPAQuery<?> query, QRevisionPsql alias, BaseAuditFilter filter) {
        applyRevisionFilters(query, alias, filter.getRevisionFilters());
        applySort(query, alias, filter.getSortFilter());
        applyPagination(query, filter.getPageFilters());
    }

    public static void applyRevisionFilters(JPAQuery<?> query, QRevisionPsql alias, AuditRevisionFilters filter) {
        if (filter == null) {
            return;
        }

        if (filter.getFrom() != null) {
            query.where(alias.time.goe(filter.getFrom()));
        }

        if (filter.getTo() != null) {
            query.where(alias.time.loe(filter.getTo()));
        }


        if (filter.getChangedBy() != null) {
            query.where(alias.email.eq(filter.getChangedBy()));
        }
    }

    public static void applyPagination(JPAQuery<?> query, PageFilters filter) {
        if (filter != null) {
            query.offset((filter.getPage() - 1) * filter.getSize())
                    .limit(filter.getSize());
        }
    }

    public static void applySort(JPAQuery<?> query, QRevisionPsql alias, AuditedEntitySortFilters sort) {
        if (sort == null) {
            sort = AuditedEntitySortFilters.builder().build(); // Use defaults
        }

        ComparableExpressionBase<?> sortField;
        switch (sort.getField()) {
            case REV:
                sortField = alias.id;
                break;
            case DATE:
                sortField = alias.time;
                break;
            case EMAIL:
                sortField = alias.email;
                break;
            default:
                throw new IllegalArgumentException(MessageFormat.format("Unknown sort field {0}", sort.getField()));
        }

        switch (sort.getDirection()) {
            case ASC:
                query.orderBy(sortField.asc());
                break;
            case DESC:
                query.orderBy(sortField.desc());
                break;
            default:
                throw new IllegalArgumentException(MessageFormat.format("Unknown sort direction {0}", sort.getDirection()));
        }
    }
}
