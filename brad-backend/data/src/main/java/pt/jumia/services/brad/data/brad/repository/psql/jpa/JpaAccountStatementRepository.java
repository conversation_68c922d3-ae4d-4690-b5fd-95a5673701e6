package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import pt.jumia.services.brad.data.brad.entities.AccountStatementPsql;

import java.util.Optional;

public interface JpaAccountStatementRepository extends JpaRepository<AccountStatementPsql, Long>, JpaSpecificationExecutor<AccountStatementPsql> {

    Optional<AccountStatementPsql> findByPreviousStatement(AccountStatementPsql accountStatementPsql);

    @Query("SELECT bs " +
            "FROM AccountStatementPsql bs " +
            "LEFT JOIN AccountStatementPsql bs2 ON " +
            "bs.id = bs2.previousStatement.id WHERE bs2.id IS NULL " +
            "AND bs.partitionKey = :accountId")
    Optional<AccountStatementPsql> findLastStatementInList(@Param("accountId") String accountId);

    Optional<AccountStatementPsql> findFirstByUpdatedByOrderByUpdatedAt(String user);


}
