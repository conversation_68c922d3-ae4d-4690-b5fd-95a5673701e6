package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import pt.jumia.services.brad.data.brad.entities.QUserPsql;
import pt.jumia.services.brad.data.brad.entities.UserPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaUserRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.account.User;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.user.UserFilters;
import pt.jumia.services.brad.domain.entities.filter.user.UserSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.UserRepository;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlUserRepository extends BaseRepository implements UserRepository {

    private final QUserPsql root = new QUserPsql("root");
    private final JpaUserRepository jpaUserRepository;
    private final EntityManager entityManager;


    @Override
    public User upsert(User user) {
        return jpaUserRepository.save(new UserPsql(user)).toEntity();
    }

    @Override
    public Optional<User> findById(long id) {
        return jpaUserRepository.findById(id).map(UserPsql::toEntity);
    }

    @Override
    public List<User> findAll(UserFilters userFilters, UserSortFilters userSortFilters, PageFilters pageFilters) throws EntityErrorsException {
        JPAQuery<UserPsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root);

        if (Objects.nonNull(userFilters)) {
            applyProjectionSelect(userFilters.getSelectedFields(), query, root, UserPsql.class);
        }
        buildWhereClauses(userFilters, query);
        // Custom ordering: Legal Representatives first, then alphabetical ordering by user name.
        query.orderBy(
                new OrderSpecifier(Order.ASC, Expressions
                        .numberTemplate(Integer.class, "case when {0} = 'Legal Representative' then 0 else 1 end",
                                root.permissionType)),
                root.userName.asc()
        );
        applyPagination(pageFilters, query);

        return query.fetch().stream()
                .distinct()
                .map(UserPsql::toEntity)
                .collect(Collectors.toList());
    }


    @Override
    @Transactional
    public void deleteById(long id) {
        jpaUserRepository.deleteById(id);
    }

    @Override
    public Integer count(UserFilters filters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(filters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    private void buildWhereClauses(UserFilters userFilters, JPAQuery<?> query) {
        if (Objects.isNull(userFilters)) {
            return;
        }
        filterByName(userFilters, query);
        filterByEmail(userFilters, query);
        filterByAccountID(userFilters, query);
        filterByAccountIds(userFilters, query);
        filterByCreatedAt(userFilters, query);
        filterByStatus(userFilters, query);

    }

    public void filterByName(UserFilters userFilters, JPAQuery<?> query) {
        if (!Objects.isNull(userFilters.getUserName())) {
            query.where(root.userName.likeIgnoreCase("%" + userFilters.getUserName() + "%"));
        }
    }

    public void filterByEmail(UserFilters userFilters, JPAQuery<?> query) {
        if (!Objects.isNull(userFilters.getEmail())) {
            query.where(root.email.likeIgnoreCase("%" + userFilters.getEmail() + "%"));
        }
    }

    public void filterByStatus(UserFilters userFilters, JPAQuery<?> query) {

        if (!Objects.isNull(userFilters.getStatus())) {
            query.where(root.status.eq(userFilters.getStatus().name()));
        }
    }

    public void filterByAccountID(UserFilters userFilters, JPAQuery<?> query) {
        if (!Objects.isNull(userFilters.getAccountID())) {
            query.where(root.account.id.eq(userFilters.getAccountID()));
        }
    }

    public void filterByAccountIds(UserFilters userFilters, JPAQuery<?> query){
        if (!Objects.isNull(userFilters.getAccountIds())) {
            query.where(root.account.id.in(userFilters.getAccountIds()));
        }
    }

    public void filterByCreatedAt(UserFilters userFilters, JPAQuery<?> query) {
        if (!Objects.isNull(userFilters.getCreatedAt())) {
            LocalDateTime createdAt = userFilters.getCreatedAt();
            LocalDateTime startOfDay = createdAt.with(LocalTime.MIN);
            LocalDateTime endOfDay = createdAt.with(LocalTime.MAX);
            query.where(root.createdAt.between(startOfDay, endOfDay));
        }
    }

}
