package pt.jumia.services.brad.data.brad.configuration;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.jdbc.datasource.AbstractDataSource;

import java.sql.Connection;
import java.sql.SQLException;

public class RetryableDatasource extends AbstractDataSource {

    private HikariDataSource dataSource;
    private final HikariConfig config;

    public RetryableDatasource(HikariConfig config) {
        this.config = new HikariConfig();
        config.copyStateTo(this.config);
    }

    private synchronized void initializeDataSource() {
        if (this.dataSource == null) {
            this.dataSource = new HikariDataSource(config);
        }
    }

    @Override
    public Connection getConnection() throws SQLException {
        initializeDataSource();
        return this.dataSource.getConnection();
    }

    @Override
    public Connection getConnection(String username, String password) throws SQLException {
        initializeDataSource();
        return this.dataSource.getConnection(username, password);
    }
}
