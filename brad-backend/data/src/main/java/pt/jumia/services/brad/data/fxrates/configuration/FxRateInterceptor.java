package pt.jumia.services.brad.data.fxrates.configuration;

import org.hibernate.EmptyInterceptor;
import pt.jumia.services.brad.data.shared.ViewContext;

import java.io.Serial;
import java.util.regex.Matcher;

public class FxRateInterceptor extends EmptyInterceptor {
    @Serial
    private static final long serialVersionUID = 46842642689433L;
    private static final String SCHEMA = "BRAD"; //dbo
    private static final String REGEX = String.format("(%s.)[^\\s]+", SCHEMA);

    @Override
    public String onPrepareStatement(String sql) {
        return sql.replaceFirst(REGEX, Matcher.quoteReplacement(String.format("%s.[%s]",
                ViewContext.getCurrentContext().getSchemaName(), ViewContext.getCurrentContext().getViewName())));
    }
}
