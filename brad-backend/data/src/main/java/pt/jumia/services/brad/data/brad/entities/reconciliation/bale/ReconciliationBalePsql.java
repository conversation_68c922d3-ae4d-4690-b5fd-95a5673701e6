package pt.jumia.services.brad.data.brad.entities.reconciliation.bale;


import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.data.brad.entities.BalePsql;
import pt.jumia.services.brad.data.brad.entities.reconciliation.ReconciliationPsql;

@Getter
@Entity
@NoArgsConstructor
@Table(name = "RECONCILIATIONS_BALE")
public class ReconciliationBalePsql {

    private static final String ID_COMPANY_COLUMN = "ID_COMPANY";

    @EmbeddedId
    private ReconciliationBalePsqlId id;

    @MapsId("reconciliationKey")
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumns({
            @JoinColumn(name = "RECONCILIATION_ID", referencedColumnName = "ID"),
            @JoinColumn(name = ID_COMPANY_COLUMN, referencedColumnName = ID_COMPANY_COLUMN)
    })
    private ReconciliationPsql reconciliation;


    @MapsId("baleKey")
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumns({
            @JoinColumn(name = "BALE_ID", referencedColumnName = "ID"),
            @JoinColumn(name = ID_COMPANY_COLUMN, referencedColumnName = ID_COMPANY_COLUMN)
    })
    private BalePsql balePsql;

    @SuppressFBWarnings("EI_EXPOSE_REP2")
    public ReconciliationBalePsql(ReconciliationPsql reconciliation, BalePsql bale) {
        this.reconciliation = reconciliation;
        this.balePsql = bale;
        this.id = new ReconciliationBalePsqlId(reconciliation.toEntity().getId(),
                bale.toEntity().getId(), bale.getIdCompany());
    }

}
