package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import pt.jumia.services.brad.data.brad.entities.TransactionPsql;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface JpaTransactionRepository extends JpaRepository<TransactionPsql, Long> {

    @Modifying
    @Query("DELETE FROM TransactionPsql t WHERE t.accountStatement.id = :statementId")
    void deleteAllByAccountStatementId(@Param("statementId") Long statementId);

    //might need to change, intellij shows errors
    @Query("select sum(tr.amount * CASE WHEN tr.direction = 2 THEN 1 ELSE -1 END) as total_amount " +
            "from TransactionPsql tr " +
            "where tr.accountStatement.account.id = :accountId " +
            "and tr.id in :transactionIds")
    BigDecimal getTotalAmountOfTransactions(@Param("accountId") Long accountId,
                                            @Param("transactionIds") List<Long> transactionIds);

    Optional<TransactionPsql> findFirstByOrderByTimestampRunAtDesc();

    Optional<TransactionPsql> findFirstByAccountStatementStatementIdOrderByTransactionDateDesc(String statementId);

    @Query("SELECT COUNT(b) FROM TransactionPsql b " +
            "WHERE b.id IN :transactionIds " +
            "AND b.reconcileStatus = 'NOT_RECONCILED'" +
            "AND b.accountStatement.status = 'IMPORTED'")
    Integer areTransactionsAvailable(@Param("transactionIds") List<Long> transactionIds);

    @Modifying
    @Query("UPDATE TransactionPsql b " +
            "SET b.reconcileStatus = 'PENDING_APPROVAL' " +
            "WHERE b.id IN :transactionIds")
    void updateTransactionsToPendingApproval(@Param("transactionIds") List<Long> transactionIds);

}
