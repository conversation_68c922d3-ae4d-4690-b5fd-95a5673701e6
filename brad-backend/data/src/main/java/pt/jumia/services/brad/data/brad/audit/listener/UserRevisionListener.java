package pt.jumia.services.brad.data.brad.audit.listener;

import org.hibernate.envers.RevisionListener;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.data.brad.entities.RevisionPsql;
import pt.jumia.services.brad.domain.RequestContext;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * {@link RevisionListener} implementation, which will be called every time there is a changed to an audited entity
 */
public class UserRevisionListener implements RevisionListener {

    @Override
    public void newRevision(Object revisionEntity) {
        RevisionPsql revision = (RevisionPsql) revisionEntity;
        RequestUser user = RequestContext.getUser();
        revision.setEmail(user == null ? "system" : user.getEmail());
        revision.setTime(LocalDateTime.now(ZoneOffset.UTC));
    }
}
