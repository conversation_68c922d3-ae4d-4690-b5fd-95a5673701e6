package pt.jumia.services.brad.data.brad.entities.reconciliation.transaction;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class ReconciliationTransactionPsqlId implements Serializable {

    @Serial
    private static final long serialVersionUID = 47542873876354L;


    @Column(name = "RECONCILIATION_ID")
    private Integer id;
    @Column(name = "TRANSACTION_ID")
    private Long transactionId;
    @Column(name = "PARTITION_KEY")
    private String partitionKey;
    @Column(name = "ID_COMPANY")
    private String idCompany;

}
