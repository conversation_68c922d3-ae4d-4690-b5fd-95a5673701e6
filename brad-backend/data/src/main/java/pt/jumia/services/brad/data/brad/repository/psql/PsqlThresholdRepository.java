package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.QThresholdPsql;
import pt.jumia.services.brad.data.brad.entities.ThresholdPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaThresholdRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.threshold.ThresholdFilters;
import pt.jumia.services.brad.domain.entities.filter.threshold.ThresholdSortFilters;
import pt.jumia.services.brad.domain.entities.reconciliation.Threshold;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.ThresholdRepository;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class PsqlThresholdRepository extends BaseRepository implements ThresholdRepository {

    private final QThresholdPsql root = new QThresholdPsql("root");
    private final JpaThresholdRepository jpaThresholdRepository;
    private final EntityManager entityManager;

    @Override
    @Transactional
    public Threshold upsert(Threshold threshold) {
        return jpaThresholdRepository.save(new ThresholdPsql(threshold)).toEntity();
    }

    @Override
    public Optional<Threshold> findById(long id) {
        return jpaThresholdRepository.findById(id).map(ThresholdPsql::toEntity);
    }

    @Override
    public Optional<Threshold> findByCurrencyCodeAndCountryCode(String currencyCode, String countryCode) {
        return Optional.ofNullable(new JPAQueryFactory(entityManager)
                .selectFrom(root)
                .where(root.currency.code.eq(currencyCode)
                        .and(root.country.code.eq(countryCode)))
                .fetchOne())
                .map(ThresholdPsql::toEntity);
    }


    @Override
    public List<Threshold> findAll(ThresholdFilters thresholdFilters, ThresholdSortFilters thresholdSortFilters,
                                   PageFilters pageFilters) throws EntityErrorsException {
        JPAQuery<ThresholdPsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root);

        if (Objects.nonNull(thresholdFilters)) {
            applyProjectionSelect(thresholdFilters.getSelectedFields(), query, root, ThresholdPsql.class);
        }
        buildWhereClauses(thresholdFilters, query);
        applySort(thresholdSortFilters, query, root, Threshold.SortingFields.class);
        applyPagination(pageFilters, query);

        return query.distinct().fetch().stream()
                .map(ThresholdPsql::toEntity)
                .toList();
    }

    @Override
    public void deleteById(long id) {
        jpaThresholdRepository.deleteById(id);
    }

    @Override
    public Integer count(ThresholdFilters thresholdFilters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(thresholdFilters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    private void buildWhereClauses(ThresholdFilters thresholdFilters, JPAQuery<?> query) {
        if (Objects.isNull(thresholdFilters)) {
            return;
        }
        filterByCountryCode(thresholdFilters, query);
        filterByCurrencyCode(thresholdFilters, query);
        filterByCreatedAt(thresholdFilters, query);
    }

    private void filterByCountryCode(ThresholdFilters thresholdFilters, JPAQuery<?> query) {
        if (!Objects.isNull(thresholdFilters.getCountryCode())) {
            query.where(root.country.code.eq(thresholdFilters.getCountryCode()));
        }
    }

    private void filterByCurrencyCode(ThresholdFilters thresholdFilters, JPAQuery<?> query) {
        if (!Objects.isNull(thresholdFilters.getCurrencyCode())) {
            query.where(root.currency.code.eq(thresholdFilters.getCurrencyCode()));
        }
    }

    public void filterByCreatedAt(ThresholdFilters thresholdFilters, JPAQuery<?> query) {
        if (!Objects.isNull(thresholdFilters.getCreatedAt())) {
            LocalDateTime createdAt = thresholdFilters.getCreatedAt();
            LocalDateTime startOfDay = createdAt.with(LocalTime.MIN);
            LocalDateTime endOfDay = createdAt.with(LocalTime.MAX);
            query.where(root.createdAt.between(startOfDay, endOfDay));
        }

    }
}
