package pt.jumia.services.brad.data.brad.entities.fxrates.statement;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.data.brad.entities.AccountStatementPsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.FxRatePsql;


@Getter
@Entity
@NoArgsConstructor
@Table(name = "FX_RATES_BANK_STATEMENT")
public class FxRateStatementPsql {

    @EmbeddedId
    private FxRateStatementPsqlId id;

    @MapsId("fxRateKey")
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumns({
            @JoinColumn(name = "FX_RATES_ID", referencedColumnName = "ID")
    })
    private FxRatePsql fxRate;

    @MapsId("bankStatementKey")
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumns({
            @JoinColumn(name = "BANK_STATEMENT_ID", referencedColumnName = "ID"),
            @JoinColumn(name = "PARTITION_KEY", referencedColumnName = "PARTITION_KEY")
    })
    private AccountStatementPsql statement;

    @SuppressFBWarnings("EI_EXPOSE_REP2")
    public FxRateStatementPsql(FxRatePsql fxRate, AccountStatementPsql statement) {
        this.fxRate = fxRate;
        this.statement = statement;
        this.id = new FxRateStatementPsqlId(fxRate.toEntity().getId(),
                statement.toEntityWithoutPreviousStatement().getId(), statement.getPartitionKey());
    }

}
