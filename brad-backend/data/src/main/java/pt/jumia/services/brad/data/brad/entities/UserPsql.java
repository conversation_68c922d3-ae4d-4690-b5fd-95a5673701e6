package pt.jumia.services.brad.data.brad.entities;

import jakarta.persistence.*;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.account.User;

import java.time.LocalDateTime;
import java.util.Map;
import pt.jumia.services.brad.domain.entities.account.User.Status;

@Setter
@Entity
@Audited
@NoArgsConstructor
@Table(name = "user_info")
public class UserPsql extends BaseEntityFieldMap<User.SortingFields> {

    @Id
    @SequenceGenerator(name = "user", sequenceName = "user_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "user_sequence_id_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "user_name", nullable = false)
    private String userName;

    @Column(name = "email", nullable = false)
    private String email;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "bank_account_id", nullable = false)
    private AccountPsql account;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "created_by", updatable = false)
    private String createdBy;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    @Column(name = "hr_role", nullable = false)
    private String hrRole;

    @Column(name = "permission_type", nullable = false)
    private String permissionType;

    @Column(name = "mobile_phone_number")
    private String mobilePhoneNumber;

    @Column(name = "status")
    private String status;

    public UserPsql(User user) {
        this.id = user.getId();
        this.userName = user.getName();
        this.email = user.getEmail();
        this.account = new AccountPsql(user.getAccount());
        this.createdBy = user.getCreatedBy() == null
                ? RequestContext.getUsername()
                : user.getCreatedBy();
        this.createdAt = user.getCreatedAt() == null
                ? LocalDateTime.now()
                : user.getCreatedAt();
        this.updatedBy = user.getUpdatedBy() == null
                ? RequestContext.getUsername()
                : user.getUpdatedBy();
        this.updatedAt = user.getUpdatedAt() == null
                ? LocalDateTime.now()
                : user.getUpdatedAt();
        this.hrRole = user.getHrRole();
        this.permissionType = user.getPermissionType();
        this.mobilePhoneNumber = user.getMobilePhoneNumber();
        this.status = user.getStatus().name();

    }

    public User toEntity(){
        return User.builder()
                .id(this.id)
                .name(this.userName)
                .email(this.email)
                .account(this.account.toEntity())
                .createdAt(this.createdAt)
                .createdBy(this.createdBy)
                .updatedAt(this.updatedAt)
                .updatedBy(this.updatedBy)
                .hrRole(this.hrRole)
                .permissionType(this.permissionType)
                .mobilePhoneNumber(this.mobilePhoneNumber)
                .status(Status.valueOf(this.status))
                .build();
    }

    static {
        Map<User.SortingFields, String> entityFields = Map.ofEntries(

                Map.entry(User.SortingFields.ID, "id"),
                Map.entry(User.SortingFields.USER_NAME, "userName"),
                Map.entry(User.SortingFields.EMAIL, "email"),
                Map.entry(User.SortingFields.CREATED_AT, "createdAt"),
                Map.entry(User.SortingFields.CREATED_BY, "createdBy"),
                Map.entry(User.SortingFields.UPDATED_AT, "updatedAt"),
                Map.entry(User.SortingFields.UPDATED_BY, "updatedBy"),
                Map.entry(User.SortingFields.HR_ROLE, "hrRole"),
                Map.entry(User.SortingFields.PERMISSION_TYPE, "permissionType"),
                Map.entry(User.SortingFields.MOBILE_PHONE_NUMBER, "mobilePhoneNumber")
        );
        BaseEntityFieldMap.addEntityFieldMap(User.SortingFields.class, entityFields);
    }

}
