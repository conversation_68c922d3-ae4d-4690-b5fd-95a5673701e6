package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import pt.jumia.services.brad.data.brad.entities.BalePsql;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface JpaBaleRepository extends JpaRepository<BalePsql, Long>, JpaSpecificationExecutor<BalePsql> {
    Optional<BalePsql> findFirstByIdCompanyOrderByBaleTimestampDesc(String idCompany);

    @Query("SELECT SUM(b.amount * CASE WHEN b.direction = 2 then 1 ELSE -1 END) as total_bale " +
            "FROM BalePsql b " +
            "WHERE b.account.id = :bankAccountId " +
            "AND b.id IN :baleIds")
    BigDecimal getTotalAmountOfBales(@Param("bankAccountId") Long bankAccountId,
                                     @Param("baleIds") List<Long> baleEntryNo);


    @Query("SELECT COUNT(b) FROM BalePsql b " +
            "WHERE b.id IN :baleIds " +
            "AND b.reconcileStatus = 'NOT_RECONCILED'")
    Integer areBalesAvailable(@Param("baleIds") List<Long> baleIds);


    @Modifying
    @Query("UPDATE BalePsql b " +
            "SET b.reconcileStatus = 'PENDING_APPROVAL' " +
            "WHERE b.id IN :baleIds")
    void updateBalesToPendingApproval(@Param("baleIds") List<Long> baleIds);

}
