package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import pt.jumia.services.brad.data.brad.entities.ViewEntityPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaViewEntityRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.ViewEntityRepository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Repository
@RequiredArgsConstructor
public class PsqlViewEntityRepository extends BaseRepository implements ViewEntityRepository {

    private final JpaViewEntityRepository jpaViewEntityRepository;

    @Override
    public ViewEntity upsert(ViewEntity viewEntity) {
        return jpaViewEntityRepository.save(new ViewEntityPsql(viewEntity)).toEntity();
    }

    @Override
    public Optional<ViewEntity> findById(long id) {
        return jpaViewEntityRepository.findById(id).map(ViewEntityPsql::toEntity);
    }

    @Override
    public List<ViewEntity> findAll(ViewEntity.EntityType entityType) throws EntityErrorsException {
        return jpaViewEntityRepository.findAllByEntityType(entityType.name()).stream()
                .map(ViewEntityPsql::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteById(long id) {
        jpaViewEntityRepository.deleteById(id);
    }
}
