DO
$$
BEGIN

insert into setting (property,
                     description,
                     value,
                     type,
                     updated_by,
                     updated_at,
                     created_by,
                     created_at)
select 'sftp_run_mode',
       'Allows for easy configuration of Sftp scan mode. Values can be LIVE or DRY_RUN. Override key should be the account id',
       'DRY_RUN',
       'DEFAULT',
       '<PERSON>',
       now(),
       '<PERSON>',
       now() WHERE NOT EXISTS (
SELECT * from setting  where property ='sftp_run_mode' and type='DEFAULT'
);
END;
$$;