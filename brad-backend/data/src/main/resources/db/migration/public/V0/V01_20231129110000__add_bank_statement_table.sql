
ALTER TABLE BANK_ACCOUNT
    ADD CONSTRAINT UQ_IBAN UNIQUE(IBAN);

CREATE SEQUENCE IF NOT EXISTS bank_statement_id_seq START 1 INCREMENT 1;

CREATE TABLE IF NOT EXISTS BANK_STATEMENT (
    ID                  BIGINT DEFAULT nextval('bank_statement_id_seq') PRIMARY KEY,
    CURRENCY            VARCHAR(3) NOT NULL,
    STATEMENT_ID        VARCHAR(50) UNIQUE NOT NULL,
    INITIAL_DATE        TIMESTAMP,
    FINAL_DATE          TIMESTAMP,
    INITIAL_DIRECTION   VARCHAR(255),
    FINAL_DIRECTION     VARCHAR(255),
    INITIAL_AMOUNT      NUMERIC(15,2),
    FINAL_AMOUNT        NUMERIC(15,2),
    STATUS              VARCHAR(50),
    BANK_ACCOUNT_ID     INTEGER,
    CREATED_BY          VARCHAR(255)        NOT NULL,
    CREATED_AT          TIMESTAMP           NOT NULL,
    UPDATED_BY          VARCHAR(255)        NOT NULL,
    UPDATED_AT          TIMESTAMP           NOT NULL    DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (BANK_ACCOUNT_ID) REFERENCES BANK_ACCOUNT(ID)
);

