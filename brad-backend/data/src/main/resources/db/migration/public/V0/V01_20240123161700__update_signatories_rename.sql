ALTER TABLE IF EXISTS signatory
    RENAME COLUMN signatory_name TO user_name;

ALTER TABLE IF EXISTS signatory
    ADD COLUMN IF NOT EXISTS hr_role varchar(50) NOT NULL DEFAULT '';

ALTER TABLE IF EXISTS signatory
    ADD COLUMN IF NOT EXISTS permission_type varchar(50) NOT NULL DEFAULT '';

ALTER TABLE IF EXISTS signatory
    ADD COLUMN IF NOT EXISTS mobile_phone_number varchar(50);

ALTER SEQUENCE IF EXISTS signatories_sequence_id_seq rename TO user_sequence_id_seq;

ALTER TABLE IF EXISTS signatory RENAME to user_info;
