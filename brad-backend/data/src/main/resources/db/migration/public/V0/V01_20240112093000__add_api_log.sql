CREATE SEQUENCE IF NOT EXISTS api_log_sequence_id_seq START 1 INCREMENT 1;
CREATE TABLE IF NOT EXISTS API_LOG (
    ID                    BIGINT DEFAULT nextval('api_log_sequence_id_seq') NOT NULL,
    LOG_TYPE              VARCHAR(255)        NOT NULL,
    REQUEST               TEXT                NOT NULL,
    RESPONSE              TEXT                NOT NULL,
    STATUS                VARCHAR(255)        NOT NULL,
    CREATED_BY            VARCHAR(255)        NOT NULL,
    CREATED_AT            TIMESTAMP           NOT NULL,
    UPDATED_BY            VARCHAR(255)        NOT NULL,
    UPDATED_AT            TIMESTAMP           NOT NULL    DEFAULT CURRENT_TIMESTAMP,
    PARTITION_KEY         VARCHAR(255),
    PRIMARY KEY (ID, PARTITION_KEY)
) PARTITION BY LIST (PARTITION_KEY);


CREATE TABLE IF NOT EXISTS api_log_bank_statement_creation PARTITION OF API_LOG FOR VALUES IN ('BANK_STATEMENT_CREATION');