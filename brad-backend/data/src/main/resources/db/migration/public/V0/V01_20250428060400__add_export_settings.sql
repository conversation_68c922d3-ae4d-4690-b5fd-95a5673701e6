INSERT INTO SETTING (property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'export.csv-chunk-limit',
       'DEFAULT',
       null,
       'Max number of records to load into memory when exporting data.',
       '5000',
       now(),
       'system',
       now(),
       'system'
    WHERE NOT EXISTS (SELECT 1 FROM setting WHERE property = 'export.csv-chunk-limit');
INSERT INTO SETTING (property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'export.csv-max-row-limit',
       'DEFAULT',
       null,
       'Max number of records to export.',
       '5000',
       now(),
       'system',
       now(),
       'system'
    WHERE NOT EXISTS (SELECT 1 FROM setting WHERE property = 'export.csv-max-row-limit');
INSERT INTO SETTING (property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'export.csv-created-at-after-minutes',
       'DEFAULT',
       null,
       'Default time interval between two exports with the same filters.',
       '5',
       now(),
       'system',
       now(),
       'system'
    WHERE NOT EXISTS (SELECT 1 FROM setting WHERE property = 'export.csv-created-at-after-minutes');
