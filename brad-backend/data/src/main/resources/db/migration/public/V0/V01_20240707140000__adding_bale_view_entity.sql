CREATE SEQUENCE IF NOT EXISTS bale_view_entity_sequence_id_seq START 1 INCREMENT 1;
CREATE TABLE IF NOT EXISTS bale_view_entity (
    id INTEGER DEFAULT nextval('bale_view_entity_sequence_id_seq') PRIMARY KEY,
    database_name VARCHAR(255) NOT NULL,
    view_name   VA<PERSON>HAR(255) NOT NULL,
    CREATED_AT  TIMESTAMP    NOT NULL,
    CREATED_BY  VARCHAR(255) NOT NULL,
    UPDATED_AT  TIMESTAMP    NOT NULL,
    UPDATED_BY  VARCHAR(255) NOT NULL,
    CONSTRAINT unique_view_name_per_database_name UNIQUE (database_name, view_name)
);