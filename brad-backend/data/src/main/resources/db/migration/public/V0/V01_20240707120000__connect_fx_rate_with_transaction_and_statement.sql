CREATE TABLE IF NOT EXISTS FX_RATES_TRANSACTION (
    FX_RATES_ID INT,
    TRANSACTION_ID BIGINT,
    PARTITION_KEY VARCHAR(255),
    PRIMARY KEY (FX_RATES_ID, TRANSACTION_ID, <PERSON>RT<PERSON>ION_KEY),
    FOREIGN KEY (FX_RATES_ID) REFERENCES FX_RATES(id),
    FOREIGN KEY (TRANSACTION_ID, PARTITION_KEY) REFERENCES TRANSACTIONS(ID, PARTITION_KEY)
);

CREATE TABLE IF NOT EXISTS FX_RATES_BANK_STATEMENT (
    FX_RATES_ID INT,
    BANK_STATEMENT_ID BIGINT,
    PARTITION_KEY VARCHAR(255),
    PRIMARY KEY (FX_RATES_ID, BANK_STATEMENT_ID, PARTITION_KEY),
    FOREIGN KEY (FX_RATES_ID) REFERENCES FX_RATES(id),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (BANK_STATEMENT_ID, PARTITION_KEY) REFERENCES BANK_STATEMENT(ID, PARTITION_KEY)
);
