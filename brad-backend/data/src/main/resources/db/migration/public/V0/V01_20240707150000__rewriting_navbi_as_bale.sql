DROP TABLE IF EXISTS reconciliation_navbi;
DROP TABLE IF EXISTS navbi;

CREATE SEQUENCE IF NOT EXISTS bale_id_seq START 1 INCREMENT 1;

CREATE TABLE IF NOT EXISTS BALE (
    id                           BIGINT DEFAULT nextval('bale_id_seq') NOT NULL,
    "id_company"                 VARCHAR(15),
    "Entry No_"                  INTEGER NOT NULL,
    "Document No_"               VARCHAR(50),
    "Document Type"              VARCHAR(50),
    "Posting Date"               VARCHAR(50),
    "BankAccount"                INTEGER REFERENCES bank_account(id),
    "Bank Account Posting Group" VARCHAR(50),
    "Description"                VARCHAR(255),
    "Source Code"                VARCHAR(50),
    "Reason Code"                VARCHAR(50),
    "Busline"                    VARCHAR(50),
    "Department"                 VARCHAR(50),
    "Amount"                     DECIMAL,
    "Remaining Amount"           DECIMAL,
    "Currency"                   INTEGER REFERENCES currency(id),
    "Amount (LCY)"               DECIMAL,
    "Bal_ Account Type"          VARCHAR(12),
    "Open"                       BOOLEAN,
    "Reversed"                   BOOLEAN,
    "Posted by"                  VARCHAR(50),
    "External Document No_"      VARCHAR(50),
    "BALE_Timestamp"             VARCHAR(50),
    "BA_Timestamp"               VARCHAR(50),
    UNIQUE (id, "id_company", "Entry No_"),
    PRIMARY KEY (id, "id_company")
) PARTITION BY LIST ("id_company");
