DROP TABLE IF EXISTS TRANSACTIONS;
DROP TABLE IF EXISTS BANK_STATEMENT;

CREATE TABLE IF NOT EXISTS BANK_STATEMENT (
    ID                    BIGINT DEFAULT nextval('bank_statement_id_seq') NOT NULL,
    CURRENCY              VARCHAR(3) NOT NULL,
    STATEMENT_ID          VARCHAR(50) NOT NULL,
    PREVIOUS_STATEMENT_ID BIGINT,
    INITIAL_DATE          TIMESTAMP,
    FINAL_DATE            TIMESTAMP,
    INITIAL_DIRECTION     VARCHAR(255),
    FINAL_DIRECTION       VARCHAR(255),
    INITIAL_AMOUNT        NUMERIC(15,2),
    FINAL_AMOUNT          NUMERIC(15,2),
    STATUS                VARCHAR(50),
    STATUS_DESCRIPTION    VARCHAR(255),
    BANK_ACCOUNT_ID       INTEGER,
    CREATED_BY            VARCHAR(255)        NOT NULL,
    CREATED_AT            TIMESTAMP           NOT NULL,
    UPDATED_BY            VARCHAR(255)        NOT NULL,
    UPDATED_AT            TIMESTAMP           NOT NULL    DEFAULT CURRENT_TIMESTAMP,
    PARTITION_KEY         VARCHAR(255),
    UNIQUE (STATEMENT_ID, PARTITION_KEY),
    UNIQUE (PREVIOUS_STATEMENT_ID, PARTITION_KEY),
    PRIMARY KEY (ID, PARTITION_KEY),
    FOREIGN KEY (BANK_ACCOUNT_ID) REFERENCES BANK_ACCOUNT(ID),
    CONSTRAINT FK_PREVIOUS_STATEMENT_ID FOREIGN KEY (PREVIOUS_STATEMENT_ID, PARTITION_KEY) REFERENCES BANK_STATEMENT(ID, PARTITION_KEY)

) PARTITION BY LIST (PARTITION_KEY);

CREATE TABLE IF NOT EXISTS TRANSACTIONS (
    ID                  BIGINT DEFAULT nextval('transactions_sequence_id_seq') NOT NULL,
    TYPE                VARCHAR(20) NOT NULL,
    ACCOUNT_ID          VARCHAR(30) NOT NULL,
    CURRENCY            VARCHAR(20) NOT NULL,
    VALUE_DATE          TIMESTAMP   NOT NULL,
    TRANSACTION_DATE    TIMESTAMP   NOT NULL,
    STATEMENT_DATE      TIMESTAMP,
    DIRECTION           SMALLINT NOT NULL,
    AMOUNT              NUMERIC(10, 2) NOT NULL,
    REFERENCE           VARCHAR(100) NOT NULL,
    DESCRIPTION         TEXT NOT NULL,
    BANK_STATEMENT      BIGINT NOT NULL,
    CREATED_BY          VARCHAR(255)        NOT NULL,
    CREATED_AT          TIMESTAMP           NOT NULL,
    UPDATED_BY          VARCHAR(255)        NOT NULL,
    UPDATED_AT          TIMESTAMP           NOT NULL    DEFAULT CURRENT_TIMESTAMP,
    PARTITION_KEY       VARCHAR(255),
    PRIMARY KEY (ID, PARTITION_KEY),
    FOREIGN KEY (BANK_STATEMENT, PARTITION_KEY) REFERENCES BANK_STATEMENT(ID, PARTITION_KEY)
) PARTITION BY LIST (PARTITION_KEY);
