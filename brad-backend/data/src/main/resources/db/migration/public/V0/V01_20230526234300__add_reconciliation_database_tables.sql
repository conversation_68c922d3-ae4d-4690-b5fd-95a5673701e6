-- ########################### FINREC ###########################

CREATE TABLE IF NOT EXISTS FINREC (
    ID INTEGER PRIMARY KEY,
    TYPE VARCHAR(255),
    ACCOUNT_CODE VARCHAR(255),
    CUR VARCHAR(255),
    ACCOUNT VARCHAR(255),
    STATEMENT_DATE DATE,
    TRANSACTION_DATE DATE,
    VALUE_DATE DATE,
    FLOWCODE VARCHAR(255),
    DIRECTION VARCHAR(255),
    AMOUNT DECIMAL,
    REFERENCE VARCHAR(255),
    DESCRIPTION VARCHAR(255),
    STATEMENT_ID VARCHAR(255),
    FILE_NAME VARCHAR(255),
    SK_AUD_INSERT INTEGER,
    SK_AUD_UPDATE INTEGER,
    TIMESTAMP_LAST_UPDATE TIMESTAMP,
    IS_RECONCILED VARCHAR(255)
);

-- ########################### NAVBI ###########################

CREATE TABLE IF NOT EXISTS NAVBI (
    ENTRY_NO INTEGER PRIMARY KEY,
    TIME_STAMP TIMESTAMP,
    ID_COMPANY VARCHAR(255),
    DOCUMENT_NO VARCHAR(255),
    DOCUMENT_TYPE VARCHAR(255),
    POSTING_DATE TIMESTAMP,
    BANK_ACCOUNT_NO VARCHAR(255),
    BANK_ACCOUNT_POSTING_GROUP VARCHAR(255),
    DESCRIPTION VARCHAR(255),
    SOURCE_CODE VARCHAR(255),
    REASON_CODE VARCHAR(255),
    BUSLINE VARCHAR(255),
    DEPARTMENT VARCHAR(255),
    AMOUNT DECIMAL,
    REMAINING_AMOUNT DECIMAL,
    CURRENCY VARCHAR(255),
    AMOUNT_LCY DECIMAL,
    BAL_ACCOUNT_NO VARCHAR(255),
    BAL_ACCOUNT_TYPE VARCHAR(255),
    IS_OPEN BOOLEAN,
    REVERSED BOOLEAN,
    POSTED_BY VARCHAR(255),
    DESTINATION_CODE VARCHAR(255),
    PARTNER_CODE VARCHAR(255),
    SK_AUD_UPDATE INTEGER,
    SK_AUD_INSERT INTEGER,
    EXTERNAL_DOCUMENT_NO VARCHAR(255),
    IS_RECONCILED VARCHAR(255)
);

CREATE TABLE IF NOT EXISTS RECONCILIATION (
    ID SERIAL PRIMARY KEY,
    STATUS VARCHAR(255),
    AMOUNT DECIMAL,
    CREATOR VARCHAR(255),
    APPROVER VARCHAR(255)
);

CREATE TABLE IF NOT EXISTS RECONCILIATION_FINREC (
    RECONCILIATIONID INTEGER REFERENCES RECONCILIATION(ID),
    FINRECID INTEGER REFERENCES FINREC(ID),
    PRIMARY KEY (RECONCILIATIONID, FINRECID)
);

CREATE TABLE IF NOT EXISTS RECONCILIATION_NAVBI (
    RECONCILIATIONID INTEGER REFERENCES RECONCILIATION(ID),
    NAVBIENTRY_NO INTEGER REFERENCES NAVBI(ENTRY_NO),
    PRIMARY KEY (RECONCILIATIONID, NAVBIENTRY_NO)
);

DO
$$
    BEGIN
        ALTER TABLE RECONCILIATION_NAVBI
            ADD CONSTRAINT FK_REC_NAVBI FOREIGN KEY (RECONCILIATIONID)
                REFERENCES RECONCILIATION(ID);

    EXCEPTION
        WHEN DUPLICATE_OBJECT THEN
            RAISE NOTICE 'Constraint FK_REC_NAVBI already exists';
    END;
$$;

DO
$$
    BEGIN
        ALTER TABLE RECONCILIATION_NAVBI
            ADD CONSTRAINT FK_NAVBI FOREIGN KEY (NAVBIENTRY_NO)
                REFERENCES NAVBI(ENTRY_NO);

    EXCEPTION
        WHEN DUPLICATE_OBJECT THEN
            RAISE NOTICE 'Constraint FK_NAVBI already exists';
    END;
$$;

DO
$$
    BEGIN
        ALTER TABLE RECONCILIATION_FINREC
            ADD CONSTRAINT FK_REC_FINREC FOREIGN KEY (RECONCILIATIONID)
                REFERENCES RECONCILIATION(ID);

    EXCEPTION
        WHEN DUPLICATE_OBJECT THEN
            RAISE NOTICE 'Constraint FK_REC_FINREC already exists';
    END;
$$;

DO
$$
    BEGIN
        ALTER TABLE RECONCILIATION_FINREC
            ADD CONSTRAINT FK_FINREC FOREIGN KEY (FINRECID)
                REFERENCES FINREC(ID);

    EXCEPTION
        WHEN DUPLICATE_OBJECT THEN
            RAISE NOTICE 'Constraint FK_FINREC already exists';
    END;
$$;






