DROP TABLE IF EXISTS RECON<PERSON><PERSON>IATION CASCADE;
DROP TABLE IF EXISTS RECONCILIATION_TRANSACTIONS CASCADE;
DROP TABLE IF EXISTS RECONCILIATION_BALE CASCADE;
DROP TABLE IF EXISTS RECONCILIATION_FINREC CASCADE;
CREATE SEQUENCE IF NOT EXISTS reconciliation_sequence_id_seq START 1 INCREMENT 1;
CREATE TABLE IF NOT EXISTS RECONCILIATIONS (
    ID BIGINT DEFAULT nextval('reconciliation_sequence_id_seq') NOT NULL,
    STATUS VARCHAR(255),
    AMOUNT DECIMAL,
    CREATOR VARCHAR(255),
    APPROVER VARCHAR(255),
    ID_COMPANY VARCHAR(15),
    PRIMARY <PERSON>Y (ID, ID_COMPANY)
) PARTITION BY LIST (ID_COMPANY);

CREATE TABLE IF NOT EXISTS RECONCILIATIONS_TRANSACTIONS (
    RECONCILIATION_ID INTEGER,
    TRANSACTION_ID INTEGER,
    PARTITION_KEY <PERSON>RCHAR(255),
    ID_COMPANY VARCHAR(15),
    <PERSON>IMARY KEY (RECONCILIATION_ID, TRANSACTION_ID, PARTITION_KEY, ID_COMPANY),
    FOREIGN KEY (RECONCILIATION_ID, ID_COMPANY) REFERENCES RECONCILIATIONS (ID, ID_COMPANY),
    FOREIGN KEY (TRANSACTION_ID, PARTITION_KEY) REFERENCES TRANSACTIONS(ID, PARTITION_KEY)
);

CREATE TABLE IF NOT EXISTS RECONCILIATIONS_BALE (
    RECONCILIATION_ID INTEGER,
    BALE_ID INTEGER,
    ID_COMPANY VARCHAR(15),
    PRIMARY KEY (RECONCILIATION_ID, BALE_ID, ID_COMPANY),
    FOREIGN KEY (RECONCILIATION_ID, ID_COMPANY) REFERENCES RECONCILIATIONS (ID, ID_COMPANY),
    FOREIGN KEY (BALE_ID, ID_COMPANY) REFERENCES BALE(ID, ID_COMPANY)
);

DO
$$
    BEGIN
        ALTER TABLE RECONCILIATIONS_BALE
            ADD CONSTRAINT FK_REC_BALE FOREIGN KEY (RECONCILIATION_ID, ID_COMPANY)
                REFERENCES RECONCILIATIONS (ID, ID_COMPANY);

    EXCEPTION
        WHEN DUPLICATE_OBJECT THEN
            RAISE NOTICE 'Constraint FK_REC_BALE already exists';
    END;
$$;

DO
$$
    BEGIN
        ALTER TABLE RECONCILIATIONS_BALE
            ADD CONSTRAINT FK_BALE FOREIGN KEY (BALE_ID, ID_COMPANY)
                REFERENCES BALE(ID, ID_COMPANY);

    EXCEPTION
        WHEN DUPLICATE_OBJECT THEN
            RAISE NOTICE 'Constraint FK_BALE already exists';
    END;
$$;

DO
$$
    BEGIN
        ALTER TABLE RECONCILIATIONS_TRANSACTIONS
            ADD CONSTRAINT FK_REC_TRANSACTION FOREIGN KEY (RECONCILIATION_ID, ID_COMPANY)
                REFERENCES RECONCILIATIONS(ID, ID_COMPANY);

    EXCEPTION
        WHEN DUPLICATE_OBJECT THEN
            RAISE NOTICE 'Constraint FK_REC_TRANSACTION already exists';
    END;
$$;

DO
$$
    BEGIN
        ALTER TABLE RECONCILIATIONS_TRANSACTIONS
            ADD CONSTRAINT FK_TRANSACTION FOREIGN KEY (TRANSACTION_ID, PARTITION_KEY)
                REFERENCES TRANSACTIONS(ID, PARTITION_KEY);
    EXCEPTION
        WHEN DUPLICATE_OBJECT THEN
            RAISE NOTICE 'Constraint FK_TRANSACTION already exists';
    END;
$$;


ALTER TABLE BALE
    ADD COLUMN IF NOT EXISTS RECONCILE_STATUS VARCHAR(255) DEFAULT 'NOT_RECONCILED' NOT NULL;
ALTER TABLE TRANSACTIONS
    ADD COLUMN IF NOT EXISTS RECONCILE_STATUS VARCHAR(255) DEFAULT 'NOT_RECONCILED' NOT NULL;