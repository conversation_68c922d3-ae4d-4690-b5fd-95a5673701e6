CREATE SEQUENCE IF NOT EXISTS currency_sequence_id_seq START 1 INCREMENT 1;
CREATE TABLE IF NOT EXISTS CURRENCY (
    ID                  BIGINT DEFAULT NEXTVAL('currency_sequence_id_seq') PRIMARY KEY,
    NAME                VARCHAR(45)     NOT NULL,
    CODE                VARCHAR(45)     UNIQUE NOT NULL,
    SYMBOL              VARCHAR(45)     NOT NULL,
    CREATED_BY          VARCHAR(255)    NOT NULL,
    CREATED_AT          TIMESTAMP       NOT NULL,
    UPDATED_BY          VARCHAR(255)    NOT NULL,
    UPDATED_AT          TIMESTAMP       NOT NULL    DEFAULT CURRENT_TIMESTAMP

);


ALTER TABLE BANK_ACCOUNT
    ALTER COLUMN             COMPANY_ID              DROP NOT NULL,
    ALTER COLUMN             NAV_REFERENCE           DROP NOT NULL,
    ALTER COLUMN             BENEFICIARY_NAME        DROP NOT NULL,
    ALTER COLUMN             BENEFICIARY_ADDRESS     DROP NOT NULL,
    ADD COLUMN IF NOT EXISTS IBAN                    VARCHAR(255),
    ALTER COLUMN             BANK_NAME               DROP NOT NULL,
    ALTER COLUMN             SWIFT_CODE              DROP NOT NULL,
    ALTER COLUMN             BANK_ROUTING_CODE       DROP NOT NULL,
    ALTER COLUMN             SORT_CODE               DROP NOT NULL,
    ALTER COLUMN             BRANCH_CODE             DROP NOT NULL,
    ALTER COLUMN             RIB                     DROP NOT NULL,
    ALTER COLUMN             STATUS                  DROP NOT NULL,
    ADD COLUMN IF NOT EXISTS CURRENCY_ID             BIGINT NOT NULL ,
    ADD CONSTRAINT FK_BANK_ACCOUNT_CURRENCY_ID FOREIGN KEY (CURRENCY_ID) REFERENCES CURRENCY(ID);


ALTER TABLE BANK_STATEMENT
    RENAME COLUMN CURRENCY TO CURRENCY_ID;

ALTER TABLE TRANSACTIONS
    RENAME COLUMN CURRENCY TO CURRENCY_ID;

ALTER TABLE BANK_STATEMENT
    ALTER COLUMN CURRENCY_ID SET DATA TYPE BIGINT USING CURRENCY_ID::BIGINT,
    ALTER COLUMN CURRENCY_ID SET NOT NULL,
    ADD CONSTRAINT FK_BANK_STATEMENT_CURRENCY_ID FOREIGN KEY (CURRENCY_ID) REFERENCES CURRENCY(ID);

ALTER TABLE TRANSACTIONS
    ALTER COLUMN CURRENCY_ID SET DATA TYPE BIGINT USING CURRENCY_ID::BIGINT,
    ALTER COLUMN CURRENCY_ID SET NOT NULL,
    ADD CONSTRAINT FK_TRANSACTIONS_CURRENCY_ID FOREIGN KEY (CURRENCY_ID) REFERENCES CURRENCY(ID);

ALTER TABLE COUNTRY
    ADD COLUMN IF NOT EXISTS CURRENCY_ID BIGINT,
    ALTER COLUMN CURRENCY_ID SET NOT NULL,
    ADD CONSTRAINT FK_COUNTRY_CURRENCY_ID FOREIGN KEY (CURRENCY_ID) REFERENCES CURRENCY(ID);
