/*-- ########################### CONTACTS ########################### --*/
CREATE SEQUENCE IF NOT EXISTS contact_sequence_id_seq START 1 INCREMENT 1;
CREATE TABLE IF NOT EXISTS CONTACT (
    ID          INTEGER DEFAULT nextval('contact_sequence_id_seq') PRIMARY KEY,
    CONTACT_TYPE VARCHAR(255) NOT NULL,
    NAME        VARCHAR(255) NOT NULL,
    EMAIL       VARCHAR(255) NOT NULL,
    BANK_ACCOUNT_ID INTEGER REFERENCES BANK_ACCOUNT(ID) NOT NULL ,
    CREATED_AT  TIMESTAMP    NOT NULL,
    CREATED_BY  VARCHAR(255) NOT NULL,
    UPDATED_AT  TIMESTAMP    NOT NULL,
    UPDATED_BY  VARCHAR(255) NOT NULL
);
