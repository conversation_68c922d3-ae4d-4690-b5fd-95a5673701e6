DO
$$
BEGIN

insert into setting (property,
                     description,
                     value,
                     type,
                     updated_by,
                     updated_at,
                     created_by,
                     created_at)
select 'troubleshooting_email_recipient',
       'Allows for easy configuration of troubleshooting email recipient',
       '<EMAIL>',
       'DEFAULT',
       'Brad',
       now(),
       '<PERSON>',
       now() WHERE NOT EXISTS (
SELECT * from setting  where property ='troubleshooting_email_recipient' and type='DEFAULT'
);
END;
$$;