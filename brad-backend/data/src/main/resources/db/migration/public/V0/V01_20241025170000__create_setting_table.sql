CREATE SEQUENCE IF NOT EXISTS setting_id_seq START 1 INCREMENT 1;
DO
$$
BEGIN
CREATE TABLE setting
(
    id          BIGINT       NOT NULL DEFAULT nextval('setting_id_seq') PRIMARY KEY,
    property     VARCHAR(255) NOT NULL,
    type         VA<PERSON><PERSON><PERSON>(64)  NOT NULL,
    override_key VARCHAR(255) UNIQUE,
    description  VARCHAR,
    value        VARCHAR      NOT NULL,
    created_by  <PERSON><PERSON><PERSON><PERSON>(255),
    created_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VA<PERSON>HAR (255),
    updated_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP
);
EXCEPTION
        WHEN DUPLICATE_TABLE THEN
            RAISE NOTICE 'table "setting" already exists. Ignoring...';
END;
$$;
