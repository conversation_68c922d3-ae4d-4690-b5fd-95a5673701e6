DO
$$
BEGIN

CREATE TABLE IF NOT EXISTS account_daily_summary (
    transaction_date DATE NOT NULL,
    account_id BIGINT NOT NULL,
    statement_id BIGINT NOT NULL,
    total_credit_amount NUMERIC(20, 2) NOT NULL,
    total_debit_amount NUMERIC(20, 2) NOT NULL,
    total_credit_amount_usd NUMERIC(20, 2),
    total_debit_amount_usd NUMERIC(20, 2),
    net_amount NUMERIC(20, 2) NOT NULL,
    net_amount_usd NUMERIC(20, 2) ,
    initial_balance NUMERIC(20, 2) NOT NULL,
    final_balance NUMERIC(20, 2) NOT NULL,
    initial_balance_usd NUMERIC(20, 2) ,
    final_balance_usd NUMERIC(20, 2),
    transactions_count INT NOT NULL,
    debit_transactions_count INT NOT NULL,
    credit_transactions_count INT NOT NULL,
    currency_id BIGINT NOT NULL,
    fx_rate_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    partition_key VARCHAR(255) NOT NULL,

    UNIQUE (transaction_date, account_id),
    FOREIGN KEY (currency_id) REFERENCES currency(id),
    FOREIGN KEY (fx_rate_id) REFERENCES fx_rates(id),
    FOREIGN KEY (account_id) REFERENCES bank_account(id),
    FOREIGN KEY (statement_id, partition_key) REFERENCES bank_statement(id, partition_key),
    PRIMARY KEY (transaction_date, account_id)
    );
END;
$$;