CREATE SEQUENCE IF NOT EXISTS bank_statement_file_id_seq START 1 INCREMENT 1;

CREATE TABLE IF NOT EXISTS bank_statement_file(
    id                      BIGINT DEFAULT nextval('bank_statement_file_id_seq') PRIMARY KEY,
    name                    <PERSON><PERSON><PERSON><PERSON>(255)  NOT NULL,
    url                     VARCHAR(255)  NOT NULL,
    processing_status       VARCHAR(255)  NOT NULL,
    checksum                VARCHAR(255),
    fk_execution_log        BIGINT REFERENCES execution_log(id) NOT NULL,
    partition_key           VARCHAR(255),
    statement_id            BIGINT,
    created_at              TIMESTAMP     NOT NULL,
    created_by              <PERSON><PERSON><PERSON><PERSON>(255)  NOT NULL,
    updated_at              TIMESTAMP     NOT NULL,
    updated_by              <PERSON><PERSON><PERSON><PERSON>(255)  NOT NULL,

    FOREIGN KEY (statement_id, partition_key) REFERENCES bank_statement (id, partition_key)
);

