CREATE SEQUENCE IF NOT EXISTS country_sequence_id_seq START 1 INCREMENT 1;
CREATE TABLE IF NOT EXISTS COUNTRY (
    ID          INTEGER DEFAULT nextval('country_sequence_id_seq') PRIMARY KEY,
    NAME        VARCHAR(255) NOT NULL,
    CODE VARCHAR(3) NOT NULL UNIQUE,
    CREATED_BY    VARCHAR(255)        NOT NULL,
    CREATED_AT    TIMESTAMP           NOT NULL,
    UPDATED_BY    VARCHAR(255)        NOT NULL,
    UPDATED_AT    TIMESTAMP           NOT NULL    DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE BANK_ACCOUNT
    DROP COLUMN IF EXISTS COUNTRY,
    ADD COLUMN IF NOT EXISTS FK_COUNTRY INTEGER REFERENCES COUNTRY(ID);