CREATE SEQUENCE IF NOT EXISTS transactions_sequence_id_seq START 1 INCREMENT 1;
CREATE TABLE IF NOT EXISTS TRANSACTIONS (
    ID                  BIGINT DEFAULT nextval('transactions_sequence_id_seq') PRIMARY KEY,
    TYPE                VARCHAR(20) NOT NULL,
    ACCOUNT_ID          VARCHAR(30) NOT NULL,
    CURRENCY            VARCHAR(20) NOT NULL,
    VALUE_DATE          TIMESTAMP   NOT NULL,
    TRANSACTION_DATE    TIMESTAMP   NOT NULL,
    STATEMENT_DATE      TIMESTAMP,
    DIRECTION           SMALLINT NOT NULL,
    AMOUNT              NUMERIC(10, 2) NOT NULL,
    REFERENCE           VARCHAR(100) NOT NULL,
    DESCRIPTION         TEXT NOT NULL,
    BANK_STATEMENT      BIGINT NOT NULL,
    CREATED_BY          VARCHAR(255)        NOT NULL,
    CREATED_AT          TIMESTAMP           NOT NULL,
    UPDATED_BY          VARCHAR(255)        NOT NULL,
    UPDATED_AT          TIMESTAMP           NOT NULL    DEFAULT CURRENT_TIMESTAMP,

    <PERSON>OR<PERSON>GN KEY (BANK_STATEMENT) REFERENCES BANK_STATEMENT(ID)
);
