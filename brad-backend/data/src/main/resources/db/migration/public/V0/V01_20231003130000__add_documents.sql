-- ########################### DOCUMENTS ###########################
CREATE SEQUENCE IF NOT EXISTS document_sequence_id_seq START 1 INCREMENT 1;
CREATE TABLE IF NOT EXISTS DOCUMENT (
    ID          INTEGER DEFAULT nextval('document_sequence_id_seq') PRIMARY KEY,
    TYPE        VARCHAR(255)  NOT NULL,
    NAME VARCHAR(255) NOT NULL,
    DESCRIPTION       VARCHAR(255)  NOT NULL,
    URL    VARCHAR(255),
    BANK_ACCOUNT_ID INTEGER REFERENCES BANK_ACCOUNT(ID) NOT NULL ,
    CREATED_AT  TIMESTAMP    NOT NULL,
    CREATED_BY  VARCHAR(255) NOT NULL,
    UPDATED_AT  TIMESTAMP    NOT NULL,
    UPDATED_BY  VARCHAR(255) NOT NULL
);
