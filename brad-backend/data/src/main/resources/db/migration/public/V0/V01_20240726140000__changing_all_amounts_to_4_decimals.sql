ALTER TABLE threshold
    ALTER COLUMN amount TYPE NUMERIC(18,4) USING amount::NUMERIC(18,4);

ALTER TABLE transactions
    ALTER COLUMN amount TYPE NUMERIC(18,4) USING amount::NUMERIC(18,4);

ALTER TABLE bank_statement
    ALTER COLUMN initial_amount TYPE NUMERIC(18,4) USING initial_amount::NUMERIC(18,4),
    ALTER COLUMN final_amount TYPE NUMERIC(18,4) USING final_amount::NUMERIC(18,4);

ALTER TABLE bale
    ALTER COLUMN "Amount" TYPE NUMERIC(18,4) USING "Amount"::<PERSON><PERSON><PERSON><PERSON>(18,4),
    ALTER COLUMN "Remaining Amount" TYPE NUMERIC(18,4) USING "Remaining Amount"::NUMERIC(18,4),
    ALTER COLUMN "Amount (LCY)" TYPE NUMERIC(18,4) USING "Amount (LCY)"::NUMERIC(18,4);