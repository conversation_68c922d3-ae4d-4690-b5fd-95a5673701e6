CREATE SEQUENCE IF NOT EXISTS execution_log_sequence_id_seq START 1 INCREMENT 1;
CREATE TABLE IF NOT EXISTS EXECUTION_LOG (
    ID                      BIGINT DEFAULT nextval('execution_log_sequence_id_seq') PRIMARY KEY,
    RECORDS_AMOUNT          BIGINT NOT NULL,
    EXECUTION_START_TIME    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    EXECUTION_END_TIME      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    APPLIED_FILTERS         TEXT,
    QUERY                   TEXT
);
