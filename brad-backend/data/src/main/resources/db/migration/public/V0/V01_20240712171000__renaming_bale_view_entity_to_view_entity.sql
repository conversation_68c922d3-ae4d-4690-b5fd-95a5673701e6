DO
$$
    BEGIN
        ALTER TABLE bale_view_entity
            ADD COLUMN IF NOT EXISTS entity_type VARCHAR(255),
            ADD COLUMN IF NOT EXISTS driver VARCHAR(255);
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Bale View Entity table not found';
    END;
$$;

DO
$$
    BEGIN
        ALTER TABLE bale_view_entity RENAME TO view_entity;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Bale View Entity table not found';
    END;
$$;


DO
$$
    BEGIN
        ALTER SEQUENCE bale_view_entity_sequence_id_seq RENAME TO view_entity_sequence_id_seq;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Bale View Entity sequence not found';
    END;
$$;

