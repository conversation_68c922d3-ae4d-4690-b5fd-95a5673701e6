DO $$
BEGIN
    CREATE SEQUENCE IF NOT EXISTS export_log_id_seq
        INCREMENT BY 1
        NO MAXVALUE
        NO MINVALUE
        CACHE 1;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'An error occurred: %', SQLERRM;
END $$;

DO $$
BEGIN
    CREATE TABLE IF NOT EXISTS export_log
    (
        id             BIGINT DEFAULT nextval('export_log_id_seq') NOT NULL PRIMARY KEY,
        type           VARCHAR(255)                                NOT NULL,
        file_url       VARCHAR,
        file_name      VARCHAR(255),
        row_count      BIGINT,
        status         VARCHAR(255),
        execution_time BIGINT,
        filters        VARCHAR,
        countries_list VARCHAR,
        created_at     TIMESTAMP                                   NOT NULL,
        created_by     varchar(255),
        updated_at     TIMESTAMP                                   NOT NULL
    );
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'An error occurred: %', SQLERRM;
END $$;