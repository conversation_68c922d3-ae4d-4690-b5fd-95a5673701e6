ALTER TABLE BALE
    ALTER COLUMN "Posting Date" TYPE DATE USING "Posting Date"::DATE;

--adding indexes to dates
CREATE INDEX IF NOT EXISTS idx_transaction_value_date
    ON transactions (value_date);
CREATE INDEX IF NOT EXISTS idx_transaction_statement_date
    ON transactions (statement_date);
CREATE INDEX IF NOT EXISTS idx_transaction_transaction_date
    ON transactions (transaction_date);

CREATE INDEX IF NOT EXISTS idx_bale_posting_date
    ON bale ("Posting Date");

