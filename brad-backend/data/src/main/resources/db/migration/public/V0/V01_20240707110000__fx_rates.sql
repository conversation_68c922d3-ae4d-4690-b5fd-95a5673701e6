CREATE SEQUENCE IF NOT EXISTS fx_rates_sequence_id_seq START 1 INCREMENT 1;
CREATE TABLE IF NOT EXISTS FX_RATES (
    id INT DEFAULT NEXTVAL('fx_rates_sequence_id_seq') PRIMARY KEY ,
    "Base_Currency" bigint REFERENCES CURRENCY(id) NOT NULL,
    "Quote_Currency" bigint REFERENCES CURRENCY(id) NOT NULL,
    "Rate_Date" DATE NOT NULL,
    "bid" DECIMAL(18, 6) NOT NULL,
    "bis_loaded_at" TIMESTAMP NOT NULL,
    "SK_Aud_Insert" INT,
    "SK_Aud_Update" INT,
    "Timestamp_Last_Update" TIMESTAMP NOT NULL
)