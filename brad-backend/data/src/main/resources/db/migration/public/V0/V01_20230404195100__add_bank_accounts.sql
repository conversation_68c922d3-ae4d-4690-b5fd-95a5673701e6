-- ########################### BANK ACCOUNTS ###########################
CREATE SEQUENCE IF NOT EXISTS bank_Account_sequence_id_seq START 1 INCREMENT 1;
CREATE TABLE IF NOT EXISTS BANK_ACCOUNT (
    ID          INTEGER DEFAULT nextval('bank_Account_sequence_id_seq') PRIMARY KEY,
    LEGAL_ENTITY        VARCHAR(255)  NOT NULL,
    NAV_BANK_ACC_NUM VARCHAR(255) UNIQUE NOT NULL,
    K<PERSON><PERSON><PERSON>_REF       VARCHAR(255)  NOT NULL,
    CURRENCY_CODE    VARCHAR(255)  NOT NULL,
    STATUS           VARCHAR(255)  NOT NULL,
    CREATED_AT  TIMESTAMP    NOT NULL,
    CREATED_BY  VARCHAR(255) NOT NULL,
    UPDATED_AT  TIMESTAMP    NOT NULL,
    UPDATED_BY  VARCHAR(255) NOT NULL
);
