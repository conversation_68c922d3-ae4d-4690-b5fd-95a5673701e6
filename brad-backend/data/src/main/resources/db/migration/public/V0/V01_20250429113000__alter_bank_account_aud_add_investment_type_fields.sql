ALTER TABLE bank_account
    DROP CONSTRAINT IF EXISTS bank_account_bank_account_number_key,
    ALTER COLUMN bank_account_number DROP NOT NULL,
    ADD COLUMN IF NOT EXISTS isin VARCHAR(20),
    ADD COLUMN IF NOT EXISTS contract_id VARCHAR(20),
    ADD COLUMN IF NOT EXISTS sub_type VARCHAR(20),
    ADD COLUMN IF NOT EXISTS amount_deposited NUMERIC(18, 4),
    ADD COLUMN IF NOT EXISTS maturity_date DATE,
    ADD COLUMN IF NOT EXISTS nominal_amount NUMERIC(18, 4),
    ADD COLUMN IF NOT EXISTS coupon_payment_periodicity VARCHAR(20),
    ADD COLUMN IF NOT EXISTS coupon_rate NUMERIC(18, 4),
    ADD COLUMN IF NOT EXISTS interest NUMERIC(18, 4);
