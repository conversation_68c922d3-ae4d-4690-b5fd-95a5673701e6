CREATE SEQUENCE IF NOT EXISTS threshold_sequence_id_seq START 1 INCREMENT 1;
CREATE TABLE IF NOT EXISTS THRESHOLD (
    ID      BIGINT DEFAULT nextval('threshold_sequence_id_seq') PRIMARY KEY,
    <PERSON><PERSON><PERSON>URRENCY BIGINT REFERENCES CURRENCY(ID),
    <PERSON>_COUNTRY INTEGER REFERENCES COUNTRY(ID),
    AMOUNT  NUMERIC(15,2),
    CREATED_BY    VARCHAR(255)        NOT NULL,
    CREATED_AT    TIMESTAMP           NOT NULL,
    UPDATED_BY    VARCHAR(255)        NOT NULL,
    UPDATED_AT    TIMESTAMP           NOT NULL    DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(FK_CURRENCY, FK_COUNTRY)
);