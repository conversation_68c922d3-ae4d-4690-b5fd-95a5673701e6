DO
$$
    BEGIN
        --rename columns
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN entry_no TO "Entry No_";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN time_stamp TO "Timestamp";
        -- ALTER TABLE IF EXISTS NAVBI RENAME COLUMN id_company TO "id_company";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN document_no TO "Document No_";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN document_type TO "Document Type";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN posting_date TO "Posting Date";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN bank_account_no TO "Bank Account No_";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN bank_account_posting_group TO "Bank Account Posting Group";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN description TO "Description";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN source_code TO "Source Code";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN reason_code TO "Reason Code";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN busline TO "Busline";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN department TO "Department";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN amount TO "Amount";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN remaining_amount TO "Remaining Amount";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN currency TO "Currency";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN amount_lcy TO "Amount (LCY)";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN bal_account_no TO "Bal_ Account No_";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN bal_account_type TO "Bal_ Account Type";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN is_open TO "Open";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN reversed TO "Reversed";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN posted_by TO "Posted by";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN destination_code TO "Destination Code";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN partner_code TO "Partner Code";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN sk_aud_update TO "SK_Aud_Update";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN sk_aud_insert TO "SK_Aud_Insert";
        ALTER TABLE IF EXISTS NAVBI RENAME COLUMN external_document_no TO "External Document No_";

        --change types
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Timestamp" TYPE VARCHAR(50);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "id_company" TYPE VARCHAR(25);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Document No_" TYPE VARCHAR(20);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Document Type" TYPE VARCHAR(19);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Bank Account No_" TYPE VARCHAR(20);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Bank Account Posting Group" TYPE VARCHAR(100);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Description" TYPE VARCHAR(100);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Source Code" TYPE VARCHAR(10);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Reason Code" TYPE VARCHAR(10);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Busline" TYPE VARCHAR(20);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Department" TYPE VARCHAR(20);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Amount" TYPE NUMERIC(38, 20);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Remaining Amount" TYPE NUMERIC(38, 20);
        -- ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Currency" TYPE VARCHAR(10);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Amount (LCY)" TYPE NUMERIC(38, 20);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Bal_ Account No_" TYPE VARCHAR(20);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Bal_ Account Type" TYPE VARCHAR(12);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Open" TYPE BOOLEAN using "Open"::boolean;
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Reversed" TYPE BOOLEAN using "Reversed"::boolean;
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Posted by" TYPE VARCHAR(50);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Destination Code" TYPE VARCHAR(20);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "Partner Code" TYPE VARCHAR(20);
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "SK_Aud_Update" TYPE INTEGER;
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "SK_Aud_Insert" TYPE INTEGER;
        ALTER TABLE IF EXISTS NAVBI ALTER COLUMN "External Document No_" TYPE VARCHAR(50);
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Could not rename columns in NAVBI';
    END;
$$;

ALTER TABLE IF EXISTS RECONCILIATION_NAVBI DROP CONSTRAINT IF EXISTS reconciliation_navbi_pkey;
ALTER TABLE IF EXISTS RECONCILIATION_NAVBI DROP COLUMN IF EXISTS navbientry_no;

DO
$$
    BEGIN
        ALTER TABLE IF EXISTS NAVBI DROP CONSTRAINT IF EXISTS navbi_pkey;
        ALTER TABLE IF EXISTS NAVBI ADD PRIMARY KEY ("Timestamp");
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Could not add primary key to NAVBI';
    END;
$$;

ALTER TABLE IF EXISTS NAVBI DROP COLUMN IF EXISTS "Currency";
ALTER TABLE IF EXISTS NAVBI ADD COLUMN IF NOT EXISTS "Currency" bigint REFERENCES CURRENCY(id);

ALTER TABLE IF EXISTS RECONCILIATION_NAVBI ADD COLUMN IF NOT EXISTS navbi_id VARCHAR(50) REFERENCES NAVBI("Timestamp");
DO
$$
    BEGIN
        ALTER TABLE IF EXISTS RECONCILIATION_NAVBI ADD PRIMARY KEY (reconciliationid, navbi_id);
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Could not add primary key to RECONCILIATION_NAVBI';
    END;
$$;
