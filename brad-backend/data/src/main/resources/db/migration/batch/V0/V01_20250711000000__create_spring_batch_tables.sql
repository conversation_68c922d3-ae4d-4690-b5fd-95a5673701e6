-- Spring Batch schema initialization
-- This creates the required tables for Spring Batch job execution tracking

-- Create the batch schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS batch;

-- Job Instance table - stores job configuration
CREATE TABLE batch.BATCH_JOB_INSTANCE (
    JOB_INSTANCE_ID BIGSERIAL NOT NULL PRIMARY KEY,
    VERSION BIGINT,
    JOB_NAME VARCHAR(100) NOT NULL,
    JOB_KEY VARCHAR(32) NOT NULL,
    CONSTRAINT JOB_INST_UN UNIQUE (JOB_NAME, JOB_KEY)
);

-- Job Execution table - stores job execution information  
CREATE TABLE batch.BATCH_JOB_EXECUTION (
    JOB_EXECUTION_ID BIGSERIAL NOT NULL PRIMARY KEY,
    VERSION BIGINT,
    JOB_INSTANCE_ID BIGINT NOT NULL,
    CREATE_TIME TIMESTAMP NOT NULL,
    START_TIME TIMESTAMP DEFAULT NULL,
    END_TIME TIMESTAMP DEFAULT NULL,
    <PERSON>AT<PERSON> VARCHAR(10),
    EXIT_CODE VARCHAR(2500),
    <PERSON>XIT_MESSAGE VARCHAR(2500),
    LAST_UPDATED TIMESTAMP,
    CONSTRAINT JOB_INST_EXEC_FK FOREIGN KEY (JOB_INSTANCE_ID) REFERENCES batch.BATCH_JOB_INSTANCE(JOB_INSTANCE_ID)
);

-- Job Execution Parameters table - stores job parameters
CREATE TABLE batch.BATCH_JOB_EXECUTION_PARAMS (
    JOB_EXECUTION_ID BIGINT NOT NULL,
    PARAMETER_NAME VARCHAR(100) NOT NULL,
    PARAMETER_TYPE VARCHAR(100) NOT NULL,
    PARAMETER_VALUE VARCHAR(2500),
    IDENTIFYING CHAR(1) NOT NULL,
    CONSTRAINT JOB_EXEC_PARAMS_FK FOREIGN KEY (JOB_EXECUTION_ID) REFERENCES batch.BATCH_JOB_EXECUTION(JOB_EXECUTION_ID)
);

-- Step Execution table - stores step execution information
CREATE TABLE batch.BATCH_STEP_EXECUTION (
    STEP_EXECUTION_ID BIGSERIAL NOT NULL PRIMARY KEY,
    VERSION BIGINT NOT NULL,
    STEP_NAME VARCHAR(100) NOT NULL,
    JOB_EXECUTION_ID BIGINT NOT NULL,
    CREATE_TIME TIMESTAMP NOT NULL,
    START_TIME TIMESTAMP DEFAULT NULL,
    END_TIME TIMESTAMP DEFAULT NULL,
    STATUS VARCHAR(10),
    COMMIT_COUNT BIGINT,
    READ_COUNT BIGINT,
    FILTER_COUNT BIGINT,
    WRITE_COUNT BIGINT,
    READ_SKIP_COUNT BIGINT,
    WRITE_SKIP_COUNT BIGINT,
    PROCESS_SKIP_COUNT BIGINT,
    ROLLBACK_COUNT BIGINT,
    EXIT_CODE VARCHAR(2500),
    EXIT_MESSAGE VARCHAR(2500),
    LAST_UPDATED TIMESTAMP,
    CONSTRAINT JOB_EXEC_STEP_FK FOREIGN KEY (JOB_EXECUTION_ID) REFERENCES batch.BATCH_JOB_EXECUTION(JOB_EXECUTION_ID)
);

-- Step Execution Context table - stores step context data
CREATE TABLE batch.BATCH_STEP_EXECUTION_CONTEXT (
    STEP_EXECUTION_ID BIGINT NOT NULL PRIMARY KEY,
    SHORT_CONTEXT VARCHAR(2500) NOT NULL,
    SERIALIZED_CONTEXT TEXT,
    CONSTRAINT STEP_EXEC_CTX_FK FOREIGN KEY (STEP_EXECUTION_ID) REFERENCES batch.BATCH_STEP_EXECUTION(STEP_EXECUTION_ID)
);

-- Job Execution Context table - stores job context data
CREATE TABLE batch.BATCH_JOB_EXECUTION_CONTEXT (
    JOB_EXECUTION_ID BIGINT NOT NULL PRIMARY KEY,
    SHORT_CONTEXT VARCHAR(2500) NOT NULL,
    SERIALIZED_CONTEXT TEXT,
    CONSTRAINT JOB_EXEC_CTX_FK FOREIGN KEY (JOB_EXECUTION_ID) REFERENCES batch.BATCH_JOB_EXECUTION(JOB_EXECUTION_ID)
);

-- Sequences for Spring Batch (PostgreSQL specific)
CREATE SEQUENCE IF NOT EXISTS batch.BATCH_STEP_EXECUTION_SEQ MAXVALUE 9223372036854775807 NO CYCLE;
CREATE SEQUENCE IF NOT EXISTS batch.BATCH_JOB_EXECUTION_SEQ MAXVALUE 9223372036854775807 NO CYCLE;
CREATE SEQUENCE IF NOT EXISTS batch.BATCH_JOB_SEQ MAXVALUE 9223372036854775807 NO CYCLE;

-- Indexes for better performance
CREATE INDEX IDX_JOB_INST_JOB_NAME ON batch.BATCH_JOB_INSTANCE (JOB_NAME);
CREATE INDEX IDX_JOB_EXEC_INST_ID ON batch.BATCH_JOB_EXECUTION (JOB_INSTANCE_ID);
CREATE INDEX IDX_STEP_EXEC_JOB_EXEC_ID ON batch.BATCH_STEP_EXECUTION (JOB_EXECUTION_ID);
