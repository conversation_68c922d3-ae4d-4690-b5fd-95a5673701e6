CREATE TABLE IF NOT EXISTS country_aud (
    ID          INTEGER,
    REV         INTEGER,
    NAME        VARCHAR(255),
    CODE        VARCHAR(3),
    CREATED_AT  TIMESTAMP,
    CREATED_BY  VARCHAR(255),
    UPDATED_AT  TIMESTAMP,
    UPDATED_BY  VARCHAR(255),
    REVTYP<PERSON>     INTEGER,
    PRIMARY KEY (ID, REV)
);

ALTER TABLE bank_account_aud
    DROP COLUMN IF EXISTS COUNTRY,
    ADD COLUMN IF NOT EXISTS FK_COUNTRY INTEGER

