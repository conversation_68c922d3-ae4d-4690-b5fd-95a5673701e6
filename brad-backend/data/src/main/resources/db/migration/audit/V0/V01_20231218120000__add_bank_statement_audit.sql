CREATE TABLE IF NOT EXISTS bank_statement_aud (
    ID                  INTEGER,
    REV                 INTEGER,
    CURRENCY            VARCHAR(3),
    STATEMENT_ID        VARCHAR(50),
    INITIAL_DATE        TIMESTAMP,
    FINAL_DATE          TIMESTAMP,
    INITIAL_DIRECTION   VARCHAR(255),
    FINAL_DIRECTION     VARCHAR(255),
    INITIAL_AMOUNT      NUMERIC(15,2),
    FINAL_AMOUNT        NUMERIC(15,2),
    STATUS              VARCHAR(50),
    STATUS_DESCRIPTION  VARCHAR(255),
    BANK_ACCOUNT_ID     INTEGER,
    PREVIOUS_STATEMENT_ID  VARCHAR(50),
    CREATED_BY          VARCHAR(255),
    CREATED_AT          TIMESTAMP,
    UPDATED_BY          VARCHAR(255),
    UPDATED_AT          TIMESTAMP,
    REVTYPE             INTEGER,
    PRIMARY KEY (ID, REV)
)
