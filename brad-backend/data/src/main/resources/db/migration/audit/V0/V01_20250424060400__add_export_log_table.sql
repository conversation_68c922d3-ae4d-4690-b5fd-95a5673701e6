
DO $$
BEGIN
    CREATE TABLE IF NOT EXISTS export_log_aud
    (
        id             BIGINT,
        type           VA<PERSON><PERSON><PERSON>(255),
        file_url       VARCHAR,
        file_name      VARCHAR(255),
        row_count      BIGINT,
        status         VARCHAR(255),
        execution_time BIGINT,
        filters        VARCHAR,
        countries_list VARCHAR,
        created_at     TIMESTAMP,
        created_by     varchar(255),
        updated_at     TIMESTAMP,
        REVTYP<PERSON>          INTEGER,
        PRIMARY KEY (ID, REV)
    );
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'An error occurred: %', SQLERRM;
END $$;