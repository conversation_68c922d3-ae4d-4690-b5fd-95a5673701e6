
CREATE TABLE IF NOT EXISTS bank_statement_file_aud(
    id                      BIGINT,
    name                    <PERSON><PERSON><PERSON><PERSON>(255),
    url                     VARCHAR(255),
    processing_status       VARCHAR(255),
    checksum                VARCHAR(255),
    fk_execution_log        BIGINT,
    partition_key           VARCHAR(255),
    statement_id            BIGINT,
    created_at              TIMESTAMP,
    created_by              <PERSON><PERSON><PERSON><PERSON>(255),
    updated_at              TIMESTAMP,
    updated_by              <PERSON><PERSON><PERSON><PERSON>(255),
    rev                     BIGINT,
    revtype                 INTEGER,
    PRIMARY KEY (id, rev)
);