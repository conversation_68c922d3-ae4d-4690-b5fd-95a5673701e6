CREATE TABLE IF NOT EXISTS currency_aud (
    ID                  INTEGER,
    REV                 INTEGER,
    NAME                VARCHAR(45),
    CODE                VARCHAR(45),
    <PERSON>Y<PERSON><PERSON>              VARCHAR(45),
    CREATED_BY          VARCHAR(255),
    CREATED_AT          TIMESTAMP,
    UPDATED_BY          VARCHAR(255),
    UPDATED_AT          TIMESTAMP,
    REVTY<PERSON><PERSON>             INTEGER,
    PRIMARY KEY (ID, REV)
);


ALTER TABLE bank_account_aud
    ADD COLUMN IF NOT EXISTS IBAN  VARCHAR(255),
    ADD COLUMN IF NOT EXISTS CURRENCY_ID  VARCHAR(255);

ALTER TABLE bank_statement_aud
    ADD COLUMN IF NOT EXISTS CURRENCY_ID VARCHAR(255);

ALTER TABLE country_aud
    ADD COLUMN IF NOT EXISTS CURRENCY_ID VARCHAR(255);