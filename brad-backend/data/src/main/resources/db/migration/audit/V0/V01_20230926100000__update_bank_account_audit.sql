ALTER TABLE bank_account_aud
DROP COLUMN IF EXISTS LEGAL_ENTITY,
DROP COLUMN IF EXISTS NAV_BANK_ACC_NUM,
DROP COLUMN IF EXISTS KYRIBA_REF,
DROP COLUMN IF EXISTS CURRENCY_CODE,
DROP COLUMN IF EXISTS STATUS,
ADD COLUMN IF NOT EXISTS COMPANY_ID VARCHAR(255),
ADD COLUMN IF NOT EXISTS COUNTRY VARCHAR(255),
ADD COLUMN IF NOT EXISTS NAV_REFERENCE VARCHAR(255),
ADD COLUMN IF NOT EXISTS BENEFICIARY_NAME VARCHAR(255),
ADD COLUMN IF NOT EXISTS BENEFICIARY_ADDRESS VARCHAR(255),
ADD COLUMN IF NOT EXISTS BANK_ACCOUNT_NUMBER VARCHAR(255),
ADD COLUMN IF NOT EXISTS IBAN VARCHAR(255),
ADD COLUMN IF NOT EXISTS BANK_NAME VARCHAR(255),
ADD COLUMN IF NOT EXISTS SWIFT_CODE VARCHAR(255),
ADD COLUMN IF NOT EXISTS BANK_ROUTING_CODE VARCHAR(255),
ADD COLUMN IF NOT EXISTS SORT_CODE VARCHAR(255),
ADD COLUMN IF NOT EXISTS BRANCH_CODE VARCHAR(255),
ADD COLUMN IF NOT EXISTS RIB VARCHAR(255);
