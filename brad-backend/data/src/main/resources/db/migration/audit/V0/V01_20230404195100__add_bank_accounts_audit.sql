CREATE TABLE IF NOT EXISTS bank_account_aud (
  ID               INTEGER,
  REV              INTEGER,
  LEGAL_ENTITY     VARCHAR(255),
  NAV_BANK_ACC_NUM VARCHAR(255),
  KYRIBA_REF       VARCHAR(255),
  CURRENCY_CODE    VARCHAR(255),
  STATUS           VARCHAR(255),
  CREATED_AT       TIMESTAMP,
  CREATED_BY       VARCHAR(255),
  UPDATED_AT       TIMESTAMP,
  UPDATED_BY       VARCHAR(255),
  REVTYPE          INTEGER,
  PRIMARY KEY (ID, REV)
);