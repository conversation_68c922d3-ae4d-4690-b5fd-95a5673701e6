info.build.version: 0.1.0
server.port: ${brad_SERVER_PORT:8080}

spring:
  application.name: ${brad_SPRING_APPLICATION_NAME:brad}
  datasource.url: *******************************************************************************
  flyway:
    enabled: ${brad_SPRING_FLYWAY_ENABLED:false}
    locations: ${brad_SPRING_FLYWAY_LOCATIONS:db/migration}
    schemas: ${brad_SPRING_FLYWAY_SCHEMAS:audit,public,batch}
  batch:
    job:
      enabled: false  # Prevent auto-execution, jobs are triggered by Quartz
    jdbc:
      initialize-schema: never  # Use Flyway migrations instead
      table-prefix: "batch.BATCH_"  # Use batch schema for Spring Batch tables
    # Optimized batch processing configuration (audit recommendations)
    bale:
      # Reduced chunk size to prevent long-running transactions (audit recommendation)
      chunk-size: 500  # Reduced from 1000 to 500 for better transaction management
      # Enhanced retry configuration with exponential backoff
      retry:
        limit: 5  # Increased from 3 for better resilience
        backoff:
          initial-delay: 1000  # 1 second initial delay
          max-delay: 30000     # 30 seconds maximum delay
          multiplier: 2.0      # Exponential backoff multiplier
      # Reduced skip limit for better data quality (audit recommendation)
      skip:
        limit: 50  # Reduced from 100 (5% failure rate instead of 10%)
        policy: "strict"  # Strict skipping policy for production
      # Memory and performance monitoring
      monitoring:
        memory-threshold: 80  # Alert when memory usage exceeds 80%
        processing-timeout: 300  # 5 minutes timeout per chunk
        metrics-enabled: true

endpoints.default.web.enabled: false

#Spring actuator management configs
management.endpoints.web:
  base-path: / # Force base path to be root for instead of default "/actuator"
  exposure.include: health, prometheus   # Expose only health endpoint

#data
data:
  db:
    driver: ${brad_DATA_DB_DRIVER:org.postgresql.Driver}
    url: *******************************************************************************
    username: ${brad_DATA_DB_USERNAME:postgres}
    password: ${brad_DATA_DB_PASSWORD:postgres}
    application-schema: ${brad_DATA_DB_APPLICATION_SCHEMA:public}
    audit-schema: ${brad_DATA_DB_AUDIT_SCHEMA:audit}
    quartz-schema: ${brad_DATA_DB_QUARTZ_SCHEMA:public}
    max-pool-size: ${brad_DATA_DB_MAX_POOL_SIZE:15}
    flyway:
      repair: ${brad_DATA_DB_MAX_POOL_SIZE:false}
  events:
    check-connection-timeout: 15s

#API
api:
  swagger-enabled: ${brad_API_SWAGGER_ENABLED:true}
  self-host: ${brad_API_SELF_HOST:http://localhost:8080/}
  allowed-domains: ${brad_API_ALLOWED_DOMAINS:http://localhost:9000,http://localhost:8080,http://localhost:3000,http://localhost:4200}

#Network
network:
  jokes.url: http://api.icndb.com

#ACL
acl:
  skip: ${brad_ACL_SKIP:false}
  url: ${brad_ACL_HOST:http://internal-api-acl-staging.jumia.services}
  app-name: ${brad_ACL_APP_NAME:BRAD}
  cache:
    strategy: ${brad_ACL_CACHE_STRATEGY:in-memory}
    in-memory:
      expiration-duration: ${brad_ACL_CACHE_IN_MEMORY_EXPIRATION_DURATION:5m}
    redis:
      host: ${brad_ACL_REDIS_HOST:dev-communications.2smgfr.0001.euw1.cache.amazonaws.com}
      port: ${brad_ACL_REDIS_PORT:6379}
      username-key-prefix: ${brad_ACL_REDIS_USERNAME_KEY_PREFIX:brad}
      password: ${brad_ACL_REDIS_PASSWORD:dummy}
      expiration-duration: ${brad_ACL_REDIS_EXPIRATION_DURATION:5m}
      timeout: ${brad_ACL_REDIS_TIMEOUT:0s}
  migrator-user:
    username: ${brad_ACL_MIGRATOR_USER_USERNAME:dummy}
    password: ${brad_ACL_MIGRATOR_USER_PASSWORD:dummy}
