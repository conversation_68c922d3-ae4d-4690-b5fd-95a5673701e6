package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.data.brad.entities.fxrates.FxRatePsql;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.fake.FakeFxRates;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class FxRatePsqlTest {

    private static final FxRate fxRate1 = FakeFxRates.FAKE_FX_RATE_EUR_EGP;
    private static FxRate fxRate2;
    @BeforeAll
    public static void setUp() {
        fxRate2 = new FxRatePsql(fxRate1).toEntity();
    }

    @Test
    public void testId() {
        assertEquals(fxRate1.getId(), fxRate2.getId());
    }

    @Test
    public void testBaseCurrency() {
        assertEquals(fxRate1.getBaseCurrency().withoutDbFields(), fxRate2.getBaseCurrency().withoutDbFields());
    }

    @Test
    public void testQuoteCurrency() {
        assertEquals(fxRate1.getQuoteCurrency().withoutDbFields(), fxRate2.getQuoteCurrency().withoutDbFields());
    }

    @Test
    public void testRateDate() {
        assertEquals(fxRate1.getRateDate(), fxRate2.getRateDate());
    }

    @Test
    public void testBid() {
        assertEquals(fxRate1.getBid(), fxRate2.getBid());
    }

    @Test
    public void testBisLoadedAt() {
        assertEquals(fxRate1.getBisLoadedAt(), fxRate2.getBisLoadedAt());
    }

    @Test
    public void testSkAudInsert() {
        assertEquals(fxRate1.getSkAudInsert(), fxRate2.getSkAudInsert());
    }

    @Test
    public void testSkAudUpdate() {
        assertEquals(fxRate1.getSkAudUpdate(), fxRate2.getSkAudUpdate());
    }

    @Test
    public void testTimestampLastUpdate() {
        assertEquals(fxRate1.getTimestampLastUpdate(), fxRate2.getTimestampLastUpdate());
    }

    @Test
    public void testEntityFields() {
        Map<FxRate.SortingFields, String> expectedFields = Map.ofEntries(
                Map.entry(FxRate.SortingFields.BASE_CURRENCY, "baseCurrency"),
                Map.entry(FxRate.SortingFields.QUOTE_CURRENCY, "quoteCurrency"),
                Map.entry(FxRate.SortingFields.RATE_DATE, "rateDate"),
                Map.entry(FxRate.SortingFields.BID, "bid"),
                Map.entry(FxRate.SortingFields.BIS_LOADED_AT, "bisLoadedAt"),
                Map.entry(FxRate.SortingFields.SK_AUD_INSERT, "skAudInsert"),
                Map.entry(FxRate.SortingFields.SK_AUD_UPDATE, "skAudUpdate"),
                Map.entry(FxRate.SortingFields.TIMESTAMP_LAST_UPDATE, "timestampLastUpdate")
        );

        assertEquals(expectedFields, FxRatePsql.getEntityFields(FxRate.SortingFields.class));
    }

}
