package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.account.User;
import pt.jumia.services.brad.domain.entities.fake.FakeUsers;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class UserPsqlTest {

    private static final User user1 = FakeUsers.getFakeUsers(1).get(0);
    private static User user2;

    @BeforeAll
    public static void setUp() {
        user2 = new UserPsql(user1).toEntity();
    }

    @Test
    public void testToEntityGetId() {
        assertEquals(user1.getId(), user2.getId());
    }

    @Test
    public void testToEntityGetName() {
        assertEquals(user1.getName(), user2.getName());
    }

    @Test
    public void testToEntityGetEmail() {
        assertEquals(user1.getEmail(), user2.getEmail());
    }

    @Test
    public void testToEntityGetAccount() {
        assertEquals(user1.getAccount(), user2.getAccount());
    }

    @Test
    public void testToEntityGetCreatedAt() {
        assertEquals(user1.getCreatedAt(), user2.getCreatedAt());
    }

    @Test
    public void testToEntityGetCreatedBy() {
        assertEquals(user1.getCreatedBy(), user2.getCreatedBy());
    }

    @Test
    public void testToEntityGetUpdatedAt() {
        assertEquals(user1.getUpdatedAt(), user2.getUpdatedAt());
    }

    @Test
    public void testToEntityGetUpdatedBy() {
        assertEquals(user1.getUpdatedBy(), user2.getUpdatedBy());
    }

    @Test
    public void testEntityFields() {
        Map<User.SortingFields, String> expectedFields = Map.ofEntries(
                Map.entry(User.SortingFields.ID, "id"),
                Map.entry(User.SortingFields.USER_NAME, "userName"),
                Map.entry(User.SortingFields.EMAIL, "email"),
                Map.entry(User.SortingFields.CREATED_AT, "createdAt"),
                Map.entry(User.SortingFields.CREATED_BY, "createdBy"),
                Map.entry(User.SortingFields.UPDATED_AT, "updatedAt"),
                Map.entry(User.SortingFields.UPDATED_BY, "updatedBy"),
                Map.entry(User.SortingFields.HR_ROLE, "hrRole"),
                Map.entry(User.SortingFields.PERMISSION_TYPE, "permissionType"),
                Map.entry(User.SortingFields.MOBILE_PHONE_NUMBER, "mobilePhoneNumber")
        );

        assertEquals(expectedFields, UserPsql.getEntityFields(User.SortingFields.class));
    }

}