package pt.jumia.services.brad.data.brad.repository.psql;

import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlCountryRepositoryTest extends BaseRepositoryTest {

    private static final Currency A_CURRENCY = FakeCurrencies.NGN;

    @Test
    public void testFindAll() throws EntityErrorsException {
        Currency aCurrency = insertCurrency(A_CURRENCY);
        FakeCountries.ALL_COUNTRIES.forEach( country -> insertCountry(country.toBuilder().currency(aCurrency).build()));
        List<Country> countries = countryRepository.findAll();
        assertEquals(11, countries.size());
    }

    @Test
    public void testFindById(){
        Currency aCurrency = insertCurrency(A_CURRENCY);
        Country aCountry = insertCountry(FakeCountries.NIGERIA.toBuilder().currency(aCurrency).build());
        Optional<Country> country = countryRepository.findById(aCountry.getId());
        assertEquals("Nigeria", country.get().getName());
        assertEquals("NG", country.get().getCode());
    }

    @Test
    public void testFindByCode(){
        Currency aCurrency = insertCurrency(A_CURRENCY);
        Country aCountry = insertCountry(FakeCountries.NIGERIA.toBuilder().currency(aCurrency).build());
        Optional<Country> country = countryRepository.findByCode(aCountry.getCode());
        assertEquals("Nigeria", country.get().getName());
        assertEquals("NG", country.get().getCode());
    }

    @Test
    public void testFindById_empty(){
        Optional<Country> country = countryRepository.findById(999L);
        assertEquals(Optional.empty(), country);
    }
}
