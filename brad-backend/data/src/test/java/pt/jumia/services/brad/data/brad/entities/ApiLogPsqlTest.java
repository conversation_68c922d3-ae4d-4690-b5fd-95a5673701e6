package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.ApiLog;
import pt.jumia.services.brad.domain.entities.fake.FakeApiLogs;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ApiLogPsqlTest {

    private static final ApiLog apiLog1 = FakeApiLogs.getFakeApiLogs(1).get(0);
    private static ApiLog apiLog2;

    @BeforeAll
    public static void setUp() {
        apiLog2 = new ApiLogPsql(apiLog1).toEntity();
    }

    @Test
    public void testToEntityGetId() {
        assertEquals(apiLog1.getId(), apiLog2.getId());
    }

    @Test
    public void testToEntityGetLogType() {
        assertEquals(apiLog1.getLogType(), apiLog2.getLogType());
    }

    @Test
    public void testToEntityGetRequest() {
        assertEquals(apiLog1.getRequest(), apiLog2.getRequest());
    }

    @Test
    public void testToEntityGetResponse() {
        assertEquals(apiLog1.getResponse(), apiLog2.getResponse());
    }

    @Test
    public void testToEntityGetStatus() {
        assertEquals(apiLog1.getLogStatus(), apiLog2.getLogStatus());
    }

    @Test
    public void testToEntityGetCreatedAt() {
        assertEquals(apiLog1.getCreatedAt(), apiLog2.getCreatedAt());
    }

    @Test
    public void testToEntityGetCreatedBy() {
        assertEquals(apiLog1.getCreatedBy(), apiLog2.getCreatedBy());
    }

    @Test
    public void testToEntityGetUpdatedAt() {
        assertEquals(apiLog1.getUpdatedAt(), apiLog2.getUpdatedAt());
    }

    @Test
    public void testToEntityGetUpdatedBy() {
        assertEquals(apiLog1.getUpdatedBy(), apiLog2.getUpdatedBy());
    }

    @Test
    public void testEntityFields() {
        Map<ApiLog.SortingFields, String> expectedFields = Map.ofEntries(
                Map.entry(ApiLog.SortingFields.ID, "id"),
                Map.entry(ApiLog.SortingFields.LOG_TYPE, "logType"),
                Map.entry(ApiLog.SortingFields.RELATED_ENTITY_ID, "relatedEntityId"),
                Map.entry(ApiLog.SortingFields.LOG_STATUS, "logStatus"),
                Map.entry(ApiLog.SortingFields.CREATED_AT, "createdAt"),
                Map.entry(ApiLog.SortingFields.CREATED_BY, "createdBy"),
                Map.entry(ApiLog.SortingFields.UPDATED_AT, "updatedAt"),
                Map.entry(ApiLog.SortingFields.UPDATED_BY, "updatedBy")
        );

        assertEquals(expectedFields, ApiLogPsql.getEntityFields(ApiLog.SortingFields.class));
    }


    
}
