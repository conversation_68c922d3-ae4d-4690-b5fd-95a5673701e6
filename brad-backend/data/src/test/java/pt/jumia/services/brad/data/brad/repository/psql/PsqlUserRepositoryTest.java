package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.account.User;
import pt.jumia.services.brad.domain.entities.account.User.Status;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.entities.fake.FakeUsers;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.user.UserFilters;
import pt.jumia.services.brad.domain.entities.filter.user.UserSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlUserRepositoryTest extends BaseRepositoryTest {

    static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");

    private static final Account AN_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();

    private static final Account B_BANK_ACCOUNT = FakeAccounts.getFakeAccounts(2, null).get(1)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();

    private static final User A_USER = FakeUsers.getFakeUsers(21).get(20)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();
    private static final User ANOTHER_USER = FakeUsers.getFakeUsers(22).get(21)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();
    private static final User B_USER = FakeUsers.getFakeUsers(23).get(22)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();

    private static final Country A_COUNTRY = FakeCountries.NIGERIA;

    private static final Currency A_CURRENCY = FakeCurrencies.EUR;

    private static Account aAccount;
    private static Account bAccount;

    @BeforeEach
    public void setup(){
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        aAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .build());

         bAccount = insertAccount(B_BANK_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .build());
    }

    @SneakyThrows
    @Test
    public void insertAndFindTest() {

        User aUser = insertUser(A_USER.toBuilder().account(aAccount).build());
        User anotherUser = insertUser(ANOTHER_USER.toBuilder().account(aAccount).build());

        UserSortFilters userSortFilters = UserSortFilters.builder()
                .direction(OrderDirection.ASC)
                .build();
        List<User> user = userRepository.findAll(null,
                userSortFilters, null);

        List<User> withoutAccount = user.stream()
                .map(u -> u.toBuilder().account(null).build())
                .toList();

        assertThat(withoutAccount).containsExactlyInAnyOrder(aUser.toBuilder().account(null).build(),
                anotherUser.toBuilder().account(null).build());
    }

    @SneakyThrows
    @Test
    public void testFindAll(){
        User aUser = insertUser(A_USER.toBuilder().account(aAccount).build());
        insertUser(ANOTHER_USER.toBuilder().account(aAccount).build());

        UserSortFilters userSortFilters = UserSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(User.SortingFields.ID)
                .build();
        UserFilters userFilters = UserFilters.builder()
                .userName(aUser.getName())
                .email(aUser.getEmail())
                .status(Status.ACTIVE)
                .accountID(aUser.getAccount().getId())
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<User> users = userRepository.findAll(userFilters,
                userSortFilters, pageFilters).stream().map(User::withoutDbFields).toList();

        assertThat(users).containsExactly(aUser.withoutDbFields());

    }

    @SneakyThrows
    @Test
    public void testFindAll2(){

        User aUser = insertUser(A_USER.toBuilder().account(aAccount).build());
        User anotherUser = insertUser(ANOTHER_USER.toBuilder().account(aAccount).build());

        UserSortFilters userSortFilters = UserSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(User.SortingFields.USER_NAME)
                .build();
        UserFilters userFilters = UserFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<User> users = userRepository.findAll(userFilters,
                userSortFilters, pageFilters).stream().map(User::withoutDbFields).toList();

        assertThat(users).containsExactly(aUser.withoutDbFields(), anotherUser.withoutDbFields());

    }

    @SneakyThrows
    @Test
    public void testFindAll_filterByBankAccountIds(){

        insertUser(A_USER.toBuilder().account(aAccount).build());
        insertUser(B_USER.toBuilder().account(bAccount).build());
        insertUser(ANOTHER_USER.toBuilder().account(aAccount).build());

        assert bAccount.getId() != null;

        UserFilters userFilters = UserFilters.builder()
                .accountIds(List.of(bAccount.getId())).build();

        List<User> users = userRepository.findAll(userFilters,
                null, null).stream().map(User::withoutDbFields).toList();

        assertEquals(users.size(), 1);
    }

    @Test
    public void findByIdTest() {

        User insertedUser = insertUser(A_USER.toBuilder().account(aAccount).build());
        assert insertedUser.getId() != null;
        Optional<User> optionalUser = userRepository.findById(insertedUser.getId()).map(User::withoutDbFields);

        assertThat(optionalUser).contains(insertedUser.withoutDbFields());
    }

    @Test
    public void updateTest() {

        User insertedUser = insertUser(A_USER.toBuilder().account(aAccount).build());

        User toUpdate = insertedUser.toBuilder()
                .name("New name")
                .build();

        User updatedUser = userRepository.upsert(toUpdate);
        assert insertedUser.getId() != null;
        Optional<User> optionalUser = userRepository.findById(insertedUser.getId()).map(User::withoutDbFields);

        assertThat(optionalUser).contains(updatedUser.withoutDbFields());
        assertEquals(toUpdate.withoutDbFields(), updatedUser.withoutDbFields());
    }

    @Test
    public void deleteTest() {

        User insertedUser = userRepository.upsert(A_USER.toBuilder().account(aAccount).build());
        assert insertedUser.getId() != null;
        assertThat(userRepository.findById(insertedUser.getId()).map(User::withoutDbFields))
                .contains(insertedUser.withoutDbFields());

        userRepository.deleteById(insertedUser.getId());
        assertThat(userRepository.findById(insertedUser.getId())).isEmpty();
    }

    @SneakyThrows
    @Test
    public void testFindAll_withInactiveStatus_findsInactiveUsers() {
        //GIVEN
        User aUser = insertUser(A_USER.toBuilder().status(Status.INACTIVE).account(aAccount).build());
        insertUser(ANOTHER_USER.toBuilder().status(Status.ACTIVE).account(aAccount).build());

        UserSortFilters userSortFilters = UserSortFilters.builder()
            .direction(OrderDirection.ASC)
            .field(User.SortingFields.ID)
            .build();
        UserFilters userFilters = UserFilters.builder()
            .status(Status.INACTIVE)
            .build();
        PageFilters pageFilters = PageFilters.builder().build();
        //WHEN
        List<User> users = userRepository.findAll(userFilters,
            userSortFilters, pageFilters).stream().map(User::withoutDbFields).toList();
        //THEN
        assertThat(users).containsExactly(aUser.withoutDbFields());

    }

    @Test
    public void testSortAccountUsersByLegalRepresentativePermissionType() throws EntityErrorsException {
        //GIVEN
        var user1 = insertUser(A_USER.toBuilder().account(aAccount).permissionType("Analyst").build());
        var user2 = insertUser(A_USER.toBuilder().account(aAccount).permissionType("Account Manager").build());
        var user3 = insertUser(B_USER.toBuilder().account(aAccount).permissionType("Legal Representative").build());


        UserSortFilters userSortFilters = UserSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(User.SortingFields.ID)
                .build();
        UserFilters userFilters = UserFilters.builder()
                .build();
        PageFilters pageFilters = PageFilters.builder().build();
        //WHEN
        List<User> users = userRepository.findAll(userFilters,
                userSortFilters, pageFilters).stream().map(User::withoutDbFields).toList();
        //THEN
        assertThat(users).containsExactly(user3.withoutDbFields(), user1.withoutDbFields(), user2.withoutDbFields());
    }

}
