package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;


public class AccountPsqlTest {

    private static final Account ACCOUNT_1 = FakeAccounts.getFakeAccounts(1, null).get(0);
    private static Account account2;

    @BeforeAll
    public static void setUp() {
        account2 = new AccountPsql(ACCOUNT_1).toEntity();
    }

    @Test
    public void testToEntityGetId() {
        assertEquals(ACCOUNT_1.getId(), account2.getId());
    }

    @Test
    public void testToEntityGetCompanyID() {
        assertEquals(ACCOUNT_1.getCompanyID(), account2.getCompanyID());
    }

    @Test
    public void testToEntityGetCountry() {
        assertEquals(ACCOUNT_1.getCountry(), account2.getCountry());
    }

    @Test
    public void testToEntityGetNavReference() {
        assertEquals(ACCOUNT_1.getNavReference(), account2.getNavReference());
    }

    @Test
    public void testToEntityGetBeneficiaryName() {
        assertEquals(ACCOUNT_1.getBeneficiaryName(), account2.getBeneficiaryName());
    }

    @Test
    public void testToEntityGetBeneficiaryAddress() {
        assertEquals(ACCOUNT_1.getBeneficiaryAddress(), account2.getBeneficiaryAddress());
    }

    @Test
    public void testToEntityGetAccountNumber() {
        assertEquals(ACCOUNT_1.getAccountNumber(), account2.getAccountNumber());
    }

    @Test
    public void testToEntityGetAccountName() {
        assertEquals(ACCOUNT_1.getAccountName(), account2.getAccountName());
    }

    @Test
    public void testToEntityGetSwiftCode() {
        assertEquals(ACCOUNT_1.getSwiftCode(), account2.getSwiftCode());
    }

    @Test
    public void testToEntityGetBankRoutingCode() {
        assertEquals(ACCOUNT_1.getBankRoutingCode(), account2.getBankRoutingCode());
    }

    @Test
    public void testToEntityGetSortCode() {
        assertEquals(ACCOUNT_1.getSortCode(), account2.getSortCode());
    }

    @Test
    public void testToEntityGetBranchCode() {
        assertEquals(ACCOUNT_1.getBranchCode(), account2.getBranchCode());
    }

    @Test
    public void testToEntityGetRib() {
        assertEquals(ACCOUNT_1.getRib(), account2.getRib());
    }

    @Test
    public void testToEntityGetCreatedAt() {
        assertEquals(ACCOUNT_1.getCreatedAt(), account2.getCreatedAt());
    }

    @Test
    public void testToEntityGetCreatedBy() {
        assertEquals(ACCOUNT_1.getCreatedBy(), account2.getCreatedBy());
    }

    @Test
    public void testToEntityGetUpdatedAt() {
        assertEquals(ACCOUNT_1.getUpdatedAt(), account2.getUpdatedAt());
    }

    @Test
    public void testToEntityGetUpdatedBy() {
        assertEquals(ACCOUNT_1.getUpdatedBy(), account2.getUpdatedBy());
    }

    @Test
    public void testEntityFields() {
        Map<Account.SortingFields, String> expectedFields = Map.ofEntries(
                Map.entry(Account.SortingFields.ID, "id"),
                Map.entry(Account.SortingFields.COMPANY_ID, "companyID"),
                Map.entry(Account.SortingFields.COUNTRY, "country"),
                Map.entry(Account.SortingFields.NAV_REFERENCE, "navReference"),
                Map.entry(Account.SortingFields.BENEFICIARY_NAME, "beneficiaryName"),
                Map.entry(Account.SortingFields.BENEFICIARY_ADDRESS, "beneficiaryAddress"),
                Map.entry(Account.SortingFields.IBAN, "iban"),
                Map.entry(Account.SortingFields.ACCOUNT_NUMBER, "accountNumber"),
                Map.entry(Account.SortingFields.ACCOUNT_NAME, "accountName"),
                Map.entry(Account.SortingFields.SWIFT_CODE, "swiftCode"),
                Map.entry(Account.SortingFields.BANK_ROUTING_CODE, "bankRoutingCode"),
                Map.entry(Account.SortingFields.SORT_CODE, "sortCode"),
                Map.entry(Account.SortingFields.BRANCH_CODE, "branchCode"),
                Map.entry(Account.SortingFields.RIB, "rib"),
                Map.entry(Account.SortingFields.CURRENCY, "currency"),
                Map.entry(Account.SortingFields.TYPE, "type"),
                Map.entry(Account.SortingFields.SUB_TYPE, "subType"),
                Map.entry(Account.SortingFields.STATUS, "status"),
                Map.entry(Account.SortingFields.STATEMENT_SOURCE, "statementSource"),
                Map.entry(Account.SortingFields.STATEMENT_PERIODICITY, "statementPeriodicity"),
                Map.entry(Account.SortingFields.LAST_PROCESSED_STATEMENT_DATE, "lastProcessedStatementDate"),
                Map.entry(Account.SortingFields.CREATED_AT, "createdAt"),
                Map.entry(Account.SortingFields.CREATED_BY, "createdBy"),
                Map.entry(Account.SortingFields.UPDATED_AT, "updatedAt"),
                Map.entry(Account.SortingFields.UPDATED_BY, "updatedBy")
        );

        assertEquals(expectedFields, AccountPsql.getEntityFields(Account.SortingFields.class));
    }

}
