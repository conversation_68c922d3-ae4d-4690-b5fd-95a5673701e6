package pt.jumia.services.brad.data;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

/**
 * Test application, needs to be here so that we can launch the app for the DB tests
 */
@ComponentScan(basePackages = {"pt.jumia.services"})
@SpringBootApplication
public class DataTestApplication {

    @Bean
    public MeterRegistry meterRegistry() {
        return new SimpleMeterRegistry();
    }

    public static void main(String[] args) {
        SpringApplication.run(DataTestApplication.class, args);
    }
}
