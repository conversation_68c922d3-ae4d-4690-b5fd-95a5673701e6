package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.entities.fake.FakeDocuments;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class DocumentPsqlTest {

    private static final Document document1 = FakeDocuments.getFakeDocuments(1).get(0);
    private static Document document2;

    @BeforeAll
    public static void setUp() {
        document2 = new DocumentPsql(document1).toEntity();
    }

    @Test
    public void testToEntityGetId() {
        assertEquals(document1.getId(), document2.getId());
    }

    @Test
    public void testToEntityGetDocumentType() {
        assertEquals(document1.getDocumentType(), document2.getDocumentType());
    }

    @Test
    public void testToEntityGetName() {
        assertEquals(document1.getName(), document2.getName());
    }

    @Test
    public void testToEntityGetDescription() {
        assertEquals(document1.getDescription(), document2.getDescription());
    }
    @Test
    public void testToEntityGetAccount() {
        assertEquals(document1.getAccount(), document2.getAccount());
    }

    @Test
    public void testToEntityGetCreatedAt() {
        assertEquals(document1.getCreatedAt(), document2.getCreatedAt());
    }

    @Test
    public void testToEntityGetCreatedBy() {
        assertEquals(document1.getCreatedBy(), document2.getCreatedBy());
    }

    @Test
    public void testToEntityGetUpdatedAt() {
        assertEquals(document1.getUpdatedAt(), document2.getUpdatedAt());
    }

    @Test
    public void testToEntityGetUpdatedBy() {
        assertEquals(document1.getUpdatedBy(), document2.getUpdatedBy());
    }

    @Test
    public void testEntityFields() {
        Map<Document.SortingFields, String> expectedFields = Map.ofEntries(
                Map.entry(Document.SortingFields.ID, "id"),
                Map.entry(Document.SortingFields.DOCUMENT_TYPE, "documentType"),
                Map.entry(Document.SortingFields.NAME, "name"),
                Map.entry(Document.SortingFields.DESCRIPTION, "description"),
                Map.entry(Document.SortingFields.CREATED_AT, "createdAt"),
                Map.entry(Document.SortingFields.CREATED_BY, "createdBy"),
                Map.entry(Document.SortingFields.UPDATED_AT, "updatedAt"),
                Map.entry(Document.SortingFields.UPDATED_BY, "updatedBy")
        );

        assertEquals(expectedFields, DocumentPsql.getEntityFields(Document.SortingFields.class));
    }

}
