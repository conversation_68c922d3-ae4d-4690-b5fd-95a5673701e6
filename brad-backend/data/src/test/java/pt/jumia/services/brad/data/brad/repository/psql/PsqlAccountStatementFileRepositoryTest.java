package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.SortingFields;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatementFiles;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PsqlAccountStatementFileRepositoryTest extends BaseRepositoryTest {

    static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");

    private static final AccountStatementFile AN_ACCOUNT_STATEMENT_FILE = FakeAccountStatementFiles.getFakeAccountStatementFiles(2).get(1)
        .toBuilder()
        .id(null)
        .statement(null)
        .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
        .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
        .build();
    private static final AccountStatementFile ANOTHER_ACCOUNT_STATEMENT_FILE = FakeAccountStatementFiles.getFakeAccountStatementFiles(3).get(2)
        .toBuilder()
        .id(null)
        .statement(null)
        .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
        .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
        .build();
    ExecutionLog anExecutionLog;

    @SneakyThrows
    @Test
    public void insertAndFindAll_withOnlySortFilters_returnsAllInsertedStatementFilesInCorrectOrder() {
        //GIVEN
        anExecutionLog = insertExecutionLog(AN_ACCOUNT_STATEMENT_FILE.getExecutionLog().toBuilder().id(null).build());

        AccountStatementFile aAccountStatementFile = insertAccountStatementFile(
            AN_ACCOUNT_STATEMENT_FILE.toBuilder().executionLog(anExecutionLog).build());
        AccountStatementFile anotherAccountStatementFile = insertAccountStatementFile(
            ANOTHER_ACCOUNT_STATEMENT_FILE.toBuilder().executionLog(anExecutionLog).build());

        AccountStatementFileSortFilters accountStatementFileSortFilters = AccountStatementFileSortFilters.builder()
            .field(SortingFields.ID)
            .direction(OrderDirection.ASC)
            .build();

        //WHEN
        List<AccountStatementFile> accountStatementFiles = accountStatementFileRepository.findAll(null,
            accountStatementFileSortFilters, null);

        //THEN
        assertEquals(2, accountStatementFiles.size());
        assertEquals(aAccountStatementFile.getId(), accountStatementFiles.get(0).getId());
        assertEquals(anotherAccountStatementFile.getId(), accountStatementFiles.get(1).getId());

    }

    @SneakyThrows
    @Test
    public void findAll_withFiltersInAscendingOrder_fetchesCorrectStatementFiles() throws EntityErrorsException {

        //GIVEN
        anExecutionLog = insertExecutionLog(AN_ACCOUNT_STATEMENT_FILE.getExecutionLog().toBuilder().id(null).build());

        AccountStatementFile aAccountStatementFile = insertAccountStatementFile(
            AN_ACCOUNT_STATEMENT_FILE.toBuilder().executionLog(anExecutionLog).build());
        AccountStatementFile anotherAccountStatementFile = insertAccountStatementFile(
            ANOTHER_ACCOUNT_STATEMENT_FILE.toBuilder().executionLog(anExecutionLog).build());
        AccountStatementFileSortFilters accountStatementFileSortFilters = AccountStatementFileSortFilters.builder()
            .direction(OrderDirection.ASC)
            .field(AccountStatementFile.SortingFields.ID)
            .build();
        AccountStatementFileFilters accountStatementFileFilters = AccountStatementFileFilters.builder()
            .name(aAccountStatementFile.getName())
            .build();
        PageFilters pageFilters = PageFilters.builder().build();

        //WHEN
        List<AccountStatementFile> accountStatementFiles = accountStatementFileRepository.findAll(accountStatementFileFilters,
            accountStatementFileSortFilters, pageFilters);

        //THEN
        assertEquals(accountStatementFiles.get(0).getId(), aAccountStatementFile.getId());
    }

    @SneakyThrows
    @Test
    public void findAll_withFiltersInDescendingOrder_fetchesCorrectStatementFiles() {

        //GIVEN
        anExecutionLog = insertExecutionLog(AN_ACCOUNT_STATEMENT_FILE.getExecutionLog().toBuilder().id(null).build());

        AccountStatementFile aAccountStatementFile = insertAccountStatementFile(
            AN_ACCOUNT_STATEMENT_FILE.toBuilder().executionLog(anExecutionLog).build());
        AccountStatementFile anotherAccountStatementFile = insertAccountStatementFile(
            ANOTHER_ACCOUNT_STATEMENT_FILE.toBuilder().executionLog(anExecutionLog).build());
        AccountStatementFileSortFilters accountStatementFileSortFilters = AccountStatementFileSortFilters.builder()
            .direction(OrderDirection.DESC)
            .field(AccountStatementFile.SortingFields.ID)
            .build();
        AccountStatementFileFilters accountStatementFileFilters = AccountStatementFileFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();

        //WHEN
        List<AccountStatementFile> accountStatementFiles = accountStatementFileRepository.findAll(accountStatementFileFilters,
            accountStatementFileSortFilters, pageFilters);

        //THEN
        assertEquals(accountStatementFiles.get(0).getId(), anotherAccountStatementFile.getId());
        assertEquals(accountStatementFiles.get(1).getId(), aAccountStatementFile.getId());

    }

    @SneakyThrows
    @Test
    public void findAll_withNoFilters_fetchesCorrectStatementFiles() {
        //GIVEN
        anExecutionLog = insertExecutionLog(AN_ACCOUNT_STATEMENT_FILE.getExecutionLog().toBuilder().id(null).build());

        AccountStatementFile aAccountStatementFile = insertAccountStatementFile(
            AN_ACCOUNT_STATEMENT_FILE.toBuilder().executionLog(anExecutionLog).build());
        AccountStatementFile anotherAccountStatementFile = insertAccountStatementFile(
            ANOTHER_ACCOUNT_STATEMENT_FILE.toBuilder().executionLog(anExecutionLog).build());
        //WHEN
        List<AccountStatementFile> accountStatementFiles = accountStatementFileRepository.findAll(null, null, null);

        //THEN
        assertTrue(accountStatementFiles.stream().anyMatch(file -> file.getId().equals(aAccountStatementFile.getId())));
        assertTrue(accountStatementFiles.stream().anyMatch(file -> file.getId().equals(anotherAccountStatementFile.getId())));
    }

    @SneakyThrows
    @Test
    public void findAll_withCreatedAtFilters_fetchesStatementsCreatedInThePeriod() {

        //GIVEN
        anExecutionLog = insertExecutionLog(AN_ACCOUNT_STATEMENT_FILE.getExecutionLog().toBuilder().id(null).build());

        AccountStatementFile aAccountStatementFile = insertAccountStatementFile(
            AN_ACCOUNT_STATEMENT_FILE.toBuilder().executionLog(anExecutionLog).build());

        AccountStatementFileSortFilters accountStatementFileSortFilters = AccountStatementFileSortFilters.builder()
            .direction(OrderDirection.DESC)
            .build();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");

        AccountStatementFileFilters accountStatementFileFilters = AccountStatementFileFilters.builder()
            .name(aAccountStatementFile.getName())
            .createdAtFrom(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .createdAtTo(LocalDateTime.now())
            .build();
        PageFilters pageFilters = PageFilters.builder().build();

        //WHEN
        List<AccountStatementFile> accountStatementFiles = accountStatementFileRepository.findAll(accountStatementFileFilters,
            accountStatementFileSortFilters, pageFilters);

        //THEN
        assertEquals(accountStatementFiles.get(0).getId(), aAccountStatementFile.getId());
    }

    @Test
    public void findByIdTest() {
        //GIVEN
        anExecutionLog = insertExecutionLog(AN_ACCOUNT_STATEMENT_FILE.getExecutionLog().toBuilder().id(null).build());

        AccountStatementFile insertedAccountStatementFile = insertAccountStatementFile(
            AN_ACCOUNT_STATEMENT_FILE.toBuilder().executionLog(anExecutionLog).build());

        assert insertedAccountStatementFile.getId() != null;

        //WHEN
        Optional<AccountStatementFile> optionalAccountStatementFile = accountStatementFileRepository.findById(insertedAccountStatementFile.getId());

        //THEN
        assertTrue(optionalAccountStatementFile.isPresent());
        assertEquals(optionalAccountStatementFile.get().getId(), insertedAccountStatementFile.getId());
    }

}
