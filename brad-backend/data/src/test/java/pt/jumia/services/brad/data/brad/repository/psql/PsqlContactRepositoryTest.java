package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Contact;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeContacts;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.contact.ContactFilters;
import pt.jumia.services.brad.domain.entities.filter.contact.ContactSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlContactRepositoryTest extends BaseRepositoryTest {

    static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");

    private static final Account A_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();

    private static final Account B_ACCOUNT = FakeAccounts.getFakeAccounts(2, null).get(1)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();

    private static final Contact A_CONTACT = FakeContacts.getFakeContacts(21).get(20)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();

    private static final Contact ANOTHER_CONTACT = FakeContacts.getFakeContacts(22).get(21)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();

    private static final Contact B_CONTACT = FakeContacts.getFakeContacts(23).get(22)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();

    private static final Country A_COUNTRY = FakeCountries.NIGERIA;
    private static final Currency A_CURRENCY = FakeCurrencies.NGN;

    @SneakyThrows
    @Test
    public void insertAndFindTest() {

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Contact aContact = insertContact(A_CONTACT.toBuilder().account(aAccount).build());
        Contact anotherContact = insertContact(ANOTHER_CONTACT.toBuilder().account(aAccount).build());

        ContactSortFilters contactSortFilters = ContactSortFilters.builder()
                .direction(OrderDirection.ASC)
                .build();
        List<Contact> contacts = contactRepository.findAll(null,
                contactSortFilters, null);

        List<Contact> withoutAccount = contacts.stream()
                .map(contact -> contact.toBuilder().account(null).build())
                .toList();

        assertThat(withoutAccount).containsExactlyInAnyOrder(aContact.toBuilder().account(null).build(),
                anotherContact.toBuilder().account(null).build());
    }

    @SneakyThrows
    @Test
    public void testFindAll(){
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(currency).build());
        Contact aContact = insertContact(A_CONTACT.toBuilder().account(aAccount).build());
        insertContact(ANOTHER_CONTACT.toBuilder().account(aAccount).build());

        ContactSortFilters contactSortFilters = ContactSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(Contact.SortingFields.ID)
                .build();
        ContactFilters contactFilters = ContactFilters.builder()
                .contactType(List.of(aContact.getContactType().toString()))
                .name(aContact.getName())
                .email(aContact.getEmail())
                .accountID(aContact.getAccount().getId())
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Contact> contacts = contactRepository.findAll(contactFilters,
                contactSortFilters, pageFilters).stream()
                .map(Contact::withoutDbFields).toList();

        assertThat(contacts).containsExactly(aContact.withoutDbFields());

    }

    @SneakyThrows
    @Test
    public void testFindAll2(){
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Contact aContact = insertContact(A_CONTACT.toBuilder().account(aAccount).build());
        Contact anotherContact = insertContact(ANOTHER_CONTACT.toBuilder().account(aAccount).build());

        ContactSortFilters contactSortFilters = ContactSortFilters.builder()
                .direction(OrderDirection.DESC)
                .field(Contact.SortingFields.ID)
                .build();
        ContactFilters contactFilters = ContactFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<Contact> contacts = contactRepository.findAll(contactFilters,
                contactSortFilters, pageFilters).stream()
                .map(Contact::withoutDbFields).toList();

        assertThat(contacts).containsExactly(anotherContact.withoutDbFields(), aContact.withoutDbFields());

    }

    @SneakyThrows
    @Test
    public void testFindAll_filterByBankAccountIds(){
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(currency).build());
        Account bAccount = insertAccount(B_ACCOUNT.toBuilder().country(country).currency(currency).build());

        insertContact(A_CONTACT.toBuilder().account(aAccount).build());
        insertContact(B_CONTACT.toBuilder().account(bAccount).build());
        insertContact(ANOTHER_CONTACT.toBuilder().account(aAccount).build());

        assert bAccount.getId() != null;

        ContactFilters contactFilters = ContactFilters.builder()
                .accountIds(List.of(bAccount.getId())).build();

        List<Contact> contacts = contactRepository.findAll(contactFilters, null, null).stream()
                .map(Contact::withoutDbFields).toList();

        assertEquals(contacts.size(), 1);
    }

    @Test
    public void findByEmail(){
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Contact insertedContact = insertContact(A_CONTACT.toBuilder().account(aAccount).build());

        Optional<Contact> optionalContact = contactRepository.findByEmail(insertedContact.getEmail())
                .map(Contact::withoutDbFields);

        assertThat(optionalContact).contains(insertedContact.withoutDbFields());
    }

    @Test
    public void findByIdTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Contact insertedContact = insertContact(A_CONTACT.toBuilder().account(aAccount).build());
        assert insertedContact.getId() != null;
        Optional<Contact> optionalContact = contactRepository.findById(insertedContact.getId())
                .map(Contact::withoutDbFields);

        assertThat(optionalContact).contains(insertedContact.withoutDbFields());
    }

    @Test
    public void updateTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Contact insertedContact = insertContact(A_CONTACT.toBuilder().account(aAccount).build());

        Contact toUpdate = insertedContact.toBuilder()
                .name("New name")
                .build();

        Contact updatedContact = contactRepository.upsert(toUpdate);
        assert insertedContact.getId() != null;
        Optional<Contact> optionalContact = contactRepository.findById(insertedContact.getId()).map(Contact::withoutDbFields);

        assertThat(optionalContact).contains(updatedContact.withoutDbFields());
        assertEquals(toUpdate.withoutDbFields(), updatedContact.withoutDbFields());
    }

    @Test
    public void deleteTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Contact insertedContact = contactRepository.upsert(A_CONTACT.toBuilder().account(aAccount).build())
                .toBuilder().account(aAccount).build();
        assert insertedContact.getId() != null;
        assertThat(contactRepository.findById(insertedContact.getId()))
                .map(Contact::withoutDbFields)
                .contains(insertedContact.withoutDbFields());

        contactRepository.deleteById(insertedContact.getId());
        assertThat(contactRepository.findById(insertedContact.getId())).isEmpty();
    }





}