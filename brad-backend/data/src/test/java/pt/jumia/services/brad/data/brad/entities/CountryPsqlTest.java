package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class CountryPsqlTest {

    private static final Country country1 = FakeCountries.ALGERIA;
    private static Country country2;

    @BeforeAll
    public static void setUp() {
        country2 = new CountryPsql(country1).toEntity();
    }

    @Test
    public void testToEntityGetId() {
        assertEquals(country1.getId(), country2.getId());
    }

    @Test
    public void testToEntityGetName() {
        assertEquals(country1.getName(), country2.getName());
    }

    @Test
    public void testToEntityGetCode() {
        assertEquals(country1.getCode(), country2.getCode());
    }

}
