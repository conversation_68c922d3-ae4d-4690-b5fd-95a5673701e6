package pt.jumia.services.brad.data.brad.repository.psql;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.Setting;
import pt.jumia.services.brad.domain.entities.filter.setting.SettingFilters;
import pt.jumia.services.brad.domain.entities.filter.setting.SettingSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;


public class PsqlSettingRepositoryTest extends BaseRepositoryTest {

    private static final Setting SETTING_1 = Setting.builder()
        .property("some.property.for.test.that.does.not.matter")
        .type(Setting.Type.DEFAULT)
        .overrideKey(null)
        .value("hello")
        .description("hi mate")
        .build();

    private static final Setting SETTING_2 = Setting.builder()
        .property("test.property.that.matters")
        .type(Setting.Type.DEFAULT)
        .overrideKey(null)
        .value("foo")
        .description("just some setting for testing")
        .build();

    private static final Setting SETTING_2_OVERRIDE = Setting.builder()
        .property(SETTING_2.getProperty())
        .type(Setting.Type.OVERRIDE)
        .overrideKey("some-override")
        .value("bar")
        .description("just some setting for testing")
        .build();

    @Test
    public void insertDelete() throws EntityErrorsException {

        long countBefore = settingRepository.findAll(null, null).size();
        Setting setting = settingRepository.upsert(SETTING_1);
        long countAfterInsert = settingRepository.findAll(null, null).size();
        settingRepository.deleteById(setting.getId());
        long countAfterDelete = settingRepository.findAll(null, null).size();

        assertThat(countAfterInsert).isEqualTo(countBefore + 1);
        assertThat(countAfterDelete).isEqualTo(countBefore);
    }

    @Test
    public void update() {

        Setting setting = insert(SETTING_1);

        Setting updatedSetting = settingRepository.upsert(setting.toBuilder()
            .description("description updated")
            .value("value updated")
            .updatedBy("updated by test")
            .build());

        assertThat(updatedSetting.getDescription()).isEqualTo("description updated");
        assertThat(updatedSetting.getValue()).isEqualTo("value updated");
        assertThat(updatedSetting.getUpdatedBy()).isEqualTo("updated by test");
    }

    @Test
    public void findWithFilters() throws EntityErrorsException {

        Setting setting2Override = insert(SETTING_2_OVERRIDE);

        SettingFilters settingFilters = SettingFilters.builder()
            .property(SETTING_2.getProperty())
            .overrideKey("some-override")
            .type(Setting.Type.OVERRIDE)
            .value("bar")
            .build();

        List<Setting> allSettings = settingRepository.findAll(settingFilters, null);

        assertThat(allSettings)
            .hasSize(1)
            .map(Setting::withoutDbFields)
            .contains(setting2Override.withoutDbFields());
    }

    @Test
    public void findWithFiltersAndPaginationAndSort() throws EntityErrorsException {

        Setting setting1 = insert(SETTING_1);
        Setting setting2 = insert(SETTING_2.toBuilder().type(Setting.Type.OVERRIDE).build());
        Setting setting2Override = insert(SETTING_2_OVERRIDE);

        SettingFilters settingFilters = SettingFilters.builder()
            .type(Setting.Type.OVERRIDE)
            .build();
        SettingSortFilters settingSortFilters = SettingSortFilters.builder()
            .direction(OrderDirection.ASC)
            .field(Setting.SortingFields.PROPERTY)
            .build();

        List<Setting> allSettings = settingRepository.findAll(settingFilters, settingSortFilters);
        long count = settingRepository.count(settingFilters);

        assertThat(allSettings)
            .hasSize((int) count)
            .map(Setting::withoutDbFields)
            .contains(setting2.withoutDbFields(), setting2Override.withoutDbFields())
            .doesNotContain(setting1.withoutDbFields());
    }

    @Test
    public void findById() {
        // prepare
        Setting setting = insert(SETTING_1);

        // execute
        Optional<Setting> optFetched = settingRepository.findById(setting.getId());

        // verify
        assertThat(optFetched)
            .isNotEmpty()
            .map(Setting::withoutDbFields)
            .contains(setting.withoutDbFields());
    }


}
