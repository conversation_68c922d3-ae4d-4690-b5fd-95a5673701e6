package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.account.Contact;
import pt.jumia.services.brad.domain.entities.fake.FakeContacts;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ContactPsqlTest {

    private static final Contact contact1 = FakeContacts.getFakeContacts(1).get(0);
    private static Contact contact2;

    @BeforeAll
    public static void setUp() {
        contact2 = new ContactPsql(contact1).toEntity();
    }

    @Test
    public void testToEntityGetId() {
        assertEquals(contact1.getId(), contact2.getId());
    }

    @Test
    public void testToEntityGetContactType() {
        assertEquals(contact1.getContactType(), contact2.getContactType());
    }

    @Test
    public void testToEntityGetName() {
        assertEquals(contact1.getName(), contact2.getName());
    }

    @Test
    public void testToEntityGetEmail() {
        assertEquals(contact1.getEmail(), contact2.getEmail());
    }

    @Test
    public void testToEntityGetAccount() {
        assertEquals(contact1.getAccount(), contact2.getAccount());
    }

    @Test
    public void testToEntityGetCreatedAt() {
        assertEquals(contact1.getCreatedAt(), contact2.getCreatedAt());
    }

    @Test
    public void testToEntityGetCreatedBy() {
        assertEquals(contact1.getCreatedBy(), contact2.getCreatedBy());
    }

    @Test
    public void testToEntityGetUpdatedAt() {
        assertEquals(contact1.getUpdatedAt(), contact2.getUpdatedAt());
    }

    @Test
    public void testToEntityGetUpdatedBy() {
        assertEquals(contact1.getUpdatedBy(), contact2.getUpdatedBy());
    }

    @Test
    public void testEntityFields() {
        Map<Contact.SortingFields, String> expectedFields = Map.ofEntries(
                Map.entry(Contact.SortingFields.ID, "id"),
                Map.entry(Contact.SortingFields.CONTACT_TYPE, "contactType"),
                Map.entry(Contact.SortingFields.NAME, "name"),
                Map.entry(Contact.SortingFields.EMAIL, "email"),
                Map.entry(Contact.SortingFields.MOBILE_PHONE_NUMBER, "mobilePhoneNumber"),
                Map.entry(Contact.SortingFields.CREATED_AT, "createdAt"),
                Map.entry(Contact.SortingFields.CREATED_BY, "createdBy"),
                Map.entry(Contact.SortingFields.UPDATED_AT, "updatedAt"),
                Map.entry(Contact.SortingFields.UPDATED_BY, "updatedBy")
        );

        assertEquals(expectedFields, ContactPsql.getEntityFields(Contact.SortingFields.class));
    }

}
