package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class CurrencyPsqlTest {

    private static final Currency currency1 = FakeCurrencies.EUR;
    private static Currency currency2;

    @BeforeAll
    public static void setUp() {
        currency2 = new CurrencyPsql(currency1).toEntity();
    }

    @Test
    public void testToEntityGetId() {
        assertEquals(currency1.getId(), currency2.getId());
    }

    @Test
    public void testToEntityGetName() {
        assertEquals(currency1.getName(), currency2.getName());
    }


    @Test
    public void testToEntityGetCode() {
        assertEquals(currency1.getCode(), currency2.getCode());
    }

    @Test
    public void testToEntityGetSymbol() {
        assertEquals(currency1.getSymbol(), currency2.getSymbol());
    }


}
