package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.fake.*;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleFilters;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleSortFilters;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.enumerations.ReconciliationStatus;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;

public class PsqlBaleRepositoryTest extends BaseRepositoryTest{

    static DateTimeFormatter dateTimeformatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");
    static DateTimeFormatter dateformatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final Account AN_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .build();

    private static final AccountStatement AN_ACCOUNT_STATEMENT = FakeAccountStatements.getFakeAccountStatements(1, AN_ACCOUNT).get(0)
            .toBuilder()
            .initialDate(LocalDate.parse("2023-04-19", dateformatter))
            .finalDate(LocalDate.parse("2023-04-19", dateformatter))
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .build();

    private static final Transaction A_TRANSACTION = FakeTransaction.getFakeCreditTransactions(1,AN_ACCOUNT_STATEMENT).get(0)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .build();

    private static final List<Bale> bales = new ArrayList<>();
    private static final String companyID = "newCompanyID";
    Currency currency;
    Country country;
    Account account;


    @BeforeEach
    public void setUp(){
        bales.clear();
        this.currency = insertCurrency(FakeCurrencies.NGN);
        this.country = insertCountry(FakeCountries.NIGERIA.toBuilder().currency(currency).build());
        this.account = insertAccount(FakeAccounts.FAKE_ACCOUNT.toBuilder()
                .currency(currency)
                .companyID(companyID)
                .country(country)
                .build());

        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .idCompany(companyID)
                    .transactionCurrency(currency)
                    .account(account)
                    .build());
        });
        bales.addAll(tempBales);

        bales.forEach(bale -> {
            try {
                bradBaleRepository.createPartition(bale.getIdCompany());
            } catch (DatabaseErrorsException e) {
                throw new RuntimeException(e);
            }
        });
    }

    @SneakyThrows
    @Test
    public void testFindAll(){
        List<Bale> baleToInsert = insertBales(bales.subList(0, 5));

        BaleSortFilters baleSortFilters = BaleSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(Bale.SortingFields.ENTRY_NO)
                .build();
        BaleFilters baleFilters = BaleFilters.builder()
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters,
                baleSortFilters, pageFilters);

        assertFalse(baleList.isEmpty());

        assertThat(baleList).hasSize(baleToInsert.size());

    }

    @SneakyThrows
    @Test
    public void testFindAll2(){
        List<Bale> baleToInsert = insertBales(bales.subList(0, 5));


        List<Bale> baleList = bradBaleRepository.findAll(null,
                null, null);

        assertThat(baleList).hasSize(baleToInsert.size());
    }

    @Test
    public void findAll_withIdCompany_returnOnlyBalesWithIdCompany() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedIdCompany = "IDC1";

        Bale baleWithDifferentIdCompany = bales.get(0).toBuilder()
                .entryNo(1000)
                .idCompany(expectedIdCompany)
                .build();

        bradBaleRepository.createPartition(baleWithDifferentIdCompany.getIdCompany());
        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentIdCompany));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);


        BaleFilters baleFilters = BaleFilters.builder()
                .idCompany(expectedIdCompany)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getIdCompany()).isEqualTo(expectedIdCompany);

        //testing with exactFilters
        String exactIdCompany = "ID";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .idCompany(exactIdCompany)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .idCompany(expectedIdCompany)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
        assertThat(baleList3.get(0).getIdCompany()).isEqualTo(expectedIdCompany);


    }

    @Test
    public void findAll_withBankAccount_returnOnlyBalesWithBankAccount() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedNavReference = "newNavReference";

        Account account = bales.get(0).getAccount().toBuilder().id(null)
                .accountNumber("newBankAccountNumber123")
                .navReference(expectedNavReference).build();

        Bale baleWithDifferentBankAccount = bales.get(0).toBuilder()
                .entryNo(1000)
                .account(insertAccount(account))
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentBankAccount));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .accountNumber(expectedNavReference)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getAccount().getNavReference()).isEqualTo(expectedNavReference);

        //testing with exactFilters
        String exactNavReference = "newNav";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .accountNumber(exactNavReference)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .accountNumber(expectedNavReference)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
        assertThat(baleList3.get(0).getAccount().getNavReference()).isEqualTo(expectedNavReference);
    }

    @Test
    public void findAll_withEntryNo_returnOnlyBalesWithEntryNo() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        int expectedEntryNo = 1000;

        Bale baleWithDifferentEntryNo = bales.get(0).toBuilder()
                .entryNo(expectedEntryNo)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentEntryNo));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .entryNo(List.of(expectedEntryNo))
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getEntryNo()).isEqualTo(expectedEntryNo);
    }

    @Test
    public void findAll_withDocumentNo_returnOnlyBalesWithDocumentNo() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedDocumentNo = "newDocumentNo";

        Bale baleWithDifferentDocumentNo = bales.get(0).toBuilder()
                .entryNo(1000)
                .documentNo(expectedDocumentNo)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentDocumentNo));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .documentNo(expectedDocumentNo)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getDocumentNo()).isEqualTo(expectedDocumentNo);

        //testing with exactFilters
        String exactDocumentNo = "newDocument";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .documentNo(exactDocumentNo)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .documentNo(expectedDocumentNo)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
        assertThat(baleList3.get(0).getDocumentNo()).isEqualTo(expectedDocumentNo);
    }

    @Test
    public void findAll_withDocumentType_returnOnlyBalesWithDocumentType() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedDocumentType = "newDocumentType";

        Bale baleWithDifferentDocumentType = bales.get(0).toBuilder()
                .entryNo(1000)
                .documentType(expectedDocumentType)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentDocumentType));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .documentType(expectedDocumentType)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getDocumentType()).isEqualTo(expectedDocumentType);

        //testing with exactFilters
        String exactDocumentType = "newDocument";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .documentType(exactDocumentType)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .documentType(expectedDocumentType)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
        assertThat(baleList3.get(0).getDocumentType()).isEqualTo(expectedDocumentType);

    }

    @Test
    public void findAll_withPostingDate_returnOnlyBalesWithPostingDate() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedPostingDate = "2021-01-01";

        Bale baleWithDifferentPostingDate = bales.get(0).toBuilder()
                .entryNo(1000)
                .postingDate(DateParser.parseToLocalDate(expectedPostingDate))
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentPostingDate));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .postingDateStart(DateParser.parseToLocalDate(expectedPostingDate))
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getPostingDate()).isEqualTo(DateParser.parseToLocalDate(expectedPostingDate));
    }

    @Test
    public void findAll_withBankAccountPostingGroup_returnOnlyBalesWithBankAccountPostingGroup() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedBankAccountPostingGroup = "newBankAccountPostingGroup";

        Bale baleWithDifferentBankAccountPostingGroup = bales.get(0).toBuilder()
                .entryNo(1000)
                .accountPostingGroup(expectedBankAccountPostingGroup)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentBankAccountPostingGroup));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .accountPostingGroup(expectedBankAccountPostingGroup)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getAccountPostingGroup()).isEqualTo(expectedBankAccountPostingGroup);

        //testing with exactFilters
        String exactBankAccountPostingGroup = "newBankAccount";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .accountPostingGroup(exactBankAccountPostingGroup)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .accountPostingGroup(expectedBankAccountPostingGroup)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
        assertThat(baleList3.get(0).getAccountPostingGroup()).isEqualTo(expectedBankAccountPostingGroup);
    }

    @Test
    public void findAll_withDescription_returnOnlyBalesWithDescription() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedDescription = "newDescription";

        Bale baleWithDifferentDescription = bales.get(0).toBuilder()
                .entryNo(1000)
                .description(expectedDescription)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentDescription));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .description(expectedDescription)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getDescription()).isEqualTo(expectedDescription);

        //testing with exactFilters
        String exactDescription = "newDesc";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .description(exactDescription)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .description(expectedDescription)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
    }

    @Test
    public void findAll_withSourceCode_returnOnlyBalesWithSourceCode() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedSourceCode = "newSourceCode";

        Bale baleWithDifferentSourceCode = bales.get(0).toBuilder()
                .entryNo(1000)
                .sourceCode(expectedSourceCode)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentSourceCode));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .sourceCode(expectedSourceCode)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getSourceCode()).isEqualTo(expectedSourceCode);

        //testing with exactFilters
        String exactSourceCode = "newSource";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .sourceCode(exactSourceCode)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .sourceCode(expectedSourceCode)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
        assertThat(baleList3.get(0).getSourceCode()).isEqualTo(expectedSourceCode);
    }

    @Test
    public void findAll_withReasonCode_returnOnlyBalesWithReasonCode() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedReasonCode = "newReasonCode";

        Bale baleWithDifferentReasonCode = bales.get(0).toBuilder()
                .entryNo(1000)
                .reasonCode(expectedReasonCode)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentReasonCode));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .reasonCode(expectedReasonCode)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getReasonCode()).isEqualTo(expectedReasonCode);

        //testing with exactFilters
        String exactReasonCode = "newReason";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .reasonCode(exactReasonCode)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .reasonCode(expectedReasonCode)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
        assertThat(baleList3.get(0).getReasonCode()).isEqualTo(expectedReasonCode);
    }

    @Test
    public void findAll_withBusLine_returnOnlyBalesWithBusLine() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedBusLine = "newBusLine";

        Bale baleWithDifferentBusLine = bales.get(0).toBuilder()
                .entryNo(1000)
                .busLine(expectedBusLine)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentBusLine));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .busLine(expectedBusLine)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getBusLine()).isEqualTo(expectedBusLine);

        //testing with exactFilters
        String exactBusLine = "newBus";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .busLine(exactBusLine)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .busLine(expectedBusLine)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
        assertThat(baleList3.get(0).getBusLine()).isEqualTo(expectedBusLine);
    }

    @Test
    public void findAll_withDepartment_returnOnlyBalesWithDepartment() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedDepartment = "newDepartment";

        Bale baleWithDifferentDepartment = bales.get(0).toBuilder()
                .entryNo(1000)
                .department(expectedDepartment)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentDepartment));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .department(expectedDepartment)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getDepartment()).isEqualTo(expectedDepartment);

        //testing with exactFilters
        String exactDepartment = "newDep";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .department(exactDepartment)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .department(expectedDepartment)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
        assertThat(baleList3.get(0).getDepartment()).isEqualTo(expectedDepartment);
    }

    @Test
    public void findAll_withDirection_returnOnlyBalesWithDirection() throws EntityErrorsException, DatabaseErrorsException, ParseException {
        //other created bales are CREDIT
        Direction expectedDirection = Direction.DEBIT;

        Bale baleWithDifferentDirection = bales.get(0).toBuilder()
                .entryNo(1000)
                .direction(expectedDirection)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentDirection));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .direction(List.of(expectedDirection.name()))
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getDirection()).isEqualTo(expectedDirection);
    }

    @Test
    public void findAll_withAmount_returnOnlyBalesWithAmount() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        BigDecimal expectedAmount = new BigDecimal("1000").setScale(4, BigDecimal.ROUND_HALF_UP);

        Bale baleWithDifferentAmount = bales.get(0).toBuilder()
                .entryNo(1000)
                .amount(expectedAmount)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentAmount));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .amount(expectedAmount)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getAmount()).isEqualTo(expectedAmount);
    }

    @Test
    public void findAll_withRemainingAmount_returnOnlyBalesWithRemainingAmount() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        BigDecimal expectedRemainingAmount = new BigDecimal("1000").setScale(4, BigDecimal.ROUND_HALF_UP);;

        Bale baleWithDifferentRemainingAmount = bales.get(0).toBuilder()
                .entryNo(1000)
                .remainingAmount(expectedRemainingAmount)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentRemainingAmount));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .remainingAmount(expectedRemainingAmount)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getRemainingAmount()).isEqualTo(expectedRemainingAmount);
    }

    @Test
    public void findAll_withTransactionCurrency_returnOnlyBalesWithTransactionCurrency() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        Currency expectedTransactionCurrency = insertCurrency(FakeCurrencies.USD);

        Bale baleWithDifferentTransactionCurrency = bales.get(0).toBuilder()
                .entryNo(1000)
                .transactionCurrency(expectedTransactionCurrency)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentTransactionCurrency));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .transactionCurrency(List.of(expectedTransactionCurrency.getCode()))
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getTransactionCurrency().getCode()).isEqualTo(expectedTransactionCurrency.getCode());
    }

    @Test
    public void findAll_withAmountLcy_returnOnlyBalesWithAmountLcy() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        BigDecimal expectedAmountLcy = new BigDecimal("1000").setScale(4, BigDecimal.ROUND_HALF_UP);;

        Bale baleWithDifferentAmountLcy = bales.get(0).toBuilder()
                .entryNo(1000)
                .amountLcy(expectedAmountLcy)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentAmountLcy));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .amountLcy(expectedAmountLcy)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getAmountLcy()).isEqualTo(expectedAmountLcy);
    }

    @Test
    public void findAll_withBalanceAccountType_returnOnlyBalesWithBalanceAccountType() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedBalanceAccountType = "newAccType";

        Bale baleWithDifferentBalanceAccountType = bales.get(0).toBuilder()
                .entryNo(1000)
                .balanceAccountType(expectedBalanceAccountType)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentBalanceAccountType));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .balanceAccountType(expectedBalanceAccountType)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getBalanceAccountType()).isEqualTo(expectedBalanceAccountType);

        //testing with exactFilters
        String exactBalanceAccountType = "newAcc";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .balanceAccountType(exactBalanceAccountType)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .balanceAccountType(expectedBalanceAccountType)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
        assertThat(baleList3.get(0).getBalanceAccountType()).isEqualTo(expectedBalanceAccountType);
    }

    @Test
    public void findAll_withIsOpen_returnOnlyBalesWithIsOpen() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        boolean expectedIsOpen = false;

        Bale baleWithDifferentIsOpen = bales.get(0).toBuilder()
                .entryNo(1000)
                .isOpen(expectedIsOpen)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentIsOpen));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .isOpen(expectedIsOpen)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getIsOpen()).isEqualTo(expectedIsOpen);
    }

    @Test
    public void findAll_withIsReversed_returnOnlyBalesWithIsReversed() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        boolean expectedIsReversed = false;

        Bale baleWithDifferentIsReversed = bales.get(0).toBuilder()
                .entryNo(1000)
                .isReversed(expectedIsReversed)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentIsReversed));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .isReversed(expectedIsReversed)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getIsReversed()).isEqualTo(expectedIsReversed);
    }

    @Test
    public void findAll_withPostedBy_returnOnlyBalesWithPostedBy() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedPostedBy = "newPostedBy";

        Bale baleWithDifferentPostedBy = bales.get(0).toBuilder()
                .entryNo(1000)
                .postedBy(expectedPostedBy)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentPostedBy));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .postedBy(expectedPostedBy)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getPostedBy()).isEqualTo(expectedPostedBy);

        //testing with exactFilters
        String exactPostedBy = "newPost";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .postedBy(exactPostedBy)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .postedBy(expectedPostedBy)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
        assertThat(baleList3.get(0).getPostedBy()).isEqualTo(expectedPostedBy);
    }

    @Test
    public void findAll_withExternalDocumentNo_returnOnlyBalesWithExternalDocumentNo() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedExternalDocumentNo = "newExternalDocumentNo";

        Bale baleWithDifferentExternalDocumentNo = bales.get(0).toBuilder()
                .entryNo(1000)
                .externalDocumentNo(expectedExternalDocumentNo)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentExternalDocumentNo));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .externalDocumentNo(expectedExternalDocumentNo)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getExternalDocumentNo()).isEqualTo(expectedExternalDocumentNo);

        //testing with exactFilters
        String exactExternalDocumentNo = "newExternalDocument";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .externalDocumentNo(exactExternalDocumentNo)
                .exactFilters(true)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList2).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .externalDocumentNo(expectedExternalDocumentNo)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList3).hasSize(1);
        assertThat(baleList3.get(0).getExternalDocumentNo()).isEqualTo(expectedExternalDocumentNo);
    }

    @Test
    public void findAll_withBaleTimestamp_returnOnlyBalesWithBaleTimestamp() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedBaleTimestamp = "2021-01-01";

        Bale baleWithDifferentBaleTimestamp = bales.get(0).toBuilder()
                .entryNo(1000)
                .baleTimestamp(expectedBaleTimestamp)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentBaleTimestamp));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .baleTimestamp(expectedBaleTimestamp)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getBaleTimestamp()).isEqualTo(expectedBaleTimestamp);
    }

    @Test
    public void findAll_withBankAccountTimestamp_returnOnlyBalesWithBankAccountTimestamp() throws EntityErrorsException, DatabaseErrorsException, ParseException {

        String expectedBankAccountTimestamp = "2021-01-01";

        Bale baleWithDifferentBankAccountTimestamp = bales.get(0).toBuilder()
                .entryNo(1000)
                .accountTimestamp(expectedBankAccountTimestamp)
                .build();

        List<Bale> balesToInsert = new ArrayList<>(List.of(baleWithDifferentBankAccountTimestamp));
        balesToInsert.addAll(bales.subList(0, 5));
        insertBales(balesToInsert);

        BaleFilters baleFilters = BaleFilters.builder()
                .accountTimestamp(expectedBankAccountTimestamp)
                .build();

        List<Bale> baleList = bradBaleRepository.findAll(baleFilters, null, null);

        assertThat(baleList).hasSize(1);
        assertThat(baleList.get(0).getAccountTimestamp()).isEqualTo(expectedBankAccountTimestamp);
    }

    @Test
    public void findAll_withReconciliationId_returnOnlyBalesWithReconciliationId() throws DatabaseErrorsException, EntityErrorsException, ParseException {

        AccountStatement aAccountStatement;

        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(account)
                    .idCompany(companyID)
                    .build());
        });


        aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(account).currency(currency).build());
        List<Transaction> transactionList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }

        List<Bale> baleList = insertBales(tempBales);

        List<Reconciliation> reconciliations = new ArrayList<>();

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                    .id(i)
                    .account(account)
                    .idCompany(companyID)
                    .baleIds(List.of(baleList.get(i).getId()))
                    .transactionIds(List.of(transactionList.get(i).getId()))
                    .build());
        }

        List<Reconciliation> insertedReconciliations = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            insertedReconciliations.add(insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount()));
        }

        BaleFilters baleFilters = BaleFilters.builder()
                .reconciliationId(insertedReconciliations.get(0).getId())
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters, null, null);
        assertThat(baleList2).hasSize(1);
    }

    @Test
    public void findAll_withReconciliationCreator_returnOnlyBalesWithReconciliationCreator() throws DatabaseErrorsException, EntityErrorsException, ParseException {

        String expectedReconciliationCreator = "newReconciliationCreator";

        AccountStatement aAccountStatement;

        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(account)
                    .idCompany(companyID)
                    .build());
        });


        aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(account).currency(currency).build());
        List<Transaction> transactionList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }

        List<Bale> baleList = insertBales(tempBales);

        List<Reconciliation> reconciliations = new ArrayList<>();

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                    .id(i)
                    .account(account)
                    .idCompany(companyID)
                    .baleIds(List.of(baleList.get(i).getId()))
                    .transactionIds(List.of(transactionList.get(i).getId()))
                    .build());
        }

        insertReconciliations(
                reconciliations.get(0).toBuilder()
                        .creator(expectedReconciliationCreator)
                        .build(),
                transactionList.get(0).getAmount(),
                baleList.get(0).getAmount(),
                BigDecimal.TEN,
                reconciliations.get(0).getAccount());
        for (int i = 1; i < 3; i++) {
           insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount());
        }

        BaleFilters baleFilters = BaleFilters.builder()
                .reconciliationCreator(expectedReconciliationCreator)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters, null, null);
        assertThat(baleList2).hasSize(1);

        //exactFilters
        String exactReconciliationCreator = "newReconciliation";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .reconciliationCreator(exactReconciliationCreator)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList3).hasSize(0);

        BaleFilters baleFilters4 = BaleFilters.builder()
                .reconciliationCreator(expectedReconciliationCreator)
                .exactFilters(true)
                .build();

        List<Bale> baleList4 = bradBaleRepository.findAll(baleFilters4, null, null);
        assertThat(baleList4).hasSize(1);
    }

    @Test
    public void findAll_withReconciliationCreationDate_returnOnlyBalesWithReconciliationCreationDate() throws DatabaseErrorsException, EntityErrorsException, ParseException {

        LocalDateTime expectedReconciliationCreationDate = LocalDateTime.now().minusDays(2);

        AccountStatement aAccountStatement;

        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(account)
                    .idCompany(companyID)
                    .build());
        });


        aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(account).currency(currency).build());
        List<Transaction> transactionList = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .id((long) i)
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }

        List<Bale> baleList = insertBales(tempBales);

        List<Reconciliation> reconciliations = new ArrayList<>();

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                    .id(i + 1)
                    .account(account)
                    .idCompany(companyID)
                    .baleIds(List.of(baleList.get(i).getId()))
                    .transactionIds(List.of(transactionList.get(i).getId()))
                    .build());
        }

        insertReconciliations(
                reconciliations.get(0).toBuilder()
                        .creationDate(expectedReconciliationCreationDate)
                        .build(),
                transactionList.get(0).getAmount(),
                baleList.get(0).getAmount(),
                BigDecimal.TEN,
                reconciliations.get(0).getAccount());
        for (int i = 1; i < 3; i++) {
            insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount());
        }

        BaleFilters baleFilters = BaleFilters.builder()
                .reconciliationCreationDateStart(expectedReconciliationCreationDate.toLocalDate().minusDays(1))
                .reconciliationCreationDateEnd(expectedReconciliationCreationDate.toLocalDate().plusDays(1))
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters, null, null);
        assertThat(baleList2).hasSize(1);
    }

    @Test
    public void findAll_withReconciliationReviewer_returnOnlyBalesWithReconciliationReviewer() throws DatabaseErrorsException, EntityErrorsException, ParseException {

        String expectedReconciliationReviewer = "newReconciliationReviewer";

        AccountStatement aAccountStatement;
        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(account)
                    .idCompany(companyID)
                    .build());
        });


        aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(account).currency(currency).build());
        List<Transaction> transactionList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }

        List<Bale> baleList = insertBales(tempBales);

        List<Reconciliation> reconciliations = new ArrayList<>();

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                    .id(i)
                    .account(account)
                    .idCompany(companyID)
                    .baleIds(List.of(baleList.get(i).getId()))
                    .transactionIds(List.of(transactionList.get(i).getId()))
                    .build());
        }

        Reconciliation insertedReconciliation = insertReconciliations(
                reconciliations.get(0),
                transactionList.get(0).getAmount(),
                baleList.get(0).getAmount(),
                BigDecimal.TEN,
                reconciliations.get(0).getAccount());
        for (int i = 1; i < 3; i++) {
            insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount());
        }

        reconciliationRepository.approve(insertedReconciliation.toBuilder()
                        .reviewer(expectedReconciliationReviewer)
                        .build());

        BaleFilters baleFilters = BaleFilters.builder()
                .reconciliationReviewer(expectedReconciliationReviewer)
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters, null, null);
        assertThat(baleList2).hasSize(1);

        //exactFilters
        String exactReconciliationReviewer = "newReconciliation";

        BaleFilters baleFilters2 = BaleFilters.builder()
                .reconciliationReviewer(exactReconciliationReviewer)
                .exactFilters(true)
                .build();

        List<Bale> baleList3 = bradBaleRepository.findAll(baleFilters2, null, null);
        assertThat(baleList3).hasSize(0);

        BaleFilters baleFilters3 = BaleFilters.builder()
                .reconciliationReviewer(expectedReconciliationReviewer)
                .exactFilters(true)
                .build();

        List<Bale> baleList4 = bradBaleRepository.findAll(baleFilters3, null, null);
        assertThat(baleList4).hasSize(1);
    }

    @Test
    public void findAll_withReconciliationReviewDate_returnOnlyBalesWithReviewDate() throws DatabaseErrorsException, EntityErrorsException, ParseException {

        LocalDateTime creationDate = LocalDateTime.now();
        AccountStatement aAccountStatement;
        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(account)
                    .idCompany(companyID)
                    .build());
        });


        aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(account).currency(currency).build());
        List<Transaction> transactionList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }

        List<Bale> baleList = insertBales(tempBales);

        List<Reconciliation> reconciliations = new ArrayList<>();

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                    .id(i)
                    .account(account)
                    .idCompany(companyID)
                    .status(ReconciliationStatus.PENDING)
                    .baleIds(List.of(baleList.get(i).getId()))
                    .transactionIds(List.of(transactionList.get(i).getId()))
                    .build());
        }

        Reconciliation insertedReconciliation;

        insertedReconciliation = insertReconciliations(
                reconciliations.get(0).toBuilder()
                        .creationDate(creationDate)
                        .build(),
                transactionList.get(0).getAmount(),
                baleList.get(0).getAmount(),
                BigDecimal.TEN,
                reconciliations.get(0).getAccount());
        for (int i = 1; i < 3; i++) {
            insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount());
        }

        reconciliationRepository.approve(insertedReconciliation.toBuilder()
                .reviewer("reviewer")
                .reviewDate(creationDate)
                .build());


        BaleFilters baleFilters = BaleFilters.builder()
                .reconciliationReviewDateStart(LocalDate.now().minusDays(1))
                .reconciliationReviewDateEnd(LocalDate.now().plusDays(1))
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters, null, null);
        assertThat(baleList2).hasSize(1);

    }

    @Test
    public void findAll_withReconciliationStatus_returnOnlyBalesWithReconciliationStatus() throws DatabaseErrorsException, EntityErrorsException, ParseException {
        AccountStatement aAccountStatement;
        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(account)
                    .idCompany(companyID)
                    .build());
        });


        aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(account).currency(currency).build());
        List<Transaction> transactionList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }

        List<Bale> baleList = insertBales(tempBales);

        List<Reconciliation> reconciliations = new ArrayList<>();

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                    .id(i)
                    .account(account)
                    .idCompany(companyID)
                    .status(ReconciliationStatus.PENDING)
                    .baleIds(List.of(baleList.get(i).getId()))
                    .transactionIds(List.of(transactionList.get(i).getId()))
                    .build());
        }

        insertReconciliations(
                reconciliations.get(0).toBuilder()
                        .status(ReconciliationStatus.REJECTED)
                        .build(),
                transactionList.get(0).getAmount(),
                baleList.get(0).getAmount(),
                BigDecimal.TEN,
                reconciliations.get(0).getAccount());
        for (int i = 1; i < 3; i++) {
            insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount());
        }

        BaleFilters baleFilters = BaleFilters.builder()
                .reconciliationStatus(List.of(ReconciliationStatus.REJECTED.name()))
                .build();

        List<Bale> baleList2 = bradBaleRepository.findAll(baleFilters, null, null);
        assertThat(baleList2).hasSize(1);
        assertThat(baleList2.get(0).getId()).isEqualTo(baleList.get(0).getId());
    }


    @Test
    public void testFindLastBaleInBradOfCompanyId() throws DatabaseErrorsException {
        List<Bale> baleToInsert = insertBales(bales.subList(0, 5));

        Bale lastBale = baleToInsert.get(baleToInsert.size() - 1);


        Optional<Bale> lastBaleInBrad = bradBaleRepository.findLastBaleInBradOfCompanyId(lastBale.getIdCompany());

        assertThat(lastBaleInBrad).isPresent();
        assertThat(lastBaleInBrad.get().getEntryNo()).isEqualTo(lastBale.getEntryNo());

    }

    @SneakyThrows
    @Test
    public void testGetTotalAmountBales(){
        List<Bale> baleToInsert = insertBales(bales.subList(0, 5));

        BigDecimal totalAmount = BigDecimal.ZERO;
        for (Bale bale : baleToInsert) {
            totalAmount = totalAmount.add(bale.getAmount());
        }

        List<Long> baleIds = new ArrayList<>();
        for (Bale bale : baleToInsert) {
            baleIds.add(bale.getId());
        }

        BigDecimal calculatedAmount = bradBaleRepository.getTotalAmountOfBales(
                baleToInsert.get(0).getAccount().getId(), baleIds
        );

        assertThat(calculatedAmount).isEqualTo(totalAmount.setScale(4, RoundingMode.CEILING));
    }

    @SneakyThrows
    @Test
    public void testCount(){
        insertBales(bales.subList(0, 5));

        BaleFilters baleFilters = BaleFilters.builder()
                .build();

        assertThat(bradBaleRepository.count(baleFilters)).isEqualTo(5);
    }
}
