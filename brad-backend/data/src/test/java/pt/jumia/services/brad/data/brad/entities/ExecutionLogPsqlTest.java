package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.fake.FakeExecutionLogs;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ExecutionLogPsqlTest {

    private static final ExecutionLog executionLog1 = FakeExecutionLogs.getFakeExecutionLogs(1).get(0);
    private static ExecutionLog executionLog2;

    @BeforeAll
    public static void setUp() {
        executionLog2 = new ExecutionLogPsql(executionLog1).toEntity();
    }

    @Test
    public void testToEntityGetId() {
        assertEquals(executionLog1.getId(), executionLog2.getId());
    }

    @Test
    public void testToEntityGetRecordsAmount() {
        assertEquals(executionLog1.getRecordsAmount(), executionLog2.getRecordsAmount());
    }

    @Test
    public void testToEntityGetExecutionStartTime() {
        assertEquals(executionLog1.getExecutionStartTime(), executionLog2.getExecutionStartTime());
    }

    @Test
    public void testToEntityGetExecutionEndTime() {
        assertEquals(executionLog1.getExecutionEndTime(), executionLog2.getExecutionEndTime());
    }

    @Test
    public void testToEntityGetAppliedFilters() {
        assertEquals(executionLog1.getAppliedFilters(), executionLog2.getAppliedFilters());
    }

    @Test
    public void testEntityFields() {
        Map<ExecutionLog.SortingFields, String> expectedFields = Map.ofEntries(
                Map.entry(ExecutionLog.SortingFields.ID, "id"),
                Map.entry(ExecutionLog.SortingFields.LOG_TYPE, "logType"),
                Map.entry(ExecutionLog.SortingFields.LOG_STATUS, "logStatus"),
                Map.entry(ExecutionLog.SortingFields.RECORDS_AMOUNT, "recordsAmount"),
                Map.entry(ExecutionLog.SortingFields.EXECUTION_START_TIME, "executionStartTime"),
                Map.entry(ExecutionLog.SortingFields.EXECUTION_END_TIME, "executionEndTime")
        );

        assertEquals(expectedFields, ExecutionLogPsql.getEntityFields(ExecutionLog.SortingFields.class));
    }


    
}
