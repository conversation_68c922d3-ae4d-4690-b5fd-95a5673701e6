package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class BaleViewEntityPsqlTest {

    private static final ViewEntity baleViewEntity1 = FakeViewEntity.getFakeViewEntity(1).get(0);
    private static ViewEntity baleViewEntity2;

    @BeforeAll
    public static void setUp() {
        baleViewEntity2 = new ViewEntityPsql(baleViewEntity1).toEntity();
    }

    @Test
    public void testToEntityGetId() {
        assertEquals(baleViewEntity1.getId(), baleViewEntity2.getId());
    }

    @Test
    public void testToEntityGetDatabaseName() {
        assertEquals(baleViewEntity1.getDatabaseName(), baleViewEntity2.getDatabaseName());
    }

    @Test
    public void testToEntityGetViewName() {
        assertEquals(baleViewEntity1.getViewName(), baleViewEntity2.getViewName());
    }

    @Test
    public void testToEntityGetCreatedAt() {
        assertEquals(baleViewEntity1.getCreatedAt(), baleViewEntity2.getCreatedAt());
    }

    @Test
    public void testToEntityGetCreatedBy() {
        assertEquals(baleViewEntity1.getCreatedBy(), baleViewEntity2.getCreatedBy());
    }

    @Test
    public void testToEntityGetUpdatedAt() {
        assertEquals(baleViewEntity1.getUpdatedAt(), baleViewEntity2.getUpdatedAt());
    }

    @Test
    public void testToEntityGetUpdatedBy() {
        assertEquals(baleViewEntity1.getUpdatedBy(), baleViewEntity2.getUpdatedBy());
    }
}
