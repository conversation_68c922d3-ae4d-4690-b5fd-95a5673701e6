package pt.jumia.services.brad.data.brad.entities;


import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.AuditedEntity;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class RevisionPsqlTest {

    @Test
    public void testToEntity() {

        RevisionPsql revisionPsql = new RevisionPsql();
        revisionPsql.setEmail("<EMAIL>");
        LocalDateTime time = LocalDateTime.now();
        revisionPsql.setTime(time);

        AuditedEntity.RevisionInfo revisionInfo = revisionPsql.toEntity();

        assertEquals(time, revisionInfo.getDatetime());
        assertEquals("<EMAIL>", revisionInfo.getEmail());
    }

}
