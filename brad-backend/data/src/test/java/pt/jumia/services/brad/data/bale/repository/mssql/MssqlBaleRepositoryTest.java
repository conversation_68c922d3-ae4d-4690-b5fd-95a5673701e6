package pt.jumia.services.brad.data.bale.repository.mssql;

import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.data.brad.repository.psql.BaseRepositoryTest;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import static org.junit.jupiter.api.Assertions.assertThrows;

public class MssqlBaleRepositoryTest extends BaseRepositoryTest {

    @Test
    public void testFindAll_ThrowsEntityErrorsException_whenBaleViewEntityIsNull() {
        assertThrows(EntityErrorsException.class, () -> baleRepository.findAll(null, null, false, ExecutionLog.builder().build()));
    }
}
