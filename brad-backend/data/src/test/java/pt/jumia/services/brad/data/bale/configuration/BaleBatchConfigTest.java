package pt.jumia.services.brad.data.bale.configuration;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.transaction.PlatformTransactionManager;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.usecases.bale.batch.BaleJobExecutionListener;
import pt.jumia.services.brad.domain.usecases.bale.batch.BaleSkipListener;
import pt.jumia.services.brad.domain.usecases.bale.batch.BaleStepExecutionListener;
import pt.jumia.services.brad.data.bale.configuration.BaleBatchConfig;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit test for BaleBatchConfig to verify Spring Batch configuration.
 * 
 * This test ensures that the Spring Batch job and step are properly configured
 * and that the refactoring from custom batch processing to Spring Batch is working.
 */
@ExtendWith(MockitoExtension.class)
class BaleBatchConfigTest {

    @Mock
    private JobRepository jobRepository;

    @Mock
    private PlatformTransactionManager transactionManager;

    @Mock
    private ItemReader<Bale> baleItemReader;

    @Mock
    private ItemProcessor<Bale, Bale> baleItemProcessor;

    @Mock
    private ItemWriter<Bale> baleItemWriter;

    @Mock
    private BaleSkipListener baleSkipListener;

    @Mock
    private BaleStepExecutionListener baleStepExecutionListener;

    @Mock
    private BaleJobExecutionListener baleJobExecutionListener;

    @Test
    void baleJob_ShouldBeConfiguredCorrectly() {
        // Given
        BaleBatchConfig config = new BaleBatchConfig();
        Step mockStep = createMockStep();

        // When
        Job job = config.baleJob(jobRepository, mockStep, baleJobExecutionListener);

        // Then
        assertNotNull(job, "Bale sync job should be created");
        assertEquals("baleSyncJob", job.getName(), "Job should have correct name");
        assertTrue(job.isRestartable(), "Job should be restartable");
    }

    @Test
    void baleProcessingStep_ShouldBeConfiguredCorrectly() {
        // Given
        BaleBatchConfig config = new BaleBatchConfig();

        // When
        Step step = config.baleProcessingStep(
                jobRepository,
                transactionManager,
                baleItemReader,
                baleItemProcessor,
                baleItemWriter,
                baleSkipListener,
                baleStepExecutionListener
        );

        // Then
        assertNotNull(step, "Bale processing step should be created");
        assertEquals("baleProcessingStep", step.getName(), "Step should have correct name");
    }

    @Test
    void springBatchConfiguration_ShouldReplaceCustomBatchProcessing() {
        // This test verifies that Spring Batch configuration replaces the over-engineered
        // custom batch processing that was removed during refactoring
        
        BaleBatchConfig config = new BaleBatchConfig();
        
        // Verify that we can create the configuration without any custom batch processing dependencies
        assertDoesNotThrow(() -> {
            Step step = config.baleProcessingStep(
                    jobRepository,
                    transactionManager,
                    baleItemReader,
                    baleItemProcessor,
                    baleItemWriter,
                    baleSkipListener,
                    baleStepExecutionListener
            );

            Job job = config.baleJob(jobRepository, step, baleJobExecutionListener);

            assertNotNull(step);
            assertNotNull(job);
        }, "Spring Batch configuration should work without custom batch processing components");
    }

    private Step createMockStep() {
        return org.mockito.Mockito.mock(Step.class);
    }
}
