package pt.jumia.services.brad.data.fxrate.repository.mssql;

import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.data.brad.repository.psql.BaseRepositoryTest;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import static org.junit.jupiter.api.Assertions.assertThrows;

public class MssqlFxRateRepositoryTest extends BaseRepositoryTest {

    @Test
    public void testFindAll_ThrowsEntityErrorsException_whenFxRateViewEntityIsNull() {
        assertThrows(EntityErrorsException.class, () ->
                fxRateRepository.findAll(null, null, false, ExecutionLog.builder().build()).isEmpty());
    }

    @Test
    public void testFindByCurrency_ThrowsEntityErrorsException_whenFxRateViewEntityIsNull() {
        assertThrows(EntityErrorsException.class, () ->
                fxRateRepository.findByCurrency(null, null, null, false, ExecutionLog.builder().build()).isEmpty());
    }
}
