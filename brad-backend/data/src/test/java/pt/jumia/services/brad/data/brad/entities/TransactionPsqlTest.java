package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.fake.FakeTransaction;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class TransactionPsqlTest {

    private static final Account ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0);
    private static final AccountStatement ACCOUNT_STATEMENT = FakeAccountStatements.getFakeAccountStatements(1, ACCOUNT).get(0);
    private static final Transaction transaction1 = FakeTransaction.getFakeCreditTransactions(1, ACCOUNT_STATEMENT).get(0);
    private static Transaction transaction2;

    @BeforeAll
    public static void setUp() {
        transaction2 = new TransactionPsql(transaction1, "username").toEntity();
    }

    @Test
    public void testToEntityGetId() {
        assertEquals(transaction2.getId(), transaction1.getId());
    }

    @Test
    public void testToEntityGetType() {
        assertEquals(transaction2.getType(), transaction1.getType());
    }

    @Test
    public void testToEntityGetCurrency() {
        assertEquals(transaction2.getCurrency(), transaction1.getCurrency());
    }

    @Test
    public void testToEntityGetValueDate() {
        assertEquals(transaction2.getValueDate(), transaction1.getValueDate());
    }

    @Test
    public void testToEntityGetTransactionDate() {
        assertEquals(transaction2.getTransactionDate(), transaction1.getTransactionDate());
    }

    @Test
    public void testToEntityGetStatementDate() {
        assertEquals(transaction2.getStatementDate(), transaction1.getStatementDate());
    }

    @Test
    public void testToEntityGetDirection() {
        assertEquals(transaction2.getDirection(), transaction1.getDirection());
    }

    @Test
    public void testToEntityGetAmount() {
        assertEquals(transaction2.getAmount(), transaction1.getAmount());
    }

    @Test
    public void testToEntityGetReference() {
        assertEquals(transaction2.getReference(), transaction1.getReference());
    }

    @Test
    public void testToEntityGetDescription() {
        assertEquals(transaction2.getDescription(), transaction1.getDescription());
    }

    @Test
    public void testToEntityGetAccountStatement() {
        assertEquals(transaction2.getAccountStatement(), transaction1.getAccountStatement());
    }

    @Test
    public void testToEntityGetRemittanceInformation() {
        assertEquals(transaction2.getRemittanceInformation(), transaction1.getRemittanceInformation());
    }

    @Test
    public void testToEntityGetOrderingPartyName() {
        assertEquals(transaction2.getOrderingPartyName(), transaction1.getOrderingPartyName());
    }

    @Test
    public void testToEntityGetCreatedAt() {
        assertEquals(transaction2.getCreatedAt(), transaction1.getCreatedAt());
    }

    @Test
    public void testToEntityGetCreatedBy() {
        assertEquals(transaction2.getCreatedBy(), transaction1.getCreatedBy());
    }

    @Test
    public void testToEntityGetUpdatedAt() {
        assertEquals(transaction2.getUpdatedAt(), transaction1.getUpdatedAt());
    }

    @Test
    public void testToEntityGetUpdatedBy() {
        assertEquals(transaction2.getUpdatedBy(), transaction1.getUpdatedBy());
    }

}
