package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.ApiLog;
import pt.jumia.services.brad.domain.entities.fake.FakeApiLogs;
import pt.jumia.services.brad.domain.entities.filter.apilog.ApiLogFilters;
import pt.jumia.services.brad.domain.entities.filter.apilog.ApiLogSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlApiLogRepositoryTest extends BaseRepositoryTest {


    static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");


    private static final ApiLog AN_API_LOG = FakeApiLogs.getFakeApiLogs(21).get(20)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();
    private static final ApiLog ANOTHER_API_LOG = FakeApiLogs.getFakeApiLogs(22).get(21)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();


    @SneakyThrows
    @Test
    public void upsertAndFindTest() {

        insertApiLog(AN_API_LOG);
        insertApiLog(ANOTHER_API_LOG);

        ApiLogSortFilters apiLogSortFilters = ApiLogSortFilters.builder()
                .direction(OrderDirection.ASC)
                .build();
        List<ApiLog> apiLogs = apiLogRepository.findAll(null,
                apiLogSortFilters, null).stream().map(ApiLog::withoutDbFields).toList();

        assertEquals(2, apiLogs.size());

    }

    @SneakyThrows
    @Test
    public void findAll_withStatusFilter_returnOnlyApiLogsWithStatus() {
        List<ApiLog> apiLogsToTest = FakeApiLogs.getFakeApiLogs(3);


        insertApiLog(apiLogsToTest.get(0).toBuilder()
                .logStatus(ApiLog.ApiLogStatus.FAILURE)
                .build());

        ApiLog secondApiLog = insertApiLog(apiLogsToTest.get(1).toBuilder()
                .logStatus(ApiLog.ApiLogStatus.SUCCESS)
                .build());

        insertApiLog(apiLogsToTest.get(2).toBuilder()
                .logStatus(ApiLog.ApiLogStatus.FAILURE)
                .build());

        ApiLogFilters apiLogFilters = ApiLogFilters.builder()
                .logStatus(List.of(secondApiLog.getLogStatus().name()))
                .build();

        List<ApiLog> apiLogs = apiLogRepository.findAll(apiLogFilters,
                null, null).stream().map(ApiLog::withoutDbFields).toList();

        assertEquals(1, apiLogs.size());
        assertEquals(secondApiLog.getLogStatus(), apiLogs.get(0).getLogStatus());

    }

    @SneakyThrows
    @Test
    public void findAll_withLogTypesFilter_returnOnlyApiLogsWithLogType() {
        List<ApiLog> apiLogsToTest = FakeApiLogs.getFakeApiLogs(3);


        insertApiLog(apiLogsToTest.get(0).toBuilder()
                .logType(ApiLog.ApiLogType.BANK_STATEMENT_CREATION)
                .build());

        ApiLog secondApiLog = insertApiLog(apiLogsToTest.get(1).toBuilder()
                .logType(ApiLog.ApiLogType.BANK_STATEMENT_CREATION)
                .build());

        insertApiLog(apiLogsToTest.get(2).toBuilder()
                .logType(ApiLog.ApiLogType.BANK_STATEMENT_CREATION)
                .build());

        ApiLogFilters apiLogFilters = ApiLogFilters.builder()
                .logStatus(List.of(secondApiLog.getLogStatus().name()))
                .build();

        ApiLogFilters apiLogFilters2 = ApiLogFilters.builder()
                .logStatus(List.of("fakeFilter"))
                .build();

        List<ApiLog> apiLogs = apiLogRepository.findAll(apiLogFilters,
                null, null).stream().map(ApiLog::withoutDbFields).toList();

        List<ApiLog> apiLogs2 = apiLogRepository.findAll(apiLogFilters2,
                null, null).stream().map(ApiLog::withoutDbFields).toList();

        assertEquals(3, apiLogs.size());
        assertEquals(0, apiLogs2.size());
        assertEquals(secondApiLog.getLogType(), apiLogs.get(0).getLogType());

    }


    @SneakyThrows
    @Test
    public void findAll_withCreatedAtFilter_returnOnlyApiLogsWithCreatedAt() {
        List<ApiLog> apiLogsToTest = FakeApiLogs.getFakeApiLogs(3);

        insertApiLog(apiLogsToTest.get(0).toBuilder()
                .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
                .build());

        ApiLog secondApiLog = insertApiLog(apiLogsToTest.get(1).toBuilder()
                .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter).plusDays(1))
                .build());

        insertApiLog(apiLogsToTest.get(2).toBuilder()
                .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
                .build());

        ApiLogFilters apiLogFilters = ApiLogFilters.builder()
                .createdAtStart(LocalDateTime.parse("2023-04-20T00:00:00.000000", formatter))
                .build();

        List<ApiLog> apiLogs = apiLogRepository.findAll(apiLogFilters,
                null, null);

        assertEquals(1, apiLogs.size());
        assertEquals(secondApiLog.getCreatedAt(), apiLogs.get(0).getCreatedAt());

    }


    @Test
    public void findByIdTest() {
        ApiLog aApiLog = insertApiLog(AN_API_LOG);
        insertApiLog(ANOTHER_API_LOG);
        assert aApiLog.getId() != null;
        Optional<ApiLog> optionalApiLog = apiLogRepository.findById(aApiLog.getId());

        assert optionalApiLog.isPresent();
        assertEquals(aApiLog.getId(), optionalApiLog.get().getId());
    }


    @Test
    public void deleteTest() {
        ApiLog aApiLog = insertApiLog(AN_API_LOG);
        insertApiLog(ANOTHER_API_LOG);
        assertThat(apiLogRepository.findById(aApiLog.getId()).isEmpty()).isFalse();

        apiLogRepository.deleteById(aApiLog.getId());
        assertThat(apiLogRepository.findById(aApiLog.getId())).isEmpty();
    }


}
