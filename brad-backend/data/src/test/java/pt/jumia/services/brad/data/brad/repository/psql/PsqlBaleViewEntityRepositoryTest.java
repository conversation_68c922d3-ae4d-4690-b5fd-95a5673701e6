package pt.jumia.services.brad.data.brad.repository.psql;

import jakarta.transaction.Transactional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Transactional
public class PsqlBaleViewEntityRepositoryTest extends BaseRepositoryTest{

    private static final List<ViewEntity> baleViewEntities = new ArrayList<>();

    @BeforeEach
    public void setUp(){
        baleViewEntities.clear();
        baleViewEntities.addAll(FakeViewEntity.getFakeViewEntity(20));
    }

    @Test
    public void testFindAll() throws EntityErrorsException {
        List<ViewEntity> baleViews = new ArrayList<>();
        baleViewEntities.forEach(baleViewEntity -> {
            baleViews.add(insertBaleViewEntity(baleViewEntity.toBuilder().id(null).build()));
        });

        List<ViewEntity> foundBaleViews = baleViewEntityRepository.findAll(ViewEntity.EntityType.BALE);
        assertEquals(baleViews.size(), foundBaleViews.size());

    }

    @Test
    public void testFindById() {
        ViewEntity baleViewEntity = insertBaleViewEntity(baleViewEntities.get(0));
        Optional<ViewEntity> optionalBaleViewEntity = baleViewEntityRepository.findById(baleViewEntity.getId());
        assertTrue(optionalBaleViewEntity.isPresent());
        ViewEntity foundBaleView = optionalBaleViewEntity.get();
        assertEquals(baleViewEntity.getId(), foundBaleView.getId());
    }
}
