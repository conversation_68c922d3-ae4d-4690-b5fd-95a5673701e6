package pt.jumia.services.brad.data.brad.repository.psql;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.fake.*;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.reconciliation.ReconciliationFilters;
import pt.jumia.services.brad.domain.entities.filter.reconciliation.ReconciliationSortFilters;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.enumerations.ReconciliationStatus;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class PsqlReconciliationRepositoryTest extends BaseRepositoryTest{

    static DateTimeFormatter dateTimeformatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");
    static DateTimeFormatter dateformatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final Account AN_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .build();

    private static final AccountStatement AN_ACCOUNT_STATEMENT = FakeAccountStatements.getFakeAccountStatements(1, AN_ACCOUNT).get(0)
            .toBuilder()
            .initialDate(LocalDate.parse("2023-04-19", dateformatter))
            .finalDate(LocalDate.parse("2023-04-19", dateformatter))
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .build();

    private static final Transaction A_TRANSACTION = FakeTransaction.getFakeCreditTransactions(1, AN_ACCOUNT_STATEMENT).get(0)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .build();

    private static final Country A_COUNTRY = FakeCountries.NIGERIA;
    private static final Currency A_CURRENCY = FakeCurrencies.NGN;

    List<Reconciliation> reconciliations = new ArrayList<>();

    List<Transaction> transactionList = new ArrayList<>();
    List<Bale> baleList = new ArrayList<>();
    AccountStatement aAccountStatement;
    Account aAccount;

    @BeforeEach
    public void setUp() throws DatabaseErrorsException {

        String companyID = "RecTest";
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        aAccount = insertAccount(FakeAccounts.FAKE_ACCOUNT.toBuilder()
                .currency(currency)
                .companyID(companyID)
                .country(country)
                .build());

        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(aAccount)
                    .idCompany(companyID)
                    .build());
        });
        aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());

        tempBales.forEach(bale -> {
            try {
                bradBaleRepository.createPartition(companyID);
            } catch (DatabaseErrorsException e) {
                throw new RuntimeException(e);
            }
        });

        for (int i = 0; i < 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }

        this.baleList = insertBales(tempBales);

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                            .id(i)
                            .account(aAccount)
                            .idCompany(companyID)
                            .baleIds(List.of(baleList.get(i).getId()))
                            .transactionIds(List.of(transactionList.get(i).getId()))
                            .build());
        }


    }

    @Test
    public void testFindAll() throws DatabaseErrorsException, EntityErrorsException {
        List<Reconciliation> insertedReconciliations = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            insertedReconciliations.add(insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount()));
        }

        ReconciliationSortFilters reconciliationSortFilters = ReconciliationSortFilters.builder()
                .direction(OrderDirection.DESC)
                .field(Reconciliation.SortingFields.ID)
                .build();
        ReconciliationFilters reconciliationFilters = ReconciliationFilters.builder()
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Reconciliation> reconciliationList = reconciliationRepository.findAll(reconciliationFilters,
                reconciliationSortFilters, pageFilters);

        assertThat(reconciliationList).hasSize(insertedReconciliations.size());

    }

    @Test
    public void testFindAll2() throws EntityErrorsException {
        List<Reconciliation> insertedReconciliations = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            insertedReconciliations.add(insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount()));
        }

        ReconciliationSortFilters reconciliationSortFilters = ReconciliationSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(Reconciliation.SortingFields.ID)
                .build();
        ReconciliationFilters reconciliationFilters = ReconciliationFilters.builder()
                .id(insertedReconciliations.get(0).getId())
                .status(insertedReconciliations.get(0).getStatus())
                .creator(insertedReconciliations.get(0).getCreator())
                .reviewer(insertedReconciliations.get(0).getReviewer())
                .amountTransaction(insertedReconciliations.get(0).getAmountTransaction())
                .amountBale(insertedReconciliations.get(0).getAmountBale())
                .amountThreshold(insertedReconciliations.get(0).getAmountThreshold())
                .baleIdsList(insertedReconciliations.get(0).getBaleIds())
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Reconciliation> reconciliationList = reconciliationRepository.findAll(reconciliationFilters,
                reconciliationSortFilters, pageFilters);

        assertThat(reconciliationList.get(0).getId()).isEqualTo(insertedReconciliations.get(0).getId());

    }

    @Test
    public void testFindAll3() throws EntityErrorsException {
        List<Reconciliation> insertedReconciliations = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            insertedReconciliations.add(insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount()));
        }

        List<Reconciliation> reconciliationList = reconciliationRepository.findAll(null,
                null, null);

        assertThat(reconciliationList).hasSize(insertedReconciliations.size());
    }

    @Test
    public void testFindAll4() throws EntityErrorsException {
        List<Reconciliation> insertedReconciliations = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            insertedReconciliations.add(insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount()));
        }

        List<Reconciliation> reconciliationList = reconciliationRepository.findAll(null,
                ReconciliationSortFilters.builder().field(null).build(), null);

        assertThat(reconciliationList).hasSize(insertedReconciliations.size());
    }

    @Test
    public void testCount() {
        List<Reconciliation> insertedReconciliations = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            insertedReconciliations.add(insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount()));
        }

        ReconciliationFilters reconciliationFilters = ReconciliationFilters.builder()
                .build();

        assertThat(reconciliationRepository.count(reconciliationFilters)).isEqualTo(insertedReconciliations.size());
    }

    @Test
    public void testApprove() {
        List<Reconciliation> insertedReconciliations = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            insertedReconciliations.add(insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount()));
        }
        for (Reconciliation insertedReconciliation : insertedReconciliations) {
            assertThat(reconciliationRepository.approve(insertedReconciliation).getStatus()).isEqualTo(ReconciliationStatus.APPROVED);
        }
    }

    @Test
    public void testFindById() {
        List<Reconciliation> insertedReconciliations = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            insertedReconciliations.add(insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    baleList.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount()));
        }

        assertThat(reconciliationRepository.findById(insertedReconciliations.get(0).getId())
                .isPresent()).isTrue();

        assertThat(reconciliationRepository.findById(insertedReconciliations.get(0).getId())
                .get().getId()).isEqualTo(insertedReconciliations.get(0).getId());

    }
    @Test
    public void testFindById_nothingFound(){
        assertThat(reconciliationRepository.findById(0)
                .isPresent()).isFalse();

    }

}
