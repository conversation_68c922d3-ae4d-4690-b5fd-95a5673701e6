package pt.jumia.services.brad.data.fxrate.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.data.brad.entities.fxrates.FxRatePsql;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.fake.FakeFxRates;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class FxRateMssqlTest {
    private static final FxRate fxRate1 = FakeFxRates.FAKE_FX_RATE_EUR_NGN;
    private static FxRate fxRate2;
    @BeforeAll
    public static void setUp() {
        fxRate2 = new FxRatePsql(fxRate1).toEntity();
    }

    @Test
    public void testId() {
        assertEquals(fxRate1.getId(), fxRate2.getId());
    }

    @Test
    public void testBaseCurrency() {
        assertEquals(fxRate1.getBaseCurrency().getCode(), fxRate2.getBaseCurrency().getCode());
    }

    @Test
    public void testQuoteCurrency() {
        assertEquals(fxRate1.getQuoteCurrency().getCode(), fxRate2.getQuoteCurrency().getCode());
    }

    @Test
    public void testRateDate() {
        assertEquals(fxRate1.getRateDate(), fxRate2.getRateDate());
    }

    @Test
    public void testBid() {
        assertEquals(fxRate1.getBid(), fxRate2.getBid());
    }

    @Test
    public void testBisLoadedAt() {
        assertEquals(fxRate1.getBisLoadedAt(), fxRate2.getBisLoadedAt());
    }

    @Test
    public void testSkAudInsert() {
        assertEquals(fxRate1.getSkAudInsert(), fxRate2.getSkAudInsert());
    }

    @Test
    public void testSkAudUpdate() {
        assertEquals(fxRate1.getSkAudUpdate(), fxRate2.getSkAudUpdate());
    }

    @Test
    public void testTimestampLastUpdate() {
        assertEquals(fxRate1.getTimestampLastUpdate(), fxRate2.getTimestampLastUpdate());
    }

}
