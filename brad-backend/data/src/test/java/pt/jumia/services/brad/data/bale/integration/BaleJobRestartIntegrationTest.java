package pt.jumia.services.brad.data.bale.integration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.*;
import org.springframework.batch.item.ExecutionContext;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.bale.batch.BaleItemReader;
import pt.jumia.services.brad.domain.usecases.executionlogs.CreateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Integration tests for job restart scenarios verifying audit compliance.
 * 
 * Tests cover:
 * - Job restart from failure points using ExecutionContext
 * - State persistence and restoration across restarts
 * - Data consistency during restart operations
 * - No data loss or duplication during restarts
 */
@ExtendWith(MockitoExtension.class)
class BaleJobRestartIntegrationTest {

    @Mock
    private BaleRepository baleRepository;
    
    @Mock
    private BradBaleRepository bradBaleRepository;
    
    @Mock
    private ReadViewEntityUseCase readViewEntityUseCase;
    
    @Mock
    private CreateExecutionLogsUseCase createExecutionLogsUseCase;

    private BaleItemReader baleItemReader;
    private ExecutionContext executionContext;

    @BeforeEach
    void setUp() {
        baleItemReader = new BaleItemReader(baleRepository, bradBaleRepository, 
            readViewEntityUseCase, createExecutionLogsUseCase);
        executionContext = new ExecutionContext();
    }

    @Test
    void jobRestart_FromMiddleOfProcessing_ShouldResumeCorrectly() throws Exception {
        // Given - Simulate a job that failed in the middle
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0),
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        List<Bale> bales = FakeBale.getFakeBale(3);
        
        lenient().when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        lenient().when(bradBaleRepository.findLastBaleInBradOfCompanyId(anyString())).thenReturn(Optional.empty());
        lenient().when(baleRepository.fetchCompanyId(any(), anyBoolean())).thenReturn("TEST_COMPANY");
        lenient().when(baleRepository.findAllBatched(anyInt(), any(), anyBoolean(), any(), anyInt(), anyInt()))
            .thenReturn(bales);

        // Simulate restart state - job failed at view entity index 1, bale offset 150
        executionContext.putInt("viewEntityIndex", 1);
        executionContext.putInt("currentBaleOffset", 150);
        executionContext.putString("currentCompanyId", "TEST_COMPANY");
        executionContext.putInt("currentEntryNo", 12345);
        executionContext.putString("initialized", "true");

        // When - Restart the reader
        baleItemReader.open(executionContext);
        
        // Then - Should resume from correct position
        assertEquals(1, executionContext.getInt("viewEntityIndex", -1), 
            "Should resume from view entity index 1");
        assertEquals(150, executionContext.getInt("currentBaleOffset", -1), 
            "Should resume from bale offset 150");
        assertEquals("TEST_COMPANY", executionContext.getString("currentCompanyId", null), 
            "Should restore company ID");
        assertEquals(12345, executionContext.getInt("currentEntryNo", -1), 
            "Should restore entry number");
        
        verify(readViewEntityUseCase).execute(ViewEntity.EntityType.BALE);
    }

    @Test
    void jobRestart_WithFreshExecution_ShouldStartFromBeginning() throws Exception {
        // Given - Fresh execution (no previous state)
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);

        // When - Start fresh execution
        baleItemReader.open(executionContext);
        
        // Then - Should start from beginning
        assertEquals(0, executionContext.getInt("viewEntityIndex", -1), 
            "Should start from view entity index 0");
        assertEquals(0, executionContext.getInt("currentBaleOffset", -1), 
            "Should start from bale offset 0");
        assertEquals("true", executionContext.getString("initialized", null), 
            "Should be marked as initialized");
        
        verify(readViewEntityUseCase).execute(ViewEntity.EntityType.BALE);
    }

    @Test
    void jobRestart_WithStateUpdate_ShouldPersistProgressCorrectly() throws Exception {
        // Given
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        List<Bale> bales = FakeBale.getFakeBale(2);
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(bradBaleRepository.findLastBaleInBradOfCompanyId(anyString())).thenReturn(Optional.empty());
        when(baleRepository.fetchCompanyId(any(), anyBoolean())).thenReturn("TEST_COMPANY");
        when(baleRepository.findAllBatched(anyInt(), any(), anyBoolean(), any(), anyInt(), anyInt()))
            .thenReturn(bales);
        when(createExecutionLogsUseCase.execute(any())).thenReturn(null);

        // When - Open reader and read some items
        baleItemReader.open(executionContext);
        
        Bale firstBale = baleItemReader.read();
        baleItemReader.update(executionContext);
        
        Bale secondBale = baleItemReader.read();
        baleItemReader.update(executionContext);

        // Then - State should be updated correctly
        assertNotNull(firstBale, "First bale should be read");
        assertNotNull(secondBale, "Second bale should be read");
        assertEquals(2, executionContext.getInt("currentBaleOffset", -1), 
            "Bale offset should be updated to 2");
        
        // Additional verification for batch state
        assertTrue(executionContext.getInt("currentBatchIndex", -1) >= 0, 
            "Batch index should be persisted");
        assertTrue(executionContext.getInt("currentBatchSize", -1) > 0, 
            "Batch size should be persisted");
    }

    @Test
    void jobRestart_AcrossViewEntityBoundaries_ShouldHandleCorrectly() throws Exception {
        // Given - Two view entities, restart should handle transition correctly
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0),
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(bradBaleRepository.findLastBaleInBradOfCompanyId(anyString())).thenReturn(Optional.empty());
        when(baleRepository.fetchCompanyId(any(), anyBoolean())).thenReturn("TEST_COMPANY");
        when(baleRepository.findAllBatched(anyInt(), any(), anyBoolean(), any(), anyInt(), anyInt()))
            .thenReturn(Arrays.asList()); // Empty list to simulate moving to next view entity
        when(createExecutionLogsUseCase.execute(any())).thenReturn(null);

        // Simulate restart at boundary between view entities
        executionContext.putInt("viewEntityIndex", 0);
        executionContext.putInt("currentBaleOffset", 0);
        executionContext.putString("initialized", "true");

        // When
        baleItemReader.open(executionContext);
        Bale result = baleItemReader.read(); // Should trigger move to next view entity

        // Then
        assertNull(result, "Should return null when no more bales");
        assertTrue(executionContext.getInt("viewEntityIndex", -1) >= 0, 
            "View entity index should be managed correctly");
    }

    @Test
    void jobRestart_WithDataConsistencyCheck_ShouldValidateState() throws Exception {
        // Given
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);

        // Simulate corrupted restart state
        executionContext.putInt("viewEntityIndex", 999); // Invalid index
        executionContext.putInt("currentBaleOffset", -1); // Invalid offset
        executionContext.putString("initialized", "true");

        // When & Then - Should handle invalid state gracefully
        assertDoesNotThrow(() -> baleItemReader.open(executionContext), 
            "Should handle invalid restart state gracefully");
        
        // State should be reset to valid values
        assertTrue(executionContext.getInt("viewEntityIndex", -1) >= 0, 
            "View entity index should be valid");
        assertTrue(executionContext.getInt("currentBaleOffset", -1) >= 0, 
            "Bale offset should be valid");
    }

    @Test
    void jobRestart_WithProperStateManagement_ShouldMaintainConsistency() throws Exception {
        // Given
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        List<Bale> bales = FakeBale.getFakeBale(5);
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(bradBaleRepository.findLastBaleInBradOfCompanyId(anyString())).thenReturn(Optional.empty());
        when(baleRepository.fetchCompanyId(any(), anyBoolean())).thenReturn("TEST_COMPANY");
        when(baleRepository.findAllBatched(anyInt(), any(), anyBoolean(), any(), anyInt(), anyInt()))
            .thenReturn(bales);
        when(createExecutionLogsUseCase.execute(any())).thenReturn(null);

        // When - Open reader and process items
        baleItemReader.open(executionContext);
        
        // Read multiple items and update state
        for (int i = 0; i < 3; i++) {
            Bale bale = baleItemReader.read();
            assertNotNull(bale, "Should read bale " + i);
            baleItemReader.update(executionContext);
        }

        // Then - State should be consistent
        assertEquals(3, executionContext.getInt("currentBaleOffset", -1), 
            "Bale offset should reflect items read");
        assertEquals(3, executionContext.getInt("currentBatchIndex", -1), 
            "Batch index should reflect items read");
        assertEquals(5, executionContext.getInt("currentBatchSize", -1), 
            "Batch size should be persisted");
    }
}