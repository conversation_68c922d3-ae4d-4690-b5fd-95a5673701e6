package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class BalePsqlTest {

    private static final Bale bale1 = FakeBale.getFakeBale(1).get(0);
    private static Bale bale2;
    @BeforeAll
    public static void setUp() {
        bale2 = new BalePsql(bale1).toEntity();
    }

    @Test
    public void testToEntityGetIdCompany() {
        assertEquals(bale1.getIdCompany(), bale2.getIdCompany());
    }

    @Test
    public void testToEntityGetBankAccount() {
        assertEquals(bale1.getAccount(), bale2.getAccount());
    }

    @Test
    public void testToEntityGetEntryNo() {
        assertEquals(bale1.getEntryNo(), bale2.getEntryNo());
    }

    @Test
    public void testToEntityGetDocumentNo() {
        assertEquals(bale1.getDocumentNo(), bale2.getDocumentNo());
    }

    @Test
    public void testToEntityGetDocumentType() {
        assertEquals(bale1.getDocumentType(), bale2.getDocumentType());
    }

    @Test
    public void testToEntityGetPostingDate() {
        assertEquals(bale1.getPostingDate(), bale2.getPostingDate());
    }

    @Test
    public void testToEntityGetBankAccountPostingGroup() {
        assertEquals(bale1.getAccountPostingGroup(), bale2.getAccountPostingGroup());
    }

    @Test
    public void testToEntityGetDescription() {
        assertEquals(bale1.getDescription(), bale2.getDescription());
    }

    @Test
    public void testToEntityGetSourceCode() {
        assertEquals(bale1.getSourceCode(), bale2.getSourceCode());
    }

    @Test
    public void testToEntityGetReasonCode() {
        assertEquals(bale1.getReasonCode(), bale2.getReasonCode());
    }

    @Test
    public void testToEntityGetBusLine() {
        assertEquals(bale1.getBusLine(), bale2.getBusLine());
    }

    @Test
    public void testToEntityGetDepartment() {
        assertEquals(bale1.getDepartment(), bale2.getDepartment());
    }

    @Test
    public void testToEntityGetAmount() {
        assertEquals(bale1.getAmount(), bale2.getAmount());
    }

    @Test
    public void testToEntityGetRemainingAmount() {
        assertEquals(bale1.getRemainingAmount(), bale2.getRemainingAmount());
    }

    @Test
    public void testToEntityGetTransactionCurrency() {
        assertEquals(bale1.getTransactionCurrency(), bale2.getTransactionCurrency());
    }

    @Test
    public void testToEntityGetAmountLcy() {
        assertEquals(bale1.getAmountLcy(), bale2.getAmountLcy());
    }

    @Test
    public void testToEntityGetBalanceAccountType() {
        assertEquals(bale1.getBalanceAccountType(), bale2.getBalanceAccountType());
    }

    @Test
    public void testToEntityGetIsOpen() {
        assertEquals(bale1.getIsOpen(), bale2.getIsOpen());
    }

    @Test
    public void testToEntityGetIsReversed() {
        assertEquals(bale1.getIsReversed(), bale2.getIsReversed());
    }

    @Test
    public void testToEntityGetPostedBy() {
        assertEquals(bale1.getPostedBy(), bale2.getPostedBy());
    }

    @Test
    public void testToEntityGetExternalDocumentNo() {
        assertEquals(bale1.getExternalDocumentNo(), bale2.getExternalDocumentNo());
    }

    @Test
    public void testToEntityGetBaleTimestamp() {
        assertEquals(bale1.getBaleTimestamp(), bale2.getBaleTimestamp());
    }

    @Test
    public void testToEntityGetBankAccountTimestamp() {
        assertEquals(bale1.getAccountTimestamp(), bale2.getAccountTimestamp());
    }
}
