package pt.jumia.services.brad.data.brad.entities.reconciliation;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.fake.FakeReconciliation;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ReconciliationPsqlTest {
    private static final Reconciliation reconciliation1 = FakeReconciliation.FAKE_RECONCILIATION;
    private static Reconciliation reconciliation2;

    @BeforeAll
    public static void setUp() {
        reconciliation2 = new ReconciliationPsql(reconciliation1, null, null).toEntityWithList(
                reconciliation1.getBaleIds(),
                reconciliation1.getTransactionIds(),
                reconciliation1.getAccount()
        );
    }
    @Test
    public void testToEntityWithListGetId() {
        assertEquals(reconciliation1.getId(), reconciliation2.getId());
    }

    @Test
    public void testToEntityWithListGetStatus() {
        assertEquals(reconciliation1.getStatus(), reconciliation2.getStatus());
    }

    @Test
    public void testToEntityWithListGetAmountTransaction() {
        assertEquals(reconciliation1.getAmountTransaction(), reconciliation2.getAmountTransaction());
    }

    @Test
    public void testToEntityWithListGetAmountBale() {
        assertEquals(reconciliation1.getAmountBale(), reconciliation2.getAmountBale());
    }

    @Test
    public void testToEntityWithListGetAmountThreshold() {
        assertEquals(reconciliation1.getAmountThreshold(), reconciliation2.getAmountThreshold());
    }

    @Test
    public void testToEntityWithListGetCreator() {
        assertEquals(reconciliation1.getCreator(), reconciliation2.getCreator());
    }

    @Test
    public void testToEntityWithListGetCreationDate() {
        assertEquals(reconciliation1.getCreationDate(), reconciliation2.getCreationDate());
    }

    @Test
    public void testToEntityWithListGetReviewer() {
        assertEquals(reconciliation1.getReviewer(), reconciliation2.getReviewer());
    }

    @Test
    public void testToEntityWithListGetReviewDate() {
        assertEquals(reconciliation1.getReviewDate(), reconciliation2.getReviewDate());
    }

    @Test
    public void testToEntityWithListGetTransactionIdsList() {
        assertEquals(reconciliation1.getTransactionIds(), reconciliation2.getTransactionIds());
    }

    @Test
    public void testToEntityWithListGetBaleIdsList() {
        assertEquals(reconciliation1.getBaleIds(), reconciliation2.getBaleIds());
    }
}
