package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.entities.fake.FakeDocuments;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.document.DocumentFilters;
import pt.jumia.services.brad.domain.entities.filter.document.DocumentSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlDocumentRepositoryTest extends BaseRepositoryTest {

    static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");

    private static final Account AN_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();

    private static final Document A_DOCUMENT = FakeDocuments.getFakeDocuments(21).get(20)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();
    private static final Document ANOTHER_DOCUMENT = FakeDocuments.getFakeDocuments(22).get(21)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();

    private static final Currency A_CURRENCY = FakeCurrencies.NGN;

    @SneakyThrows
    @Test
    public void insertAndFindTest() {
        Currency aCurrency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(FakeCountries.NIGERIA.toBuilder().currency(aCurrency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(aCurrency).build());

        Document aDocument = insertDocument(A_DOCUMENT.toBuilder().account(aAccount).build());
        Document anotherDocument = insertDocument(ANOTHER_DOCUMENT.toBuilder().account(aAccount).build());

        DocumentSortFilters documentSortFilters = DocumentSortFilters.builder()
                .direction(OrderDirection.ASC)
                .build();
        List<Document> documents = documentRepository.findAll(null,
                documentSortFilters, null);

        List<Document> withoutAccount = documents.stream()
                .map(document -> document.toBuilder().account(null).build())
                .toList();

        assertThat(withoutAccount).containsExactlyInAnyOrder(aDocument.toBuilder().account(null).build(),
                anotherDocument.toBuilder().account(null).build());
    }

    @SneakyThrows
    @Test
    public void testFindAll(){
        Currency aCurrency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(FakeCountries.NIGERIA.toBuilder().currency(aCurrency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(aCurrency).build());
        Document aDocument = insertDocument(A_DOCUMENT.toBuilder().account(aAccount).build());
        insertDocument(ANOTHER_DOCUMENT.toBuilder().account(aAccount).build());

        DocumentSortFilters documentSortFilters = DocumentSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(Document.SortingFields.ID)
                .build();
        DocumentFilters documentFilters = DocumentFilters.builder()
                .types(List.of(aDocument.getDocumentType().toString()))
                .name(aDocument.getName())
                .description(aDocument.getDescription())
                .accountId(aDocument.getAccount().getId())
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Document> documents = documentRepository.findAll(documentFilters,
                documentSortFilters, pageFilters).stream().map(Document::withoutDbFields).toList();

        assertThat(documents).containsExactly(aDocument.withoutDbFields());

    }

    @SneakyThrows
    @Test
    public void testFindAll2(){
        Currency aCurrency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(FakeCountries.NIGERIA.toBuilder().currency(aCurrency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(aCurrency).build());

        Document aDocument = insertDocument(A_DOCUMENT.toBuilder().account(aAccount).build());
        Document anotherDocument = insertDocument(ANOTHER_DOCUMENT.toBuilder().account(aAccount).build());

        DocumentSortFilters documentSortFilters = DocumentSortFilters.builder()
                .direction(OrderDirection.DESC)
                .field(Document.SortingFields.ID)
                .build();
        DocumentFilters documentFilters = DocumentFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<Document> documents = documentRepository.findAll(documentFilters,
                documentSortFilters, pageFilters).stream().map(Document::withoutDbFields).toList();

        assertThat(documents).containsExactly(anotherDocument.withoutDbFields(), aDocument.withoutDbFields());

    }

    @SneakyThrows
    @Test
    public void findByIdTest() {
        Currency aCurrency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(FakeCountries.NIGERIA.toBuilder().currency(aCurrency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(aCurrency).build());

        Document insertedDocument = insertDocument(A_DOCUMENT.toBuilder().account(aAccount).build());
        assert insertedDocument.getId() != null;
        Optional<Document> optionalDocument = documentRepository.findById(insertedDocument.getId()).map(Document::withoutDbFields);

        assertThat(optionalDocument).contains(insertedDocument.withoutDbFields());
    }

    @SneakyThrows
    @Test
    public void updateTest() {
        Currency aCurrency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(FakeCountries.NIGERIA.toBuilder().currency(aCurrency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(aCurrency).build());

        Document insertedDocument = insertDocument(A_DOCUMENT.toBuilder().account(aAccount).build());

        Document toUpdate = insertedDocument.toBuilder()
                .name("New name")
                .build();

        Document updatedDocument = documentRepository.upsert(toUpdate);
        assert insertedDocument.getId() != null;
        Optional<Document> optionalDocument = documentRepository.findById(insertedDocument.getId()).map(Document::withoutDbFields);

        assertThat(optionalDocument).contains(updatedDocument.withoutDbFields());
        assertEquals(toUpdate.withoutDbFields(), updatedDocument.withoutDbFields());
    }

    @SneakyThrows
    @Test
    public void deleteTest() {
        Currency aCurrency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(FakeCountries.NIGERIA.toBuilder().currency(aCurrency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(aCurrency).build());

        Document insertedDocument = documentRepository.upsert(A_DOCUMENT.toBuilder().account(aAccount).build());
        assert insertedDocument.getId() != null;
        assertThat(documentRepository.findById(insertedDocument.getId()).map(Document::withoutDbFields))
                .contains(insertedDocument.withoutDbFields());

        documentRepository.deleteById(insertedDocument.getId());
        assertThat(documentRepository.findById(insertedDocument.getId())).isEmpty();
    }

}
