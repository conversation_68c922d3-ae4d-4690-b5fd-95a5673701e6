package pt.jumia.services.brad.data.brad.repository.psql;

import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlCurrencyRepositoryTest extends BaseRepositoryTest {

    @Test
    public void testFindAll(){
        FakeCurrencies.ALL_CURRENCIES.forEach(super::insertCurrency);
        List<Currency> currencies = currencyRepository.findAll(null);
        assertEquals(13, currencies.size());
    }

    @Test
    public void testFindById(){
        Currency aCurrency = insertCurrency(FakeCurrencies.NGN);
        Optional<Currency> currency = currencyRepository.findById(aCurrency.getId());
        assertEquals("Nigerian Naira", currency.get().getName());
        assertEquals("NGN", currency.get().getCode());
        assertEquals("₦", currency.get().getSymbol());
    }

    @Test
    public void testFindByCode(){
        Currency aCurrency = insertCurrency(FakeCurrencies.NGN);
        Optional<Currency> currency = currencyRepository.findByCode(aCurrency.getCode());
        assertEquals("Nigerian Naira", currency.get().getName());
        assertEquals("NGN", currency.get().getCode());
        assertEquals("₦", currency.get().getSymbol());

    }

    @Test
    public void testFindById_empty(){
        Optional<Currency> currency = currencyRepository.findById(999L);
        assertEquals(Optional.empty(), currency);
    }
}
