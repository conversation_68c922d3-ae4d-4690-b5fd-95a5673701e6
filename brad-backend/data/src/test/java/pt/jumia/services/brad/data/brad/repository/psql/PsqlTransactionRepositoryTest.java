package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.*;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.fake.*;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionSortFilters;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.enumerations.ReconciliationStatus;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlTransactionRepositoryTest extends BaseRepositoryTest {


    static DateTimeFormatter dateTimeformatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");
    static DateTimeFormatter dateformatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final Account AN_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .build();

    private static final AccountStatement AN_ACCOUNT_STATEMENT = FakeAccountStatements.getFakeAccountStatements(1,AN_ACCOUNT).get(0)
            .toBuilder()
            .initialDate(LocalDate.parse("2023-04-19", dateformatter))
            .finalDate(LocalDate.parse("2023-04-19", dateformatter))
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .build();

    private static final Transaction A_TRANSACTION = FakeTransaction.getFakeCreditTransactions(1,AN_ACCOUNT_STATEMENT).get(0)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
            .build();

    private static final Country A_COUNTRY = FakeCountries.NIGERIA;
    private static final Currency A_CURRENCY = FakeCurrencies.NGN;
    @SneakyThrows
    @Test
    public void insertAndFindTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .companyID("companyID1")
                .country(country)
                .currency(currency)
                .build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertTransaction(A_TRANSACTION.toBuilder().accountStatement(aAccountStatement).currency(currency).build());
        insertTransaction(A_TRANSACTION.toBuilder().accountStatement(aAccountStatement).currency(currency).build());

        TransactionSortFilters transactionSortFilters = TransactionSortFilters.builder()
                .direction(OrderDirection.ASC)
                .build();
        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(null,
                transactionSortFilters, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(2, transactions.size());

    }
    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withPartitionKeyFilter_returnOnlyTransactionWithPartitionKey() {
            Currency currency = insertCurrency(A_CURRENCY);
            Country country = insertCountry(A_COUNTRY.toBuilder()
                    .currency(currency)
                    .build());
            Account aAccount = insertAccount(AN_ACCOUNT.toBuilder()
                    .companyID("companyID2")
                    .country(country)
                    .currency(currency)
                    .build());
            Account anotherAccount = insertAccount(AN_ACCOUNT.toBuilder()
                    .companyID("companyID3")
                    .accountNumber("newNumber")
                    .country(country).currency(currency).build());

            AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder()
                    .account(aAccount)
                    .currency(currency)
                    .build());
            AccountStatement anotherAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder()
                    .account(anotherAccount)
                    .currency(currency)
                    .statementId("anotherStatementID")
                    .build());

            insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build());
            Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(anotherAccountStatement)
                    .currency(currency)
                    .build());
            insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(anotherAccountStatement)
                    .currency(currency)
                    .build());

        assert secondTransaction.getAccountStatement().getAccount().getId() != null;
        TransactionFilters transactionFilters = TransactionFilters.builder()
                    .partitionKey(secondTransaction.getAccountStatement().getAccount().getId().toString())
                    .build();

            List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                    null, null);

        assertEquals(2, transactions.size());
        assertEquals(secondTransaction.getAccountStatement().getAccount().getId(), transactions.get(0).getAccountStatement().getAccount().getId());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withTypeFilter_returnOnlyTransactionsWithType() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder()
                .account(aAccount)
                .currency(currency)
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .type("A_TYPE")
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .type("A_TYPE")
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .type("ANOTHER_TYPE")
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .type(secondTransaction.getType())
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();


        assertEquals(2, transactions.size());
        assertEquals(secondTransaction.getType(), transactions.get(0).getType());

        //testing with exactFilters
        String exactTypeFilter = "TYPE";
        TransactionFilters transactionFilters1 = TransactionFilters.builder()
                .type(exactTypeFilter)
                .exactFilters(true)
                .build();

        List<Transaction> transactions1 = transactionRepository.findAllWithReconciliation(transactionFilters1, null, null);
        assertEquals(0, transactions1.size());

        exactTypeFilter = "A_TYPE";
        transactionFilters1 = TransactionFilters.builder()
                .type(exactTypeFilter)
                .exactFilters(true)
                .build();

        transactions1 = transactionRepository.findAllWithReconciliation(transactionFilters1, null, null);
        assertEquals(2, transactions1.size());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withCurrencyFilter_returnOnlyTransactionsWithCurrency() {
        Currency aCurrency = insertCurrency(FakeCurrencies.EUR);
        Currency anotherCurrency = insertCurrency(FakeCurrencies.NGN);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(aCurrency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(aCurrency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(aCurrency).build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(anotherCurrency)
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(anotherCurrency)
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(aCurrency)
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .currency(List.of(anotherCurrency.getCode()))
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(2, transactions.size());
        assertEquals(secondTransaction.getCurrency(), transactions.get(0).getCurrency());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withValueDateFilter_returnOnlyTransactionsWithValueDate() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(LocalDate.parse("2023-04-19", dateformatter))
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(LocalDate.parse("2023-04-20", dateformatter))
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(LocalDate.parse("2023-04-21", dateformatter))
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .valueDateStart(secondTransaction.getValueDate())
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(1, transactions.size());
        assertEquals(secondTransaction.getValueDate(), transactions.get(0).getValueDate());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withTransactionDateFilter_returnOnlyTransactionsWithTransactionDate() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder()
                .account(aAccount)
                .currency(currency)
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .transactionDate(LocalDate.parse("2023-04-19", dateformatter))
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .transactionDate(LocalDate.parse("2023-04-20", dateformatter))
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .transactionDate(LocalDate.parse("2023-04-21", dateformatter))
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .transactionDateStart(secondTransaction.getTransactionDate())
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(1, transactions.size());
        assertEquals(secondTransaction.getTransactionDate(), transactions.get(0).getTransactionDate());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withStatementDateFilter_returnOnlyTransactionsWithStatementDate() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .statementDate(LocalDate.parse("2023-04-19", dateformatter))
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .statementDate(LocalDate.parse("2023-04-20", dateformatter))
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .statementDate(LocalDate.parse("2023-04-21", dateformatter))
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .statementDateStart(secondTransaction.getStatementDate())
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(1, transactions.size());
        assertEquals(secondTransaction.getStatementDate(), transactions.get(0).getStatementDate());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withDirectionFilter_returnOnlyTransactionsWithDirection() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .direction(Direction.CREDIT)
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .direction(Direction.CREDIT)
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .direction(Direction.DEBIT)
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .direction(List.of(secondTransaction.getDirection().name()))
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(2, transactions.size());
        assertEquals(secondTransaction.getDirection(), transactions.get(0).getDirection());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withAmountFilter_returnOnlyTransactionsWithAmount() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .amount(BigDecimal.valueOf(100))
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .amount(BigDecimal.valueOf(200))
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .amount(BigDecimal.valueOf(300))
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .amount(secondTransaction.getAmount())
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(1, transactions.size());
        assertEquals(secondTransaction.getAmount().intValue(), transactions.get(0).getAmount().intValue());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withReferenceFilter_returnOnlyTransactionsWithReference() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .reference("A_REFERENCE")
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .reference("A_REFERENCE")
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .reference("ANOTHER_REFERENCE")
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .reference(secondTransaction.getReference())
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(2, transactions.size());
        assertEquals(secondTransaction.getReference(), transactions.get(0).getReference());

        //testing with exactFilters
        String exactReferenceFilter = "REFERENCE";
        TransactionFilters transactionFilters1 = TransactionFilters.builder()
                .reference(exactReferenceFilter)
                .exactFilters(true)
                .build();

        List<Transaction> transactions1 = transactionRepository.findAllWithReconciliation(transactionFilters1, null, null);
        assertEquals(0, transactions1.size());

        exactReferenceFilter = "A_REFERENCE";
        transactionFilters1 = TransactionFilters.builder()
                .reference(exactReferenceFilter)
                .exactFilters(true)
                .build();

        transactions1 = transactionRepository.findAllWithReconciliation(transactionFilters1, null, null);
        assertEquals(2, transactions1.size());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withDescriptionFilter_returnOnlyTransactionsWithDescription() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country)
                .currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .description("A_DESCRIPTION")
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .description("A_DESCRIPTION")
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .description("ANOTHER_DESCRIPTION")
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .description(secondTransaction.getDescription())
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(2, transactions.size());
        assertEquals(secondTransaction.getDescription(), transactions.get(0).getDescription());

        //testing with exactFilters
        String exactDescriptionFilter = "DESCRIPTION";

        TransactionFilters transactionFilters1 = TransactionFilters.builder()
                .description(exactDescriptionFilter)
                .exactFilters(true)
                .build();

        List<Transaction> transactions1 = transactionRepository.findAllWithReconciliation(transactionFilters1, null, null);
        assertEquals(0, transactions1.size());

        exactDescriptionFilter = "A_DESCRIPTION";
        transactionFilters1 = TransactionFilters.builder()
                .description(exactDescriptionFilter)
                .exactFilters(true)
                .build();

        transactions1 = transactionRepository.findAllWithReconciliation(transactionFilters1, null, null);
        assertEquals(2, transactions1.size());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withAccountStatementIDFilter_returnOnlyTransactionsWithAccountStatementID() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        AccountStatement anotherAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount)
                .currency(currency)
                .statementId("123").build());

        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(anotherAccountStatement)
                .currency(currency)
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(anotherAccountStatement)
                .currency(currency)
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .accountStatementID(String.valueOf(secondTransaction.getAccountStatement().getId()))
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(2, transactions.size());
        assertEquals(secondTransaction.getAccountStatement().getId(), transactions.get(0).getAccountStatement().getId());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withRemittanceInformationFilter_returnOnlyTransactionsWithRemittanceInformation() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country)
                .currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .remittanceInformation("A_REMITTANCE_INFORMATION")
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .remittanceInformation("A_REMITTANCE_INFORMATION")
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .remittanceInformation("ANOTHER_REMITTANCE_INFORMATION")
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .remittanceInformation(secondTransaction.getRemittanceInformation())
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(2, transactions.size());
        assertEquals(secondTransaction.getRemittanceInformation(), transactions.get(0).getRemittanceInformation());

        //testing with exactFilters
        String exactRemittanceInformationFilter = "REMITTANCE_INFORMATION";

        TransactionFilters transactionFilters1 = TransactionFilters.builder()
                .remittanceInformation(exactRemittanceInformationFilter)
                .exactFilters(true)
                .build();

        List<Transaction> transactions1 = transactionRepository.findAllWithReconciliation(transactionFilters1, null, null);
        assertEquals(0, transactions1.size());

        exactRemittanceInformationFilter = "A_REMITTANCE_INFORMATION";
        transactionFilters1 = TransactionFilters.builder()
                .remittanceInformation(exactRemittanceInformationFilter)
                .exactFilters(true)
                .build();

        transactions1 = transactionRepository.findAllWithReconciliation(transactionFilters1, null, null);
        assertEquals(2, transactions1.size());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withOrderingPartyNameFilter_returnOnlyTransactionsWithOrderingPartyName() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country)
                .currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .orderingPartyName("A_ORDERING_PARTY_NAME")
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .orderingPartyName("A_ORDERING_PARTY_NAME")
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .orderingPartyName("ANOTHER_ORDERING_PARTY_NAME")
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .orderingPartyName(secondTransaction.getOrderingPartyName())
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(2, transactions.size());
        assertEquals(secondTransaction.getOrderingPartyName(), transactions.get(0).getOrderingPartyName());

        //testing with exactFilters
        String exactOrderingPartyNameFilter = "ORDERING_PARTY_NAME";

        TransactionFilters transactionFilters1 = TransactionFilters.builder()
                .orderingPartyName(exactOrderingPartyNameFilter)
                .exactFilters(true)
                .build();

        List<Transaction> transactions1 = transactionRepository.findAllWithReconciliation(transactionFilters1, null, null);
        assertEquals(0, transactions1.size());

        exactOrderingPartyNameFilter = "A_ORDERING_PARTY_NAME";
        transactionFilters1 = TransactionFilters.builder()
                .orderingPartyName(exactOrderingPartyNameFilter)
                .exactFilters(true)
                .build();

        transactions1 = transactionRepository.findAllWithReconciliation(transactionFilters1, null, null);
        assertEquals(2, transactions1.size());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withCreatedAtFilter_returnOnlyTransactionsWithCreatedAt() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());

        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .createdAt(LocalDateTime.parse("2023-04-20T17:58:03.687298", dateTimeformatter))
                .build());
        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .createdAt(LocalDateTime.parse("2023-04-21T17:58:03.687299", dateTimeformatter))
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .createdAtStart(secondTransaction.getCreatedAt().toLocalDate().atStartOfDay())
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters,
                null, null).stream().map(Transaction::withoutDbFields).toList();

        assertEquals(1, transactions.size());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withASCSorting_returnCorrectlySortedTransactions() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        AccountStatement anotherAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount)
                .currency(currency)
                .statementId("anotherStatementID").build());

        Transaction firstTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(anotherAccountStatement)
                .currency(currency)
                .createdAt(LocalDateTime.parse("2023-04-20T17:58:03.687298", dateTimeformatter))
                .build());
        Transaction thirdTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(anotherAccountStatement)
                .currency(currency)
                .createdAt(LocalDateTime.parse("2023-04-21T17:58:03.687299", dateTimeformatter))
                .build());

        TransactionSortFilters transactionSortFilters = TransactionSortFilters.builder()
                .field(Transaction.SortingFields.CREATED_AT)
                .direction(OrderDirection.ASC)
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(null,
                transactionSortFilters, null);

        assertEquals(3, transactions.size());
        assertEquals(firstTransaction.getCreatedAt(), transactions.get(0).getCreatedAt());
        assertEquals(secondTransaction.getCreatedAt(), transactions.get(1).getCreatedAt());
        assertEquals(thirdTransaction.getCreatedAt(), transactions.get(2).getCreatedAt());
    }

    @SneakyThrows
    @Test
    public void findAll_WithReconciliation_withDESCSorting_returnCorrectlySortedTransactions() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        AccountStatement anotherAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount)
                .currency(currency)
                .statementId("anotherStatementID").build());

        Transaction firstTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeformatter))
                .build());
        Transaction secondTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(anotherAccountStatement)
                .currency(currency)
                .createdAt(LocalDateTime.parse("2023-04-20T17:58:03.687298", dateTimeformatter))
                .build());
        Transaction thirdTransaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(anotherAccountStatement)
                .currency(currency)
                .createdAt(LocalDateTime.parse("2023-04-21T17:58:03.687299", dateTimeformatter))
                .build());

        TransactionSortFilters transactionSortFilters = TransactionSortFilters.builder()
                .field(Transaction.SortingFields.CREATED_AT)
                .direction(OrderDirection.DESC)
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(null,
                transactionSortFilters, null);

        assertEquals(3, transactions.size());
        assertEquals(thirdTransaction.getCreatedAt(), transactions.get(0).getCreatedAt());
        assertEquals(secondTransaction.getCreatedAt(), transactions.get(1).getCreatedAt());
        assertEquals(firstTransaction.getCreatedAt(), transactions.get(2).getCreatedAt());
    }

    @Test
    public void findAll_WithReconciliation_withReconciliationId_returnOnlyTransactionsWithReconciliationId() throws DatabaseErrorsException, EntityErrorsException {
        String companyId = "compID";
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .companyID(companyId)
                .build());

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());

        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(aAccount)
                    .idCompany(companyId)
                    .build());
        });
        tempBales.forEach(bale -> {
            try {
                bradBaleRepository.createPartition(bale.getIdCompany());
            } catch (DatabaseErrorsException e) {
                throw new RuntimeException(e);
            }
        });
        List<Bale> bales = insertBales(tempBales);
        List<Transaction> transactionList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }

        List<Reconciliation> reconciliations = new ArrayList<>();

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                    .id(i)
                    .account(aAccount)
                    .idCompany(companyId)
                    .baleIds(List.of(bales.get(i).getId()))
                    .transactionIds(List.of(transactionList.get(i).getId()))
                    .build());
        }

        List<Reconciliation> insertedReconciliations = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            insertedReconciliations.add(insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    bales.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount()));
        }

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .reconciliationId(insertedReconciliations.get(0).getId())
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters, null, null);
        assertEquals(1, transactions.size());

    }

    @Test
    public void findAll_WithReconciliation_withReconciliationCreator_returnOnlyTransactionsWithReconciliationCreator() throws DatabaseErrorsException, EntityErrorsException {
        String expectedReconciliationCreator = "newReconciliationCreator";
        String companyId = "compID";
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .companyID(companyId)
                .build());

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());

        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(aAccount)
                    .idCompany(companyId)
                    .build());
        });
        tempBales.forEach(bale -> {
            try {
                bradBaleRepository.createPartition(bale.getIdCompany());
            } catch (DatabaseErrorsException e) {
                throw new RuntimeException(e);
            }
        });
        List<Bale> bales = insertBales(tempBales);
        List<Transaction> transactionList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }

        List<Reconciliation> reconciliations = new ArrayList<>();

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                    .id(i)
                    .account(aAccount)
                    .idCompany(companyId)
                    .baleIds(List.of(bales.get(i).getId()))
                    .transactionIds(List.of(transactionList.get(i).getId()))
                    .build());
        }

        insertReconciliations(
                reconciliations.get(0).toBuilder()
                        .creator(expectedReconciliationCreator)
                        .build(),
                transactionList.get(0).getAmount(),
                bales.get(0).getAmount(),
                BigDecimal.TEN,
                reconciliations.get(0).getAccount());

        for (int i = 0; i < 3; i++) {
            insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    bales.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount());
        }

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .reconciliationCreator(expectedReconciliationCreator)
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters, null, null);
        assertEquals(1, transactions.size());

        //exact filter
        String exactReconciliationCreator = "reconciliationCreator";

        TransactionFilters transactionFilters1 = TransactionFilters.builder()
                .reconciliationCreator(exactReconciliationCreator)
                .exactFilters(true)
                .build();

        List<Transaction> transactions1 = transactionRepository.findAllWithReconciliation(transactionFilters1, null, null);
        assertEquals(0, transactions1.size());

        TransactionFilters transactionFilters2 = TransactionFilters.builder()
                .reconciliationCreator(expectedReconciliationCreator)
                .exactFilters(true)
                .build();

        List<Transaction> transactions2 = transactionRepository.findAllWithReconciliation(transactionFilters2, null, null);
        assertEquals(1, transactions2.size());
    }

    @Test
    public void findAll_WithReconciliation_withReconciliationCreationDate_returnOnlyTransactionsWithReconciliationCreationDate() throws DatabaseErrorsException, EntityErrorsException {

        LocalDateTime expectedReconciliationCreationDate = LocalDateTime.now().minusDays(2);
        String companyId = "compID";
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .companyID(companyId)
                .build());

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());

        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(aAccount)
                    .idCompany(companyId)
                    .build());
        });
        tempBales.forEach(bale -> {
            try {
                bradBaleRepository.createPartition(bale.getIdCompany());
            } catch (DatabaseErrorsException e) {
                throw new RuntimeException(e);
            }
        });
        List<Bale> bales = insertBales(tempBales);
        List<Transaction> transactionList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }

        List<Reconciliation> reconciliations = new ArrayList<>();

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                    .id(i)
                    .account(aAccount)
                    .idCompany(companyId)
                    .baleIds(List.of(bales.get(i).getId()))
                    .transactionIds(List.of(transactionList.get(i).getId()))
                    .build());
        }

        insertReconciliations(
                reconciliations.get(0).toBuilder()
                        .creationDate(expectedReconciliationCreationDate)
                        .build(),
                transactionList.get(0).getAmount(),
                bales.get(0).getAmount(),
                BigDecimal.TEN,
                reconciliations.get(0).getAccount());
        for (int i = 1; i < 3; i++) {
            insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    bales.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount());
        }

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .reconciliationCreationDateStart(expectedReconciliationCreationDate.toLocalDate().minusDays(1))
                .reconciliationCreationDateEnd(expectedReconciliationCreationDate.toLocalDate().plusDays(1))
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters, null, null);
        assertEquals(1, transactions.size());
    }

    @Test
    public void findAll_WithReconciliation_withReconciliationReviewer_returnOnlyTransactionsWithReconciliationReviewer() throws DatabaseErrorsException, EntityErrorsException {
        String expectedReconciliationReviewer = "newReconciliationReviewer";
        String companyId = "compID";
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .companyID(companyId)
                .build());

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());

        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(aAccount)
                    .idCompany(companyId)
                    .build());
        });
        tempBales.forEach(bale -> {
            try {
                bradBaleRepository.createPartition(bale.getIdCompany());
            } catch (DatabaseErrorsException e) {
                throw new RuntimeException(e);
            }
        });
        List<Bale> bales = insertBales(tempBales);
        List<Transaction> transactionList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }
        List<Reconciliation> reconciliations = new ArrayList<>();

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                    .id(i)
                    .account(aAccount)
                    .idCompany(companyId)
                    .baleIds(List.of(bales.get(i).getId()))
                    .transactionIds(List.of(transactionList.get(i).getId()))
                    .build());
        }

        Reconciliation insertedReconciliation = insertReconciliations(
                reconciliations.get(0),
                transactionList.get(0).getAmount(),
                bales.get(0).getAmount(),
                BigDecimal.TEN,
                reconciliations.get(0).getAccount());
        for (int i = 1; i < 3; i++) {
            insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    bales.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount());
        }

        reconciliationRepository.approve(insertedReconciliation.toBuilder()
                .reviewer(expectedReconciliationReviewer)
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .reconciliationReviewer(expectedReconciliationReviewer)
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters, null, null);
        assertEquals(1, transactions.size());

        //exact filter
        String exactReconciliationReviewer = "reconciliationReviewer";

        TransactionFilters transactionFilters1 = TransactionFilters.builder()
                .reconciliationReviewer(exactReconciliationReviewer)
                .exactFilters(true)
                .build();

        List<Transaction> transactions1 = transactionRepository.findAllWithReconciliation(transactionFilters1, null, null);
        assertEquals(0, transactions1.size());

        TransactionFilters transactionFilters2 = TransactionFilters.builder()
                .reconciliationReviewer(expectedReconciliationReviewer)
                .exactFilters(true)
                .build();

        List<Transaction> transactions2 = transactionRepository.findAllWithReconciliation(transactionFilters2, null, null);
        assertEquals(1, transactions2.size());

    }

    @Test
    public void findAll_WithReconciliation_withReconciliationReviewDate_returnOnlyTransactionsWithReviewDate() throws EntityErrorsException, DatabaseErrorsException {

        LocalDateTime creationDate = LocalDateTime.now();
        String companyId = "compID";
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .companyID(companyId)
                .build());

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());

        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(aAccount)
                    .idCompany(companyId)
                    .build());
        });
        tempBales.forEach(bale -> {
            try {
                bradBaleRepository.createPartition(bale.getIdCompany());
            } catch (DatabaseErrorsException e) {
                throw new RuntimeException(e);
            }
        });
        List<Bale> bales = insertBales(tempBales);
        List<Transaction> transactionList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }
        List<Reconciliation> reconciliations = new ArrayList<>();

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                    .id(i)
                    .account(aAccount)
                    .idCompany(companyId)
                    .baleIds(List.of(bales.get(i).getId()))
                    .transactionIds(List.of(transactionList.get(i).getId()))
                    .build());
        }

        Reconciliation insertedReconciliation = insertReconciliations(
                reconciliations.get(0).toBuilder()
                        .creationDate(creationDate)
                        .build(),
                transactionList.get(0).getAmount(),
                bales.get(0).getAmount(),
                BigDecimal.TEN,
                reconciliations.get(0).getAccount());
        for (int i = 1; i < 3; i++) {
            insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    bales.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount());
        }
        reconciliationRepository.approve(insertedReconciliation.toBuilder()
                .reviewer("reviewer")
                .reviewDate(creationDate)
                .build());

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .reconciliationReviewDateStart(LocalDate.now().minusDays(1))
                .reconciliationReviewDateEnd(LocalDate.now().plusDays(1))
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters, null, null);
        assertEquals(1, transactions.size());

    }

    @Test
    public void findAll_WithReconciliation_withReconciliationStatus_returnOnlyTransactionsWithReconciliationStatus() throws EntityErrorsException, DatabaseErrorsException {
        String companyId = "compID";
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .companyID(companyId)
                .build());

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());

        List<Bale> tempBales = new ArrayList<>();
        FakeBale.getFakeBale(20).forEach(bale -> {
            tempBales.add(bale.toBuilder()
                    .transactionCurrency(currency)
                    .account(aAccount)
                    .idCompany(companyId)
                    .build());
        });
        tempBales.forEach(bale -> {
            try {
                bradBaleRepository.createPartition(bale.getIdCompany());
            } catch (DatabaseErrorsException e) {
                throw new RuntimeException(e);
            }
        });
        List<Bale> bales = insertBales(tempBales);
        List<Transaction> transactionList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            transactionList.add(insertTransaction(A_TRANSACTION.toBuilder()
                    .accountStatement(aAccountStatement)
                    .currency(currency)
                    .build()));
        }
        List<Reconciliation> reconciliations = new ArrayList<>();

        for (int i = 0; i < 3; i++) {
            reconciliations.add(FakeReconciliation.FAKE_RECONCILIATION.toBuilder()
                    .id(i)
                    .account(aAccount)
                    .idCompany(companyId)
                    .baleIds(List.of(bales.get(i).getId()))
                    .transactionIds(List.of(transactionList.get(i).getId()))
                    .build());
        }

        insertReconciliations(
                reconciliations.get(0).toBuilder()
                        .status(ReconciliationStatus.REJECTED)
                        .build(),
                transactionList.get(0).getAmount(),
                bales.get(0).getAmount(),
                BigDecimal.TEN,
                reconciliations.get(0).getAccount());
        for (int i = 1; i < 3; i++) {
            insertReconciliations(
                    reconciliations.get(i),
                    transactionList.get(i).getAmount(),
                    bales.get(i).getAmount(),
                    BigDecimal.TEN,
                    reconciliations.get(i).getAccount());
        }

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .reconciliationStatus(List.of(ReconciliationStatus.REJECTED.name()))
                .build();

        List<Transaction> transactions = transactionRepository.findAllWithReconciliation(transactionFilters, null, null);
        assertEquals(1, transactions.size());
        assertEquals(transactions.get(0).getId(),transactionList.get(0).getId());

    }

    @Test
    public void findAllWithReconciliationTransactionWithoutUsdFxRate_withoutStatementFxRate_returnTransaction() throws DatabaseErrorsException {

        LocalDate valueDate = LocalDate.parse("2023-04-19", dateformatter);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Currency usdCurrency = insertCurrency(FakeCurrencies.USD);

        Set<FxRate> fxRates = new HashSet<>(insertFxRate(List.of(setupFxRate(currency, usdCurrency, valueDate))));

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());

        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(valueDate)
                .fxRates(fxRates)
                .createdAt(LocalDateTime.now())
                .build());

        Transaction transaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(valueDate)
                .createdAt(LocalDateTime.now())
                .build());

        List<Transaction> transactions = transactionRepository.findTransactionsWithoutFxRate(fxRates.iterator().next());

        assertEquals(1, transactions.size());
        assertEquals(transaction.getId(), transactions.get(0).getId());

    }

    @Test
    public void findAllWithReconciliationTransactionWithoutUsdFxRate_withStatementSameFxRate_returnEmpty() throws DatabaseErrorsException {

        LocalDate valueDate = LocalDate.parse("2023-04-19", dateformatter);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Currency usdCurrency = insertCurrency(FakeCurrencies.USD);

        Set<FxRate> fxRates = new HashSet<>(insertFxRate(List.of(setupFxRate(currency, usdCurrency, valueDate))));

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder()
                .account(aAccount)
                .fxRates(fxRates)
                .currency(currency).build());

        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(valueDate)
                .createdAt(LocalDateTime.now())
                .build());

        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(valueDate)
                .createdAt(LocalDateTime.now())
                .build());

        List<Transaction> transactions = transactionRepository.findTransactionsWithoutFxRate(fxRates.iterator().next());

        assertEquals(0, transactions.size());

    }

    @Test
    public void findAllWithReconciliationTransactionWithoutUsdFxRate_withStatementDifferentFxRate_returnTransaction() throws DatabaseErrorsException {

        LocalDate valueDate = LocalDate.parse("2023-04-19", dateformatter);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Currency usdCurrency = insertCurrency(FakeCurrencies.USD);
        Currency eurCurrency = insertCurrency(FakeCurrencies.EUR);

        List<FxRate> fxRatesToCreate = new ArrayList<>();
        fxRatesToCreate.add(setupFxRate(currency, eurCurrency, valueDate));

        Set<FxRate> fxRates = new HashSet<>(insertFxRate(fxRatesToCreate));

        Set<FxRate> newFxRates = new HashSet<>(insertFxRate(List.of(setupFxRate(currency, usdCurrency, valueDate))));

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder()
                .account(aAccount)
                .fxRates(fxRates)
                .currency(currency).build());

        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(valueDate)
                .createdAt(LocalDateTime.now())
                .build());

        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(valueDate)
                .createdAt(LocalDateTime.now())
                .build());

        List<Transaction> transactions = transactionRepository.findTransactionsWithoutFxRate(newFxRates.iterator().next());

        assertEquals(2, transactions.size());

    }

    @Test
    public void findAllWithReconciliationTransactionWithoutLcyFxRate_withoutStatementFxRate_returnTransaction() throws DatabaseErrorsException {

        LocalDate valueDate = LocalDate.parse("2023-04-19", dateformatter);

        Currency countryCurrency = insertCurrency(FakeCurrencies.AED);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(countryCurrency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Set<FxRate> fxRates = new HashSet<>(insertFxRate(List.of(setupFxRate(currency, countryCurrency, valueDate))));

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());

        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(valueDate)
                .fxRates(fxRates)
                .createdAt(LocalDateTime.now())
                .build());

        Transaction transaction = insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(valueDate)
                .createdAt(LocalDateTime.now())
                .build());

        List<Transaction> transactions = transactionRepository.findTransactionsWithoutFxRate(fxRates.iterator().next());

        assertEquals(1, transactions.size());
        assertEquals(transaction.getId(), transactions.get(0).getId());

    }

    @Test
    public void findAllWithReconciliationTransactionWithoutLcyFxRate_withStatementSameFxRate_returnEmpty() throws DatabaseErrorsException {

        LocalDate valueDate = LocalDate.parse("2023-04-19", dateformatter);

        Currency countryCurrency = insertCurrency(FakeCurrencies.AED);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(countryCurrency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Set<FxRate> fxRates = new HashSet<>(insertFxRate(List.of(setupFxRate(currency, countryCurrency, valueDate))));

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder()
                .account(aAccount)
                .fxRates(fxRates)
                .currency(currency).build());

        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(valueDate)
                .createdAt(LocalDateTime.now())
                .build());

        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(valueDate)
                .createdAt(LocalDateTime.now())
                .build());

        List<Transaction> transactions = transactionRepository.findTransactionsWithoutFxRate(fxRates.iterator().next());

        assertEquals(0, transactions.size());

    }

    @Test
    public void findAllWithReconciliationTransactionWithoutLcyFxRate_withStatementDifferentFxRate_returnTransaction() throws DatabaseErrorsException {

        LocalDate valueDate = LocalDate.parse("2023-04-19", dateformatter);

        Currency countryCurrency = insertCurrency(FakeCurrencies.AED);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(countryCurrency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Currency eurCurrency = insertCurrency(FakeCurrencies.EUR);

        List<FxRate> fxRatesToCreate = new ArrayList<>();
        fxRatesToCreate.add(setupFxRate(currency, eurCurrency, valueDate));

        Set<FxRate> fxRates = new HashSet<>(insertFxRate(fxRatesToCreate));

        Set<FxRate> newFxRates = new HashSet<>(insertFxRate(List.of(setupFxRate(currency, countryCurrency, valueDate))));

        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder()
                .account(aAccount)
                .fxRates(fxRates)
                .currency(currency).build());

        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(valueDate)
                .createdAt(LocalDateTime.now())
                .build());

        insertTransaction(A_TRANSACTION.toBuilder()
                .accountStatement(aAccountStatement)
                .currency(currency)
                .valueDate(valueDate)
                .createdAt(LocalDateTime.now())
                .build());

        List<Transaction> transactions = transactionRepository.findTransactionsWithoutFxRate(newFxRates.iterator().next());

        assertEquals(2, transactions.size());

    }

    private FxRate setupFxRate(Currency baseCurrency, Currency quoteCurrency, LocalDate rateDate) {
        return FakeFxRates.BASE_FX_RATE.toBuilder()
                .baseCurrency(baseCurrency)
                .quoteCurrency(quoteCurrency)
                .rateDate(rateDate)
                .bid(BigDecimal.TEN)
                .build();
    }

}
