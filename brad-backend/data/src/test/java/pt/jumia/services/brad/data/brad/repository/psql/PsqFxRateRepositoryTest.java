package pt.jumia.services.brad.data.brad.repository.psql;

import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.entities.fake.FakeFxRates;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.fxrate.FxRateFilters;
import pt.jumia.services.brad.domain.entities.filter.fxrate.FxRateSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;

@Transactional
public class PsqFxRateRepositoryTest extends BaseRepositoryTest {

    private static final List<FxRate> fxRates = FakeFxRates.FAKE_FX_RATE_LIST;

    @Test
    public void testSyncFindAll() throws EntityErrorsException, DatabaseErrorsException {
        
        List<FxRate> fxRateToInsert = insertFxRate(this.setupFxRateWithCurrency(fxRates));

        FxRateSortFilters fxRateSortFilters = FxRateSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(FxRate.SortingFields.TIMESTAMP_LAST_UPDATE)
                .build();
        FxRateFilters fxRateFilters = FxRateFilters.builder()
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<FxRate> fxRateList = bradFxRateRepository.findAll(fxRateFilters,
                fxRateSortFilters, pageFilters);

        assertFalse(fxRateList.isEmpty());

        assertThat(fxRateList).hasSize(fxRateToInsert.size());

    }

    @Test
    public void testFindWithBaseCurrency() throws EntityErrorsException, DatabaseErrorsException {
        List<FxRate> fxRateToInsert = List.of(
                FakeFxRates.FAKE_FX_RATE_EUR_USD,
                FakeFxRates.FAKE_FX_RATE_EUR_NGN,
                FxRate.builder()
                        .baseCurrency(Currency.builder().code("NGN").build())
                        .quoteCurrency(Currency.builder().code("EUR").build())
                        .rateDate(LocalDate.now())
                        .bid(new BigDecimal("0.00025"))
                        .bisLoadedAt(LocalDateTime.now())
                        .skAudInsert(1)
                        .skAudUpdate(1)
                        .timestampLastUpdate(LocalDateTime.now())
                        .build()
        );

        insertFxRate(this.setupFxRateWithCurrency(fxRateToInsert));

        FxRateSortFilters fxRateSortFilters = FxRateSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(FxRate.SortingFields.TIMESTAMP_LAST_UPDATE)
                .build();
        FxRateFilters fxRateFilters = FxRateFilters.builder()
                .baseCurrency(List.of("EUR"))
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<FxRate> fxRateList = bradFxRateRepository.findAll(fxRateFilters,
                fxRateSortFilters, pageFilters);

        assertFalse(fxRateList.isEmpty());

        assertThat(fxRateList).hasSize(fxRateToInsert.size()-1);
    }

    @Test
    public void testFindWithQuoteCurrency() throws EntityErrorsException, DatabaseErrorsException {
        List<FxRate> fxRateToInsert = List.of(
                FakeFxRates.FAKE_FX_RATE_EUR_USD,
                FakeFxRates.FAKE_FX_RATE_EUR_NGN,
                FxRate.builder()
                        .baseCurrency(Currency.builder().code("NGN").build())
                        .quoteCurrency(Currency.builder().code("EUR").build())
                        .rateDate(LocalDate.now())
                        .bid(new BigDecimal("0.00025"))
                        .bisLoadedAt(LocalDateTime.now())
                        .skAudInsert(1)
                        .skAudUpdate(1)
                        .timestampLastUpdate(LocalDateTime.now())
                        .build()
        );

        insertFxRate(this.setupFxRateWithCurrency(fxRateToInsert));

        FxRateSortFilters fxRateSortFilters = FxRateSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(FxRate.SortingFields.TIMESTAMP_LAST_UPDATE)
                .build();
        FxRateFilters fxRateFilters = FxRateFilters.builder()
                .quoteCurrency(List.of("EUR"))
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<FxRate> fxRateList = bradFxRateRepository.findAll(fxRateFilters,
                fxRateSortFilters, pageFilters);

        assertFalse(fxRateList.isEmpty());

        assertThat(fxRateList).hasSize(1);
    }

    @Test
    public void testFindWithRateDate() throws EntityErrorsException, DatabaseErrorsException {
        List<FxRate> fxRateToInsert = List.of(
                FakeFxRates.FAKE_FX_RATE_EUR_USD,
                FakeFxRates.FAKE_FX_RATE_EUR_NGN,
                FxRate.builder()
                        .baseCurrency(Currency.builder().code("NGN").build())
                        .quoteCurrency(Currency.builder().code("EUR").build())
                        .rateDate(LocalDate.now().minusDays(1))
                        .bid(new BigDecimal("0.00025"))
                        .bisLoadedAt(LocalDateTime.now())
                        .skAudInsert(1)
                        .skAudUpdate(1)
                        .timestampLastUpdate(LocalDateTime.now())
                        .build()
        );

        insertFxRate(this.setupFxRateWithCurrency(fxRateToInsert));

        FxRateSortFilters fxRateSortFilters = FxRateSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(FxRate.SortingFields.TIMESTAMP_LAST_UPDATE)
                .build();
        FxRateFilters fxRateFilters = FxRateFilters.builder()
                .rateDate(LocalDate.now().minusDays(1))
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<FxRate> fxRateList = bradFxRateRepository.findAll(fxRateFilters,
                fxRateSortFilters, pageFilters);

        assertFalse(fxRateList.isEmpty());

        assertThat(fxRateList).hasSize(1);
    }

    @Test
    public void testFindAllFxRateInRateDateOfCurrency() throws DatabaseErrorsException {
        List<FxRate> fxRateToInsert = List.of(
                FakeFxRates.FAKE_FX_RATE_EUR_USD,
                FakeFxRates.FAKE_FX_RATE_EUR_NGN,
                FxRate.builder()
                        .baseCurrency(Currency.builder().code("NGN").build())
                        .quoteCurrency(Currency.builder().code("EUR").build())
                        .rateDate(LocalDate.now().minusDays(1))
                        .bid(new BigDecimal("0.00025"))
                        .bisLoadedAt(LocalDateTime.now())
                        .skAudInsert(1)
                        .skAudUpdate(1)
                        .timestampLastUpdate(LocalDateTime.now())
                        .build(),
                FakeFxRates.FAKE_FX_RATE_EUR_USD.toBuilder()
                        .rateDate(LocalDate.now().minusDays(1))
                        .build()
        );

        insertFxRate(this.setupFxRateWithCurrency(fxRateToInsert));

        Currency currency = currencyRepository.findByCode("EUR").orElse(null);

        List<FxRate> fxRateList = bradFxRateRepository.findAllFxRateInRateDateOfBaseCurrency(LocalDate.now(), currency);

        assertFalse(fxRateList.isEmpty());

        assertThat(fxRateList).hasSize(2);
    }


    @Test
    public void testFindFxRateByRateDateAndBaseCurrencyAndQuoteCurrency() throws DatabaseErrorsException {
        List<FxRate> fxRateToInsert = List.of(
                FakeFxRates.FAKE_FX_RATE_EUR_USD,
                FakeFxRates.FAKE_FX_RATE_EUR_NGN,
                FxRate.builder()
                        .baseCurrency(Currency.builder().code("NGN").build())
                        .quoteCurrency(Currency.builder().code("EUR").build())
                        .rateDate(LocalDate.now().minusDays(1))
                        .bid(new BigDecimal("0.00025"))
                        .bisLoadedAt(LocalDateTime.now())
                        .skAudInsert(1)
                        .skAudUpdate(1)
                        .timestampLastUpdate(LocalDateTime.now())
                        .build(),
                FakeFxRates.FAKE_FX_RATE_EUR_USD.toBuilder()
                        .rateDate(LocalDate.now().minusDays(1))
                        .build()
        );

        List<FxRate> createdFxRates = insertFxRate(this.setupFxRateWithCurrency(fxRateToInsert));
        FxRate expectedFxRate = createdFxRates.stream()
                .filter(fxRate -> fxRate.getBaseCurrency().getCode().equals("EUR") && fxRate.getQuoteCurrency().getCode().equals("USD"))
                .findFirst()
                .orElse(null);
        Currency currencyEUR = currencyRepository.findByCode("EUR").orElse(null);
        Currency currencyUSD = currencyRepository.findByCode("USD").orElse(null);

        Optional<FxRate> fxRate = bradFxRateRepository.findFxRateByRateDateAndBaseCurrencyAndQuoteCurrency(
                LocalDate.now(),
                currencyEUR,
                currencyUSD
        );

        assertFalse(fxRate.isEmpty());

        assert expectedFxRate != null;
        assertThat(fxRate.get().withoutDbFields()).isEqualTo(expectedFxRate.withoutDbFields());
    }

    //findLastFxRateInBrad
    @Test
    public void testFindLastFxRateInBrad() throws DatabaseErrorsException {
        List<FxRate> fxRateToInsert = List.of(
                FakeFxRates.FAKE_FX_RATE_EUR_USD,
                FakeFxRates.FAKE_FX_RATE_EUR_NGN.toBuilder()
                        .timestampLastUpdate(LocalDateTime.now().plusDays(1))
                        .build(),
                FxRate.builder()
                        .baseCurrency(Currency.builder().code("NGN").build())
                        .quoteCurrency(Currency.builder().code("EUR").build())
                        .rateDate(LocalDate.now().minusDays(1))
                        .bid(new BigDecimal("0.00025"))
                        .bisLoadedAt(LocalDateTime.now())
                        .skAudInsert(1)
                        .skAudUpdate(1)
                        .timestampLastUpdate(LocalDateTime.now())
                        .build(),
                FakeFxRates.FAKE_FX_RATE_EUR_USD.toBuilder()
                        .rateDate(LocalDate.now().minusDays(1))
                        .timestampLastUpdate(LocalDateTime.now().minusDays(1))
                        .build()
        );

        List<FxRate> createdFxRates = insertFxRate(this.setupFxRateWithCurrency(fxRateToInsert));
        FxRate expectedFxRate = createdFxRates.stream()
                .filter(fxRate -> fxRate.getBaseCurrency().getCode().equals("EUR") && fxRate.getQuoteCurrency().getCode().equals("NGN"))
                .findFirst()
                .orElse(null);
        Optional<FxRate> fxRate = bradFxRateRepository.findLastFxRateInBrad();

        assertFalse(fxRate.isEmpty());

        assert expectedFxRate != null;
        assertThat(fxRate.get().withoutDbFields()).isEqualTo(expectedFxRate.withoutDbFields());
    }

    private List<FxRate> setupFxRateWithCurrency(List<FxRate> fxRates) {
        List<Currency> realCurrencies = new ArrayList<>();
        FakeCurrencies.ALL_CURRENCIES.forEach(currency -> {
            realCurrencies.add(insertCurrency(currency));
        });
        List<FxRate> fxRateListWithCurrencies = new ArrayList<>();
        fxRates.forEach(fxRate -> {
            Currency baseCurrency = realCurrencies.stream()
                    .filter(currency -> currency.getCode().equals(fxRate.getBaseCurrency().getCode()))
                    .findFirst()
                    .orElse(null);
            Currency quoteCurrency = realCurrencies.stream()
                    .filter(currency -> currency.getCode().equals(fxRate.getQuoteCurrency().getCode()))
                    .findFirst()
                    .orElse(null);
            fxRateListWithCurrencies.add(fxRate.toBuilder()
                    .baseCurrency(baseCurrency)
                    .quoteCurrency(quoteCurrency)
                    .build());
        });

        return fxRateListWithCurrencies;
    }

}
