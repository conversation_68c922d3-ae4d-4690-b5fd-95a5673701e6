package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementSortFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlAccountStatementRepositoryTest extends BaseRepositoryTest {


    static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");
    static DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final Account AN_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeFormatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeFormatter))
            .build();

    private static final AccountStatement AN_ACCOUNT_STATEMENT = FakeAccountStatements.getFakeAccountStatements(21,AN_ACCOUNT).get(20)
            .toBuilder()
            .initialDate(LocalDate.parse("2023-04-19", dateFormatter))
            .finalDate(LocalDate.parse("2023-04-19", dateFormatter))
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeFormatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeFormatter))
            .build();
    private static final AccountStatement ANOTHER_ACCOUNT_STATEMENT = FakeAccountStatements.getFakeAccountStatements(22,AN_ACCOUNT).get(21)
            .toBuilder()
            .initialDate(LocalDate.parse("2023-04-19", dateFormatter))
            .finalDate(LocalDate.parse("2023-04-19", dateFormatter))
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeFormatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", dateTimeFormatter))
            .build();

    private static final Country A_COUNTRY = FakeCountries.NIGERIA;
    private static final Currency A_CURRENCY = FakeCurrencies.NGN;
    
    @SneakyThrows
    @Test
    public void insertAndFindTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertAccountStatement(ANOTHER_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());

        AccountStatementSortFilters accountStatementSortFilters = AccountStatementSortFilters.builder()
                .direction(OrderDirection.ASC)
                .build();
        List<AccountStatement> accountStatements = accountStatementRepository.findAll(null,
            accountStatementSortFilters, null).stream().map(AccountStatement::withoutDbFields).toList();

        assertEquals(2, accountStatements.size());

    }

    @SneakyThrows
    @Test
    public void findAll_withCurrencyFilter_returnOnlyAccountStatementsWithCurrency() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);
        Currency aCurrency = insertCurrency(FakeCurrencies.NGN);
        Currency anotherCurrency = insertCurrency(FakeCurrencies.EUR);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(aCurrency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(aCurrency).build());
        Account anotherAccount = insertAccount(
            FakeAccounts.getFakeAccounts(2, null).get(1).toBuilder().country(country).currency(anotherCurrency).build());
        insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(anotherCurrency)
                .build());
        AccountStatement anotherAccountStatement = insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(anotherAccount)
                .currency(aCurrency)
                .build());
        insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(anotherAccount)
                .currency(anotherCurrency)
                .build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .currencyCodes(List.of(anotherAccountStatement.getCurrency().getCode()))
                .build();

        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters,
                null, null).stream().map(AccountStatement::withoutDbFields).toList();

        assertEquals(1, accountStatements.size());
        assertEquals(anotherAccountStatement.getCurrency(), accountStatements.get(0).getCurrency());
    }

    @SneakyThrows
    @Test
    public void findAll_withStatementIDFilter_returnOnlyAccountStatementsWithStatementID() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(currency)
                .statementId("*********")
                .build());

        AccountStatement aAccountStatement = insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(aAccount)
                .currency(currency)
                .statementId("*********")
                .build());

        insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(aAccount)
                .currency(currency)
                .statementId("*********")
                .build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .statementId(aAccountStatement.getStatementId())
                .build();

        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters,
                null, null).stream().map(AccountStatement::withoutDbFields).toList();

        assertEquals(1, accountStatements.size());
        assertEquals(aAccountStatement.getStatementId(), accountStatements.get(0).getStatementId());
    }

    @SneakyThrows
    @Test
    public void findAll_withPreviousStatement_returnOnlyAccountStatementsWithPreviousStatementID() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        AccountStatement firstAccountStatement = insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(currency)
                .previousStatement(null)
                .build());

        AccountStatement secondAccountStatement = insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(aAccount)
                .currency(currency)
                .previousStatement(firstAccountStatement)
                .build());

        insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(aAccount)
                .currency(currency)
                .previousStatement(secondAccountStatement)
                .build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .previousStatementID(secondAccountStatement.getPreviousStatement().getId())
                .build();

        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters,
                null, null).stream().map(AccountStatement::withoutDbFields).toList();

        assertEquals(1, accountStatements.size());
        assertEquals(secondAccountStatement.getPreviousStatement().getId(), accountStatements.get(0).getPreviousStatement().getId());
    }

    @SneakyThrows
    @Test
    public void findAll_withInitialDateFilter_returnOnlyAccountStatementsWithInitialDate() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialDate(LocalDate.parse("2023-04-19", dateFormatter))
                .build());

        AccountStatement secondAccountStatement = insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialDate(LocalDate.parse("2023-04-20", dateFormatter))
                .build());

        insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialDate(LocalDate.parse("2023-04-21", dateFormatter))
                .build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .initialDateStart(secondAccountStatement.getInitialDate())
                .build();

        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters,
                null, null).stream().map(AccountStatement::withoutDbFields).toList();

        assertEquals(1, accountStatements.size());
        assertEquals(secondAccountStatement.getInitialDate(), accountStatements.get(0).getInitialDate());
    }

    @SneakyThrows
    @Test
    public void findAll_withFinalDateFilter_returnOnlyAccountStatementsWithFinalDate() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(currency)
                .finalDate(LocalDate.parse("2023-04-19", dateFormatter))
                .build());

        insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(aAccount)
                .currency(currency)
                .finalDate(LocalDate.parse("2023-04-20", dateFormatter))
                .build());

        AccountStatement thirdAccountStatement = insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(aAccount)
                .currency(currency)
                .finalDate(LocalDate.parse("2023-04-21", dateFormatter))
                .build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .finalDateStart(thirdAccountStatement.getFinalDate())
                .build();

        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters,
                null, null).stream().map(AccountStatement::withoutDbFields).toList();

        assertEquals(1, accountStatements.size());
        assertEquals(thirdAccountStatement.getFinalDate(), accountStatements.get(0).getFinalDate());
    }

    @SneakyThrows
    @Test
    public void findAll_withInitialDirectionFilter_returnOnlyAccountStatementsWithInitialDirection() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialDirection(Direction.CREDIT)
                .build());

        AccountStatement secondAccountStatement = insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialDirection(Direction.CREDIT)
                .build());

        insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialDirection(Direction.DEBIT)
                .build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .initialDirection(List.of(String.valueOf(secondAccountStatement.getInitialDirection())))
                .build();

        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters,
                null, null).stream().map(AccountStatement::withoutDbFields).toList();

        assertEquals(2, accountStatements.size());
        assertEquals(secondAccountStatement.getInitialDirection(), accountStatements.get(0).getInitialDirection());
    }

    @SneakyThrows
    @Test
    public void findAll_withFinalDirectionFilter_returnOnlyAccountStatementsWithFinalDirection() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(currency)
                .finalDirection(Direction.CREDIT)
                .build());

        insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(aAccount)
                .currency(currency)
                .finalDirection(Direction.CREDIT)
                .build());

        AccountStatement thirdAccountStatement = insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(aAccount)
                .currency(currency)
                .finalDirection(Direction.DEBIT)
                .build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .finalDirection(List.of(String.valueOf(thirdAccountStatement.getFinalDirection())))
                .build();

        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters,
                null, null).stream().map(AccountStatement::withoutDbFields).toList();

        assertEquals(1, accountStatements.size());
        assertEquals(thirdAccountStatement.getFinalDirection(), accountStatements.get(0).getFinalDirection());
    }

    @SneakyThrows
    @Test
    public void findAll_withInitialAmountFilter_returnOnlyAccountStatementsWithSimilarInitialAmount() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialAmount(BigDecimal.valueOf(10))
                .build());

        insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialAmount(BigDecimal.valueOf(10))
                .build());

        insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialAmount(BigDecimal.valueOf(15))
                .build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .initialAmount(BigDecimal.valueOf(10))
                .build();

        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters,
                null, null).stream().map(AccountStatement::withoutDbFields).toList();


        assertEquals(2, accountStatements.size());
    }

    @SneakyThrows
    @Test
    public void findAll_withFinalAmountFilter_returnOnlyAccountStatementsWithSimilarFinalAmount() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(currency)
                .finalAmount(BigDecimal.valueOf(10))
                .build());

        insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(aAccount)
                .currency(currency)
                .finalAmount(BigDecimal.valueOf(10))
                .build());

        insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(aAccount)
                .currency(currency)
                .finalAmount(BigDecimal.valueOf(15))
                .build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .finalAmount(BigDecimal.valueOf(10))
                .build();

        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters,
                null, null).stream().map(AccountStatement::withoutDbFields).toList();

        assertEquals(2, accountStatements.size());
    }

    @SneakyThrows
    @Test
    public void findAll_withStatusFilter_returnOnlyAccountStatementsWithStatus() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(currency)
                .status(AccountStatementStatus.IMPORTED)
                .build());

        AccountStatement secondAccountStatement = insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(aAccount)
                .currency(currency)
                .status(AccountStatementStatus.REVIEW)
                .build());

        insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(aAccount)
                .currency(currency)
                .status(AccountStatementStatus.IMPORTED)
                .build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .status(List.of(secondAccountStatement.getStatus()))
                .build();

        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters,
                null, null).stream().map(AccountStatement::withoutDbFields).toList();

        assertEquals(1, accountStatements.size());
        assertEquals(secondAccountStatement.getStatus(), accountStatements.get(0).getStatus());

    }

    @SneakyThrows
    @Test
    public void findAll_withStatusDescription_returnOnlyAccountStatementsWithStatusDescription() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);

        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(currency)
                .statusDescription(AccountStatementStatus.Description.ERROR_OPENING_BALANCE)
                .build());

        insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(aAccount)
                .currency(currency)
                .statusDescription(AccountStatementStatus.Description.ERROR_CLOSING_BALANCE)
                .build());

        AccountStatement thirdAccountStatement = insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(aAccount)
                .currency(currency)
                .statusDescription(AccountStatementStatus.Description.ERROR_PREVIOUS_STATEMENT)
                .build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .statusDescription(List.of(AccountStatementStatus.Description.ERROR_PREVIOUS_STATEMENT))
                .build();

        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters,
                null, null).stream().map(AccountStatement::withoutDbFields).toList();

        assertEquals(1, accountStatements.size());
        assertEquals(thirdAccountStatement.getStatusDescription(), accountStatements.get(0).getStatusDescription());
    }

    @SneakyThrows
    @Test
    public void findAll_withAccountFilter_returnOnlyAccountStatementsWithAccount() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        Account anotherAccount = insertAccount(FakeAccounts.getFakeAccounts(2, null).get(1).toBuilder()
                .country(country).currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertAccountStatement(ANOTHER_ACCOUNT_STATEMENT.toBuilder().account(anotherAccount).currency(currency).build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .accountID(aAccount.getId())
                .build();

        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters,
                null, null).stream().map(AccountStatement::withoutDbFields).toList();

        assertEquals(1, accountStatements.size());
        assertEquals(aAccountStatement.getAccount().getId(), accountStatements.get(0).getAccount().getId());
    }

    @SneakyThrows
    @Test
    public void findAll_withASCSorting_returnCorrectlySortedAccountStatements() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        AccountStatement firstAccountStatement = insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialDate(LocalDate.parse("2023-04-19", dateFormatter))
                .build());
        AccountStatement secondAccountStatement = insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialDate(LocalDate.parse("2023-04-20", dateFormatter))
                .build());
        AccountStatement thirdAccountStatement = insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialDate(LocalDate.parse("2023-04-21", dateFormatter))
                .build());
        AccountStatementSortFilters accountStatementSortFilters = AccountStatementSortFilters.builder()
                .field(AccountStatement.SortingFields.INITIAL_DATE)
                .direction(OrderDirection.ASC)
                .build();
        List<AccountStatement> accountStatements = accountStatementRepository.findAll(null,
            accountStatementSortFilters, null).stream().map(AccountStatement::withoutDbFields).toList();
        assertEquals(3, accountStatements.size());
        assertEquals(firstAccountStatement.getInitialDate(), accountStatements.get(0).getInitialDate());
        assertEquals(secondAccountStatement.getInitialDate(), accountStatements.get(1).getInitialDate());
        assertEquals(thirdAccountStatement.getInitialDate(), accountStatements.get(2).getInitialDate());
    }

    @SneakyThrows
    @Test
    public void findAll_withDESCSorting_returnCorrectlySortedAccountStatements() {
        List<AccountStatement> accountStatementsToTest = FakeAccountStatements.getFakeAccountStatements(3, AN_ACCOUNT);
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        AccountStatement firstAccountStatement = insertAccountStatement(accountStatementsToTest.get(0).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialDate(LocalDate.parse("2023-04-19", dateFormatter))
                .build());
        AccountStatement secondAccountStatement = insertAccountStatement(accountStatementsToTest.get(1).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialDate(LocalDate.parse("2023-04-20", dateFormatter))
                .build());
        AccountStatement thirdAccountStatement = insertAccountStatement(accountStatementsToTest.get(2).toBuilder()
                .account(aAccount)
                .currency(currency)
                .initialDate(LocalDate.parse("2023-04-21", dateFormatter))
                .build());
        AccountStatementSortFilters accountStatementSortFilters = AccountStatementSortFilters.builder()
                .field(AccountStatement.SortingFields.INITIAL_DATE)
                .direction(OrderDirection.DESC)
                .build();
        List<AccountStatement> accountStatements = accountStatementRepository.findAll(null,
            accountStatementSortFilters, null).stream().map(AccountStatement::withoutDbFields).toList();
        assertEquals(3, accountStatements.size());
        assertEquals(thirdAccountStatement.getInitialDate(), accountStatements.get(0).getInitialDate());
        assertEquals(secondAccountStatement.getInitialDate(), accountStatements.get(1).getInitialDate());
        assertEquals(firstAccountStatement.getInitialDate(), accountStatements.get(2).getInitialDate());
    }


    @SneakyThrows
    @Test
    public void findByIdTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertAccountStatement(ANOTHER_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        assert aAccountStatement.getId() != null;
        Optional<AccountStatement> optionalAccountStatement = accountStatementRepository.findById(aAccountStatement.getId())
                .map(AccountStatement::withoutDbFields);

        assert optionalAccountStatement.isPresent();
        assertEquals(aAccountStatement.getStatementId(), optionalAccountStatement.get().getStatementId());
    }

    @SneakyThrows
    @Test
    public void updateTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertAccountStatement(ANOTHER_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());

        AccountStatement toUpdate = aAccountStatement.toBuilder()
                .finalAmount(BigDecimal.valueOf(21))
                .build();

        accountStatementRepository.upsert(toUpdate);
        assert aAccountStatement.getId() != null;
        Optional<AccountStatement> optionalAccountStatement = accountStatementRepository.findById(aAccountStatement.getId())
                .map(AccountStatement::withoutDbFields);
        assert optionalAccountStatement.isPresent();
        assertEquals("21.0000", optionalAccountStatement.get().getFinalAmount().toString());
    }

    @SneakyThrows
    @Test
    public void deleteTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        AccountStatement aAccountStatement = insertAccountStatement(AN_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        insertAccountStatement(ANOTHER_ACCOUNT_STATEMENT.toBuilder().account(aAccount).currency(currency).build());
        assert aAccountStatement.getId() != null;
        assertThat(accountStatementRepository.findById(aAccountStatement.getId()).isEmpty()).isFalse();

        accountStatementRepository.deleteById(aAccountStatement.getId());
        assertThat(accountStatementRepository.findById(aAccountStatement.getId())).isEmpty();
    }

    @Test
    public void findNextStatement_returnCorrectStatement() {
        List<AccountStatement> accountStatements = generateNLinkedStatements(3);

        Optional<AccountStatement> nextStatement = accountStatementRepository.findByPreviousStatementId(accountStatements.get(0));
        assert nextStatement.isPresent();
        assertEquals(accountStatements.get(1).getId(), nextStatement.get().getId());
    }

    @Test
    public void findAllStatementsOrdered_afterInsertingInBetween_returnCorrectOrder() {
        int amountToGenerate = 10;

        List<AccountStatement> accountStatements = generateNLinkedStatements(amountToGenerate);

        AccountStatement newStatement = ANOTHER_ACCOUNT_STATEMENT.toBuilder()
                .account(accountStatements.get(5).getAccount())
                .currency(accountStatements.get(5).getCurrency())
                .previousStatement(accountStatements.get(5).getPreviousStatement())
                .build();

        AccountStatement oldStatement = this.accountStatementRepository.upsert(accountStatements.get(5).toBuilder().previousStatement(null).build());

        AccountStatement insertedStatement = insertAccountStatement(newStatement);

        this.accountStatementRepository.upsert(oldStatement.toBuilder()
                .previousStatement(insertedStatement)
                .build());

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .status(AccountStatementStatus.getValues())
                .accountID(insertedStatement.getAccount().getId())
                .build();

        List<AccountStatement> orderedStatements = this.accountStatementRepository.findAllStatementsOrdered(accountStatementFilters);

        assertEquals(amountToGenerate + 1, orderedStatements.size());

        AccountStatement oldStatementInList = orderedStatements.stream().filter(statement ->
                statement.getId().equals(oldStatement.getId())).findFirst().get();

        assertEquals(insertedStatement.getId(), oldStatementInList.getPreviousStatement().getId());
        assertEquals(accountStatements.get(4).getId(), insertedStatement.getPreviousStatement().getId());
    }

    @Test
    public void findNextStatement_returnNoStatement(){
        List<AccountStatement> accountStatements = generateNLinkedStatements(3);

        Optional<AccountStatement> nextStatement = accountStatementRepository.findByPreviousStatementId(accountStatements.get(2));
        assert nextStatement.isEmpty();
    }

    @Test
    public void findLastStatementInList_returnCorrectStatement() {
        List<AccountStatement> accountStatements = generateNLinkedStatements(3);

        Optional<AccountStatement> lastStatement = accountStatementRepository.findLastStatementInList(accountStatements.get(0).getAccount().getId());
        assert lastStatement.isPresent();
        assertEquals(accountStatements.get(2).getId(), lastStatement.get().getId());
    }


    private List<AccountStatement> generateNLinkedStatements(int n) {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        List<AccountStatement> accountStatements = FakeAccountStatements.getFakeAccountStatements(n, aAccount);
        List<AccountStatement> insertedStatements = new ArrayList<>();
        for (int i=0; i<n;i++) {
            AccountStatement previousStatement = null;
            if (!insertedStatements.isEmpty()){
                previousStatement = insertedStatements.get(i-1);
            }
            insertedStatements.add(insertAccountStatement(accountStatements.get(i).toBuilder()
                    .account(aAccount)
                    .currency(currency)
                    .previousStatement(previousStatement)
                    .build()));
        }
        return insertedStatements;


    }


}
