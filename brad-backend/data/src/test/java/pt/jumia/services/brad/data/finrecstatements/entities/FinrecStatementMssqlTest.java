package pt.jumia.services.brad.data.finrecstatements.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.FinrecStatement;
import pt.jumia.services.brad.domain.entities.fake.FakeFinrecStatements;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class FinrecStatementMssqlTest {

    private static final FinrecStatement finrecStatement1 = FakeFinrecStatements.getSimpleFinrecStatements(1).get(0);
    private static FinrecStatement finrecStatement2;

    @BeforeAll
    public static void setUp() {
        finrecStatement2 = new FinrecStatementMssql(finrecStatement1).toEntity();
    }


    @Test
    public void testToEntityGetTranID() {
        assertEquals(finrecStatement1.getTranID(), finrecStatement2.getTranID());
    }

    @Test
    public void testToEntityGetInitialDate() {
        assertEquals(finrecStatement1.getInitialDate().withNano(0), finrecStatement2.getInitialDate().withNano(0));
    }

    @Test
    public void testToEntityGetFinalDate() {
        assertEquals(finrecStatement1.getFinalDate().withNano(0), finrecStatement2.getFinalDate().withNano(0));
    }

    @Test
    public void testToEntityGetAccountNumber() {
        assertEquals(finrecStatement1.getAccountNumber(), finrecStatement2.getAccountNumber());
    }

    @Test
    public void testToEntityGetOpeningBalance() {
        assertEquals(finrecStatement1.getOpeningBalance(), finrecStatement2.getOpeningBalance());
    }

    @Test
    public void testToEntityGetCurrency() {
        assertEquals(finrecStatement1.getCurrency(), finrecStatement2.getCurrency());
    }

    @Test
    public void testToEntityGetType() {
        assertEquals(finrecStatement1.getType(), finrecStatement2.getType());
    }

    @Test
    public void testToEntityGetHasTransaction() {
        assertEquals(finrecStatement1.getHasTransaction(), finrecStatement2.getHasTransaction());
    }

    @Test
    public void testToEntityGetRunningBalance() {
        assertEquals(finrecStatement1.getRunningBalance(), finrecStatement2.getRunningBalance());
    }

    @Test
    public void testToEntityGetReference() {
        assertEquals(finrecStatement1.getReference(), finrecStatement2.getReference());
    }

    @Test
    public void testToEntityGetValueDate() {
        assertEquals(finrecStatement1.getValueDate(), finrecStatement2.getValueDate());
    }

    @Test
    public void testToEntityGetDescription() {
        assertEquals(finrecStatement1.getDescription(), finrecStatement2.getDescription());
    }

    @Test
    public void testToEntityGetTranAmount() {
        assertEquals(finrecStatement1.getTranAmount(), finrecStatement2.getTranAmount());
    }

    @Test
    public void testToEntityGetDirection() {
        assertEquals(finrecStatement1.getDirection(), finrecStatement2.getDirection());
    }

    @Test
    public void testToEntityGetTranDate() {
        assertEquals(finrecStatement1.getTranDate(), finrecStatement2.getTranDate());
    }

    @Test
    public void testToEntityGetSkAudInsert() {
        assertEquals(finrecStatement1.getSkAudInsert(), finrecStatement2.getSkAudInsert());
    }

    @Test
    public void testToEntityGetTimestampRunAt() {
        assertEquals(finrecStatement1.getTimestampRunAt(), finrecStatement2.getTimestampRunAt());
    }
}
