package pt.jumia.services.brad.data.brad.entities;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class AccountStatementPsqlTest {

    private static final Account ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0);
    private static final AccountStatement ACCOUNT_STATEMENT_1 = FakeAccountStatements.getFakeAccountStatements(1, ACCOUNT).get(0);
    private static AccountStatement accountStatement2;

    @BeforeAll
    public static void setUp() {
        accountStatement2 = new AccountStatementPsql(ACCOUNT_STATEMENT_1).toEntity();
    }

    @Test
    void testToEntityGetId() {
        assertEquals(ACCOUNT_STATEMENT_1.getId(), accountStatement2.getId());
    }

    @Test
    void testToEntityGetCurrency() {
        assertEquals(ACCOUNT_STATEMENT_1.getCurrency(), accountStatement2.getCurrency());
    }

    @Test
    void testToEntityGetStatementId() {
        assertEquals(ACCOUNT_STATEMENT_1.getStatementId(), accountStatement2.getStatementId());
    }

    @Test
    void testToEntityGetPreviousStatement() {
        assertEquals(ACCOUNT_STATEMENT_1.getPreviousStatement(), accountStatement2.getPreviousStatement());
    }

    @Test
    void testToEntityGetInitialDate() {
        assertEquals(ACCOUNT_STATEMENT_1.getInitialDate(), accountStatement2.getInitialDate());
    }

    @Test
    void testToEntityGetFinalDate() {
        assertEquals(ACCOUNT_STATEMENT_1.getFinalDate(), accountStatement2.getFinalDate());
    }

    @Test
    void testToEntityGetInitialDirection() {
        assertEquals(ACCOUNT_STATEMENT_1.getInitialDirection(), accountStatement2.getInitialDirection());
    }

    @Test
    void testToEntityGetFinalDirection() {
        assertEquals(ACCOUNT_STATEMENT_1.getFinalDirection(), accountStatement2.getFinalDirection());
    }

    @Test
    void testToEntityGetInitialAmount() {
        assertEquals(ACCOUNT_STATEMENT_1.getInitialAmount(), accountStatement2.getInitialAmount());
    }

    @Test
    void testToEntityGetFinalAmount() {
        assertEquals(ACCOUNT_STATEMENT_1.getFinalAmount(), accountStatement2.getFinalAmount());
    }

    @Test
    void testToEntityGetStatus() {
        assertEquals(ACCOUNT_STATEMENT_1.getStatus(), accountStatement2.getStatus());
    }

    @Test
    void testToEntityGetStatusDescription() {
        assertEquals(ACCOUNT_STATEMENT_1.getStatusDescription(), accountStatement2.getStatusDescription());
    }

    @Test
    void testToEntityGetAccount() {
        assertEquals(ACCOUNT_STATEMENT_1.getAccount(), accountStatement2.getAccount());
    }

    @Test
    void testToEntityGetCreatedBy() {
        assertEquals(ACCOUNT_STATEMENT_1.getCreatedBy(), accountStatement2.getCreatedBy());
    }

    @Test
    void testToEntityGetCreatedAt() {
        assertEquals(ACCOUNT_STATEMENT_1.getCreatedAt(), accountStatement2.getCreatedAt());
    }

    @Test
    void testToEntityGetUpdatedBy() {
        assertEquals(ACCOUNT_STATEMENT_1.getUpdatedBy(), accountStatement2.getUpdatedBy());
    }

    @Test
    void testToEntityGetUpdatedAt() {
        assertEquals(ACCOUNT_STATEMENT_1.getUpdatedAt(), accountStatement2.getUpdatedAt());
    }


    @Test
    void testEntityFields() {
        Map<AccountStatement.SortingFields, String> expectedFields = Map.ofEntries(
                Map.entry(AccountStatement.SortingFields.ID, "id"),
                Map.entry(AccountStatement.SortingFields.CURRENCY, "currency"),
                Map.entry(AccountStatement.SortingFields.STATEMENT_ID, "statementId"),
                Map.entry(AccountStatement.SortingFields.PREVIOUS_STATEMENT_ID, "previousStatement"),
                Map.entry(AccountStatement.SortingFields.INITIAL_DATE, "initialDate"),
                Map.entry(AccountStatement.SortingFields.FINAL_DATE, "finalDate"),
                Map.entry(AccountStatement.SortingFields.INITIAL_DIRECTION, "initialDirection"),
                Map.entry(AccountStatement.SortingFields.FINAL_DIRECTION, "finalDirection"),
                Map.entry(AccountStatement.SortingFields.INITIAL_AMOUNT, "initialAmount"),
                Map.entry(AccountStatement.SortingFields.FINAL_AMOUNT, "finalAmount"),
                Map.entry(AccountStatement.SortingFields.STATUS, "status"),
                Map.entry(AccountStatement.SortingFields.STATUS_DESCRIPTION, "statusDescription"),
                Map.entry(AccountStatement.SortingFields.DESCRIPTION, "description"),
                Map.entry(AccountStatement.SortingFields.FLOW, "flow"),
                Map.entry(AccountStatement.SortingFields.CREATED_AT, "createdAt"),
                Map.entry(AccountStatement.SortingFields.CREATED_BY, "createdBy"),
                Map.entry(AccountStatement.SortingFields.UPDATED_AT, "updatedAt"),
                Map.entry(AccountStatement.SortingFields.UPDATED_BY, "updatedBy")
        );

        assertEquals(expectedFields, AccountStatementPsql.getEntityFields(AccountStatement.SortingFields.class));
    }
}
