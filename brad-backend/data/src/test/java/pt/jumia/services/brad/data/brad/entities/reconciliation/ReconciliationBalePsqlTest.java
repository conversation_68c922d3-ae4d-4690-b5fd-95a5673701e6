package pt.jumia.services.brad.data.brad.entities.reconciliation;

import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.data.brad.entities.BalePsql;
import pt.jumia.services.brad.data.brad.entities.reconciliation.bale.ReconciliationBalePsql;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeReconciliation;

import java.util.HashSet;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ReconciliationBalePsqlTest {


    @Test
    public void testConstructor() {

        ReconciliationPsql reconciliationPsql = new ReconciliationPsql(FakeReconciliation.FAKE_RECONCILIATION,
                new HashSet<>(), new HashSet<>());
        BalePsql balePsql = new BalePsql(FakeBale.FAKE_BALE);

        ReconciliationBalePsql reconciliationBalePsql = new ReconciliationBalePsql(reconciliationPsql, balePsql);


        assertEquals(reconciliationPsql, reconciliationBalePsql.getReconciliation());
        assertEquals(balePsql, reconciliationBalePsql.getBalePsql());
    }


}
