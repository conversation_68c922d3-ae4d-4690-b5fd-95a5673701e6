package pt.jumia.services.brad.domain.usecases.bale.batch;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.usecases.executionlogs.ReadExecutionLogsUseCase;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class BaleErrorReporterTest {

    @Mock
    private ReadExecutionLogsUseCase readExecutionLogsUseCase;

    private BaleErrorReporter baleErrorReporter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        baleErrorReporter = new BaleErrorReporter(readExecutionLogsUseCase);
    }

    @Test
    void testGenerateErrorSummaryWithNoErrors() {
        // Arrange
        ExecutionLog executionLog = ExecutionLog.builder()
                .id(1L)
                .logType(ExecutionLog.ExecutionLogType.BALE)
                .logStatus(ExecutionLog.ExecutionLogStatus.SYNCED)
                .recordsAmount(100)
                .executionStartTime(LocalDateTime.now().minusMinutes(5))
                .executionEndTime(LocalDateTime.now())
                .errors(List.of())
                .build();

        // Act
        String summary = baleErrorReporter.generateErrorSummary(executionLog);

        // Assert
        assertNotNull(summary);
        assertTrue(summary.contains("BALE SYNC ERROR SUMMARY"));
        assertTrue(summary.contains("ExecutionLog ID: 1"));
        assertTrue(summary.contains("Status: SYNCED"));
        assertTrue(summary.contains("Records Amount: 100"));
        assertTrue(summary.contains("No errors recorded"));
    }

    @Test
    void testGenerateErrorSummaryWithErrors() {
        // Arrange
        List<ExecutionLog.SyncingError> errors = List.of(
                ExecutionLog.SyncingError.builder()
                        .errorDescription("Account not found")
                        .errorCategory("RECOVERABLE")
                        .operationContext("PROCESS_PHASE")
                        .accountNumber("12345")
                        .entryNo(1001)
                        .build(),
                ExecutionLog.SyncingError.builder()
                        .errorDescription("Database connection timeout")
                        .errorCategory("CRITICAL")
                        .operationContext("WRITE_PHASE")
                        .accountNumber("12346")
                        .entryNo(1002)
                        .build(),
                ExecutionLog.SyncingError.builder()
                        .errorDescription("Invalid data format")
                        .errorCategory("DATA_ERROR")
                        .operationContext("READ_PHASE")
                        .accountNumber("12347")
                        .entryNo(1003)
                        .build()
        );

        ExecutionLog executionLog = ExecutionLog.builder()
                .id(2L)
                .logType(ExecutionLog.ExecutionLogType.BALE)
                .logStatus(ExecutionLog.ExecutionLogStatus.PARTIAL_SUCCESS)
                .recordsAmount(75)
                .executionStartTime(LocalDateTime.now().minusMinutes(5))
                .executionEndTime(LocalDateTime.now())
                .errors(errors)
                .build();

        // Act
        String summary = baleErrorReporter.generateErrorSummary(executionLog);

        // Assert
        assertNotNull(summary);
        assertTrue(summary.contains("BALE SYNC ERROR SUMMARY"));
        assertTrue(summary.contains("ExecutionLog ID: 2"));
        assertTrue(summary.contains("Status: PARTIAL_SUCCESS"));
        assertTrue(summary.contains("Records Amount: 75"));
        assertTrue(summary.contains("Total Errors: 3"));
        assertTrue(summary.contains("ERROR BREAKDOWN BY CATEGORY"));
        assertTrue(summary.contains("RECOVERABLE: 1 errors"));
        assertTrue(summary.contains("CRITICAL: 1 errors"));
        assertTrue(summary.contains("DATA_ERROR: 1 errors"));
        assertTrue(summary.contains("ERROR BREAKDOWN BY OPERATION PHASE"));
        assertTrue(summary.contains("CRITICAL ERRORS REQUIRE IMMEDIATE ATTENTION"));
        assertTrue(summary.contains("Database connection timeout"));
        assertTrue(summary.contains("ERRORS BY ACCOUNT"));
        assertTrue(summary.contains("Account 12345: 1 errors"));
        assertTrue(summary.contains("RECOVERY RECOMMENDATIONS"));
    }

    @Test
    void testGenerateErrorSummaryById() {
        // Arrange
        Long executionLogId = 1L;
        ExecutionLog executionLog = ExecutionLog.builder()
                .id(executionLogId)
                .logType(ExecutionLog.ExecutionLogType.BALE)
                .logStatus(ExecutionLog.ExecutionLogStatus.SYNCED)
                .recordsAmount(100)
                .errors(List.of())
                .build();

        when(readExecutionLogsUseCase.execute(executionLogId)).thenReturn(executionLog);

        // Act
        String summary = baleErrorReporter.generateErrorSummary(executionLogId);

        // Assert
        assertNotNull(summary);
        assertTrue(summary.contains("ExecutionLog ID: 1"));
        verify(readExecutionLogsUseCase).execute(executionLogId);
    }

    @Test
    void testGenerateErrorSummaryByIdWithException() {
        // Arrange
        Long executionLogId = 1L;
        when(readExecutionLogsUseCase.execute(executionLogId)).thenThrow(new RuntimeException("Database error"));

        // Act
        String summary = baleErrorReporter.generateErrorSummary(executionLogId);

        // Assert
        assertNotNull(summary);
        assertTrue(summary.contains("Error generating summary"));
        assertTrue(summary.contains("Database error"));
        verify(readExecutionLogsUseCase).execute(executionLogId);
    }
}