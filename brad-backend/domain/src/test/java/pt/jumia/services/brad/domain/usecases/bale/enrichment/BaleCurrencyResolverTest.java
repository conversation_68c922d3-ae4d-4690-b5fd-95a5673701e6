package pt.jumia.services.brad.domain.usecases.bale.enrichment;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for BaleCurrencyResolver focusing on currency determination logic.
 * 
 * Tests cover:
 * - Currency resolution from account information
 * - USD fallback when account currency is unavailable
 * - Exception handling for currency resolution failures
 * - Various account currency scenarios
 */
@ExtendWith(MockitoExtension.class)
class BaleCurrencyResolverTest {

    @Mock
    private ReadCurrenciesUseCase readCurrenciesUseCase;

    private BaleCurrencyResolver resolver;

    @BeforeEach
    void setUp() {
        resolver = new BaleCurrencyResolver(readCurrenciesUseCase);
    }

    @Test
    void determineCurrency_WithAccountCurrencyId_ShouldReturnAccountCurrency() throws CurrencyResolutionException {
        // Given
        Currency accountCurrency = Currency.builder().id(1L).build();
        Account account = Account.builder().currency(accountCurrency).build();
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder().account(account).build();
        
        Currency expectedCurrency = FakeCurrencies.USD;
        when(readCurrenciesUseCase.execute("EUR")).thenReturn(expectedCurrency);

        // When
        Currency result = resolver.determineCurrency(bale, account);

        // Then
        assertNotNull(result, "Should return a currency");
        assertEquals(expectedCurrency, result, "Should return the account currency");
        verify(readCurrenciesUseCase).execute("EUR");
        verify(readCurrenciesUseCase, never()).execute("USD");
    }

    @Test
    void determineCurrency_WithNullAccountCurrency_ShouldReturnUSD() throws CurrencyResolutionException {
        // Given
        Account account = Account.builder().currency(null).build();
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder().account(account).build();
        
        Currency expectedUsdCurrency = FakeCurrencies.USD;
        when(readCurrenciesUseCase.execute("USD")).thenReturn(expectedUsdCurrency);

        // When
        Currency result = resolver.determineCurrency(bale, account);

        // Then
        assertNotNull(result, "Should return a currency");
        assertEquals(expectedUsdCurrency, result, "Should return USD currency");
        verify(readCurrenciesUseCase).execute("USD");
    }

    @Test
    void determineCurrency_WithAccountCurrencyButNullId_ShouldReturnUSD() throws CurrencyResolutionException {
        // Given
        Currency accountCurrencyWithNullId = Currency.builder().id(null).build();
        Account account = Account.builder().currency(accountCurrencyWithNullId).build();
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder().account(account).build();
        
        Currency expectedUsdCurrency = FakeCurrencies.USD;
        when(readCurrenciesUseCase.execute("USD")).thenReturn(expectedUsdCurrency);

        // When
        Currency result = resolver.determineCurrency(bale, account);

        // Then
        assertNotNull(result, "Should return a currency");
        assertEquals(expectedUsdCurrency, result, "Should return USD currency when account currency ID is null");
        verify(readCurrenciesUseCase).execute("USD");
    }

    @Test
    void determineCurrency_WithUseCaseException_ShouldThrowCurrencyResolutionException() {
        // Given
        Currency accountCurrency = Currency.builder().id(2L).build();
        Account account = Account.builder().currency(accountCurrency).build();
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder().account(account).build();
        
        when(readCurrenciesUseCase.execute("GBP"))
            .thenThrow(new RuntimeException("Currency service unavailable"));

        // When & Then
        CurrencyResolutionException exception = assertThrows(CurrencyResolutionException.class,
            () -> resolver.determineCurrency(bale, account),
            "Should throw CurrencyResolutionException when use case fails");
        
        assertTrue(exception.getMessage().contains("Failed to determine currency"),
            "Exception message should indicate currency determination failure");
        assertTrue(exception.getMessage().contains(bale.getEntryNo().toString()),
            "Exception message should include bale entry number for context");
        verify(readCurrenciesUseCase).execute("GBP");
    }

    @Test
    void determineCurrency_WithNullCurrencyFromUseCase_ShouldThrowCurrencyResolutionException() {
        // Given
        Currency accountCurrency = Currency.builder().id(3L).build();
        Account account = Account.builder().currency(accountCurrency).build();
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder().account(account).build();
        
        when(readCurrenciesUseCase.execute("JPY")).thenReturn(null);

        // When & Then
        CurrencyResolutionException exception = assertThrows(CurrencyResolutionException.class,
            () -> resolver.determineCurrency(bale, account),
            "Should throw CurrencyResolutionException when use case returns null");
        
        assertTrue(exception.getMessage().contains("Currency resolution returned null"),
            "Exception message should indicate null currency issue");
        assertTrue(exception.getMessage().contains(bale.getEntryNo().toString()),
            "Exception message should include bale entry number for context");
        verify(readCurrenciesUseCase).execute("JPY");
    }

    @Test
    void determineCurrency_WithUSDFallbackException_ShouldThrowCurrencyResolutionException() {
        // Given
        Account account = Account.builder().currency(null).build();
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder().account(account).build();
        
        when(readCurrenciesUseCase.execute("USD"))
            .thenThrow(new RuntimeException("USD currency not found"));

        // When & Then
        CurrencyResolutionException exception = assertThrows(CurrencyResolutionException.class,
            () -> resolver.determineCurrency(bale, account),
            "Should throw CurrencyResolutionException when USD fallback fails");
        
        assertTrue(exception.getMessage().contains("Failed to determine currency"),
            "Exception message should indicate currency determination failure");
        verify(readCurrenciesUseCase).execute("USD");
    }

    @Test
    void determineCurrency_WithDifferentCurrencyIds_ShouldCallUseCaseWithCorrectParameters() throws CurrencyResolutionException {
        // Given
        Long[] currencyIds = {1L, 2L, 3L, 4L};
        Currency[] expectedCurrencies = new Currency[currencyIds.length];
        
        for (int i = 0; i < currencyIds.length; i++) {
            Currency accountCurrency = Currency.builder().id(currencyIds[i]).build();
            Account account = Account.builder().currency(accountCurrency).build();
            Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder().account(account).build();
            
            expectedCurrencies[i] = FakeCurrencies.USD;
            when(readCurrenciesUseCase.execute(currencyIds[i].toString())).thenReturn(expectedCurrencies[i]);

            // When
            Currency result = resolver.determineCurrency(bale, account);

            // Then
            assertEquals(expectedCurrencies[i], result, 
                "Should return correct currency for " + currencyIds[i]);
            verify(readCurrenciesUseCase).execute(currencyIds[i].toString());
        }
    }

    @Test
    void determineCurrency_WithEmptyStringCurrencyId_ShouldReturnUSD() throws CurrencyResolutionException {
        // Given
        Currency accountCurrencyWithEmptyId = Currency.builder().id(null).build();
        Account account = Account.builder().currency(accountCurrencyWithEmptyId).build();
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder().account(account).build();
        
        Currency expectedUsdCurrency = FakeCurrencies.USD;
        when(readCurrenciesUseCase.execute("USD")).thenReturn(expectedUsdCurrency);

        // When
        Currency result = resolver.determineCurrency(bale, account);

        // Then
        assertNotNull(result, "Should return a currency");
        assertEquals(expectedUsdCurrency, result, "Should return USD currency when account currency ID is empty");
        verify(readCurrenciesUseCase).execute("USD");
    }

    @Test
    void determineCurrency_WithValidAccountCurrencyMultipleTimes_ShouldCallUseCaseEachTime() throws CurrencyResolutionException {
        // Given
        Currency accountCurrency = Currency.builder().id(4L).build();
        Account account = Account.builder().currency(accountCurrency).build();
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder().account(account).build();
        
        Currency expectedCurrency = FakeCurrencies.USD;
        when(readCurrenciesUseCase.execute("CHF")).thenReturn(expectedCurrency);

        // When
        Currency result1 = resolver.determineCurrency(bale, account);
        Currency result2 = resolver.determineCurrency(bale, account);
        Currency result3 = resolver.determineCurrency(bale, account);

        // Then
        assertEquals(expectedCurrency, result1, "First call should return expected currency");
        assertEquals(expectedCurrency, result2, "Second call should return expected currency");
        assertEquals(expectedCurrency, result3, "Third call should return expected currency");
        
        // Should call use case each time (no caching)
        verify(readCurrenciesUseCase, times(3)).execute("CHF");
    }

    @Test
    void determineCurrency_WithSpecificExceptionWrapping_ShouldPreserveCauseInformation() {
        // Given
        Currency accountCurrency = Currency.builder().id(5L).build();
        Account account = Account.builder().currency(accountCurrency).build();
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder().account(account).build();
        
        RuntimeException originalException = new RuntimeException("Database timeout");
        when(readCurrenciesUseCase.execute("NOK")).thenThrow(originalException);

        // When & Then
        CurrencyResolutionException exception = assertThrows(CurrencyResolutionException.class,
            () -> resolver.determineCurrency(bale, account),
            "Should throw CurrencyResolutionException");
        
        assertEquals(originalException, exception.getCause(),
            "Should preserve original exception as cause");
        assertTrue(exception.getMessage().contains("Failed to determine currency"),
            "Should have descriptive message");
        assertTrue(exception.getMessage().contains(bale.getEntryNo().toString()),
            "Should include bale entry number in message");
    }
}
