package pt.jumia.services.brad.domain.usecases.bale.validation;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.exceptions.InvalidEntityException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for BaleValidator focusing on validation logic and exception handling.
 * 
 * Tests cover:
 * - Bale validation rules and business logic
 * - Proper use of InvalidEntityException (existing domain exception)
 * - Account and entry number validation
 * - Error message clarity and context
 */
@ExtendWith(MockitoExtension.class)
class BaleValidatorTest {

    private BaleValidator validator;

    @BeforeEach
    void setUp() {
        validator = new BaleValidator();
    }

    @Test
    void validateBale_WithValidBale_ShouldPassValidation() throws InvalidEntityException {
        // Given
        Bale validBale = FakeBale.getFakeBale(1).get(0);

        // When & Then
        assertDoesNotThrow(() -> validator.validateBale(validBale),
            "Valid bale should pass validation without exceptions");
    }

    @Test
    void validateBale_WithNullBale_ShouldThrowInvalidEntityException() {
        // Given
        Bale nullBale = null;

        // When & Then
        InvalidEntityException exception = assertThrows(InvalidEntityException.class,
            () -> validator.validateBale(nullBale),
            "Null bale should throw InvalidEntityException");
        
        assertTrue(exception.getMessage().contains("cannot be null"),
            "Exception message should indicate null bale issue");
    }

    @Test
    void validateBale_WithNullAccount_ShouldThrowInvalidEntityException() {
        // Given
        Bale baleWithNullAccount = FakeBale.getFakeBale(1).get(0).toBuilder()
            .account(null)
            .build();

        // When & Then
        InvalidEntityException exception = assertThrows(InvalidEntityException.class,
            () -> validator.validateBale(baleWithNullAccount),
            "Bale with null account should throw InvalidEntityException");
        
        assertTrue(exception.getMessage().contains("account cannot be null"),
            "Exception message should indicate null account issue");
        assertTrue(exception.getMessage().contains("entry: " + baleWithNullAccount.getEntryNo()),
            "Exception message should include entry number for context");
    }

    @Test
    void validateBale_WithNullNavReference_ShouldThrowInvalidEntityException() {
        // Given
        Account accountWithNullNavRef = Account.builder()
            .navReference(null)
            .build();
        
        Bale baleWithNullNavRef = FakeBale.getFakeBale(1).get(0).toBuilder()
            .account(accountWithNullNavRef)
            .build();

        // When & Then
        InvalidEntityException exception = assertThrows(InvalidEntityException.class,
            () -> validator.validateBale(baleWithNullNavRef),
            "Bale with null NAV reference should throw InvalidEntityException");
        
        assertTrue(exception.getMessage().contains("NAV reference cannot be null or empty"),
            "Exception message should indicate NAV reference issue");
        assertTrue(exception.getMessage().contains("entry: " + baleWithNullNavRef.getEntryNo()),
            "Exception message should include entry number for context");
    }

    @Test
    void validateBale_WithEmptyNavReference_ShouldThrowInvalidEntityException() {
        // Given
        Account accountWithEmptyNavRef = Account.builder()
            .navReference("")
            .build();
        
        Bale baleWithEmptyNavRef = FakeBale.getFakeBale(1).get(0).toBuilder()
            .account(accountWithEmptyNavRef)
            .build();

        // When & Then
        InvalidEntityException exception = assertThrows(InvalidEntityException.class,
            () -> validator.validateBale(baleWithEmptyNavRef),
            "Bale with empty NAV reference should throw InvalidEntityException");
        
        assertTrue(exception.getMessage().contains("NAV reference cannot be null or empty"),
            "Exception message should indicate NAV reference issue");
    }

    @Test
    void validateBale_WithWhitespaceOnlyNavReference_ShouldThrowInvalidEntityException() {
        // Given
        Account accountWithWhitespaceNavRef = Account.builder()
            .navReference("   ")
            .build();
        
        Bale baleWithWhitespaceNavRef = FakeBale.getFakeBale(1).get(0).toBuilder()
            .account(accountWithWhitespaceNavRef)
            .build();

        // When & Then
        InvalidEntityException exception = assertThrows(InvalidEntityException.class,
            () -> validator.validateBale(baleWithWhitespaceNavRef),
            "Bale with whitespace-only NAV reference should throw InvalidEntityException");
        
        assertTrue(exception.getMessage().contains("NAV reference cannot be null or empty"),
            "Exception message should indicate NAV reference issue");
    }

    @Test
    void validateBale_WithNullEntryNumber_ShouldThrowInvalidEntityException() {
        // Given
        Bale baleWithNullEntryNo = FakeBale.getFakeBale(1).get(0).toBuilder()
            .entryNo(null)
            .build();

        // When & Then
        InvalidEntityException exception = assertThrows(InvalidEntityException.class,
            () -> validator.validateBale(baleWithNullEntryNo),
            "Bale with null entry number should throw InvalidEntityException");
        
        assertTrue(exception.getMessage().contains("entry number cannot be null"),
            "Exception message should indicate null entry number issue");
    }

    @Test
    void validateBale_WithValidNavReferenceAndEntryNumber_ShouldPassValidation() throws InvalidEntityException {
        // Given
        Account validAccount = Account.builder()
            .navReference("VALID_NAV_REF_123")
            .build();
        
        Bale validBale = FakeBale.getFakeBale(1).get(0).toBuilder()
            .account(validAccount)
            .entryNo(12345)
            .build();

        // When & Then
        assertDoesNotThrow(() -> validator.validateBale(validBale),
            "Bale with valid account and entry number should pass validation");
    }

    @Test
    void validateBale_MultipleValidationErrors_ShouldThrowForFirstError() {
        // Given - Bale with multiple validation issues (null account takes precedence)
        Bale baleWithMultipleIssues = FakeBale.getFakeBale(1).get(0).toBuilder()
            .account(null)
            .entryNo(null)
            .build();

        // When & Then
        InvalidEntityException exception = assertThrows(InvalidEntityException.class,
            () -> validator.validateBale(baleWithMultipleIssues),
            "Should throw exception for first validation error encountered");
        
        // Should fail on account validation first (before entry number validation)
        assertTrue(exception.getMessage().contains("account cannot be null"),
            "Should report first validation failure (account)");
    }

    @Test
    void validateBale_WithValidBaleFromFakeData_ShouldPassValidation() throws InvalidEntityException {
        // Given - Use multiple fake bales to ensure validation works with various data
        for (int i = 0; i < 5; i++) {
            Bale fakeBale = FakeBale.getFakeBale(1).get(0);
            
            // When & Then
            assertDoesNotThrow(() -> validator.validateBale(fakeBale),
                "Fake bale " + i + " should pass validation");
        }
    }
}
