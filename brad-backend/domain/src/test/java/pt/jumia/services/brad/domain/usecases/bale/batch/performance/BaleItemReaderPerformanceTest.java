package pt.jumia.services.brad.domain.usecases.bale.batch.performance;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.item.ExecutionContext;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.bale.batch.BaleItemReader;
import pt.jumia.services.brad.domain.usecases.executionlogs.CreateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Performance tests for BaleItemReader focusing on memory usage and throughput.
 * 
 * Tests verify:
 * - Memory-safe batch processing with large datasets
 * - Batch size enforcement prevents memory issues
 * - Cursor-based reading maintains consistent memory usage
 * - Processing throughput meets performance requirements
 */
@ExtendWith(MockitoExtension.class)
class BaleItemReaderPerformanceTest {

    @Mock
    private BaleRepository baleRepository;
    
    @Mock
    private BradBaleRepository bradBaleRepository;
    
    @Mock
    private ReadViewEntityUseCase readViewEntityUseCase;
    
    @Mock
    private CreateExecutionLogsUseCase createExecutionLogsUseCase;

    private BaleItemReader baleItemReader;
    private ExecutionContext executionContext;

    @BeforeEach
    void setUp() {
        baleItemReader = new BaleItemReader(baleRepository, bradBaleRepository, 
            readViewEntityUseCase, createExecutionLogsUseCase);
        executionContext = new ExecutionContext();
    }

    @Test
    void memoryUsage_WithLargeDataset_ShouldRemainConstant() throws Exception {
        // Given - Large dataset simulation
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        // Create a large batch but within safe limits
        List<Bale> largeBatch = FakeBale.getFakeBale(1000); // Exactly at MAX_MEMORY_BATCH_SIZE
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(bradBaleRepository.findLastBaleInBradOfCompanyId(anyString())).thenReturn(Optional.empty());
        when(baleRepository.fetchCompanyId(any(), anyBoolean())).thenReturn("TEST_COMPANY");
        when(baleRepository.findAllBatched(anyInt(), any(), anyBoolean(), any(), anyInt(), anyInt()))
            .thenReturn(largeBatch);
        when(createExecutionLogsUseCase.execute(any())).thenReturn(
            ExecutionLog.builder().build());

        // When - Process large dataset
        baleItemReader.open(executionContext);
        
        long memoryBefore = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        
        // Read multiple batches
        for (int i = 0; i < 5; i++) {
            Bale bale = baleItemReader.read();
            if (bale == null) break;
        }
        
        long memoryAfter = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        
        // Then - Memory usage should remain reasonable
        long memoryIncrease = memoryAfter - memoryBefore;
        long maxExpectedIncrease = 50 * 1024 * 1024; // 50MB should be sufficient
        
        assertTrue(memoryIncrease < maxExpectedIncrease, 
            String.format("Memory increase %d bytes should be less than %d bytes", 
                memoryIncrease, maxExpectedIncrease));
    }

    @Test
    void batchSizeEnforcement_WithOversizedRequest_ShouldLimitToSafeSize() throws Exception {
        // Given - Simulate repository returning oversized batch
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(bradBaleRepository.findLastBaleInBradOfCompanyId(anyString())).thenReturn(Optional.empty());
        when(baleRepository.fetchCompanyId(any(), anyBoolean())).thenReturn("TEST_COMPANY");
        when(createExecutionLogsUseCase.execute(any())).thenReturn(
            ExecutionLog.builder().build());

        // When - Open reader (this triggers batch loading)
        baleItemReader.open(executionContext);

        // Then - Should request safe batch size (1000 max)
        verify(baleRepository).findAllBatched(anyInt(), any(), anyBoolean(), any(), anyInt(), eq(1000));
    }

    @Test
    void processingThroughput_WithMultipleBatches_ShouldMeetPerformanceRequirements() throws Exception {
        // Given
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        List<Bale> batch = FakeBale.getFakeBale(500); // Normal batch size
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(bradBaleRepository.findLastBaleInBradOfCompanyId(anyString())).thenReturn(Optional.empty());
        when(baleRepository.fetchCompanyId(any(), anyBoolean())).thenReturn("TEST_COMPANY");
        when(baleRepository.findAllBatched(anyInt(), any(), anyBoolean(), any(), anyInt(), anyInt()))
            .thenReturn(batch)
            .thenReturn(Arrays.asList()); // Empty list to end processing
        when(createExecutionLogsUseCase.execute(any())).thenReturn(
            ExecutionLog.builder().build());

        // When - Process multiple batches and measure throughput
        baleItemReader.open(executionContext);
        
        long startTime = System.currentTimeMillis();
        int itemsProcessed = 0;
        
        Bale bale;
        while ((bale = baleItemReader.read()) != null) {
            itemsProcessed++;
        }
        
        long endTime = System.currentTimeMillis();
        long processingTime = endTime - startTime;
        
        // Then - Throughput should meet minimum requirements
        assertTrue(itemsProcessed > 0, "Should process at least some items");
        assertTrue(processingTime < 10000, "Should complete within 10 seconds"); // Reasonable for 500 items
        
        double throughput = (double) itemsProcessed / processingTime * 1000; // items per second
        assertTrue(throughput > 10, "Should process at least 10 items per second");
    }

    @Test
    void cursorBasedReading_WithMultipleViewEntities_ShouldMaintainConsistentMemory() throws Exception {
        // Given - Multiple view entities
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0),
            FakeViewEntity.getFakeViewEntity(1).get(0),
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        List<Bale> batch = FakeBale.getFakeBale(100);
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(bradBaleRepository.findLastBaleInBradOfCompanyId(anyString())).thenReturn(Optional.empty());
        when(baleRepository.fetchCompanyId(any(), anyBoolean())).thenReturn("TEST_COMPANY");
        when(baleRepository.findAllBatched(anyInt(), any(), anyBoolean(), any(), anyInt(), anyInt()))
            .thenReturn(batch)
            .thenReturn(Arrays.asList()); // Empty list for each view entity
        when(createExecutionLogsUseCase.execute(any())).thenReturn(
            ExecutionLog.builder().build());

        // When - Process all view entities
        baleItemReader.open(executionContext);
        
        long initialMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
        
        // Process items from all view entities
        int totalItems = 0;
        Bale bale;
        while ((bale = baleItemReader.read()) != null) {
            totalItems++;
            
            // Check memory periodically
            if (totalItems % 50 == 0) {
                long currentMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
                long memoryIncrease = currentMemory - initialMemory;
                assertTrue(memoryIncrease < 100 * 1024 * 1024, // 100MB limit
                    "Memory should not increase significantly during processing");
            }
        }
        
        // Then - Should have processed items from first view entity
        assertTrue(totalItems > 0, "Should process items from view entities");
    }
}