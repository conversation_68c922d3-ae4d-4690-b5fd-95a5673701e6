package pt.jumia.services.brad.domain.usecases.bale.enrichment;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.usecases.fxrates.brad.ReadBradFxRateUseCase;

import java.time.LocalDate;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BaleFxRateEnricherTest {

    @Mock
    private ReadBradFxRateUseCase readBradFxRateUseCase;

    private BaleFxRateEnricher baleFxRateEnricher;

    @BeforeEach
    void setUp() {
        baleFxRateEnricher = new BaleFxRateEnricher(readBradFxRateUseCase);
    }

    @Test
    void enrichWithFxRates_WithValidBaleAndCurrency_ShouldReturnFxRates() throws Exception {
        // Given
        Bale bale = FakeBale.getFakeBale(1).get(0);
        Currency currency = FakeCurrencies.USD;
        LocalDate postingDate = LocalDate.now();
        
        // When
        Set<FxRate> result = baleFxRateEnricher.enrichWithFxRates(bale, currency);

        // Then
        assertNotNull(result, "FX rates should not be null");
        verify(readBradFxRateUseCase).addFxRate(eq(result), eq(currency.getCode()), eq("USD"), eq(postingDate));
    }

    @Test
    void enrichWithFxRates_WithCountryCurrency_ShouldAddCountryFxRate() throws Exception {
        // Given
        Currency countryCurrency = FakeCurrencies.NGN;
        countryCurrency = countryCurrency.toBuilder().code("EUR").build();
        
        Country country = Country.builder()
                .currency(countryCurrency)
                .build();
                
        Account account = Account.builder()
                .country(country)
                .build();
                
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder()
                .account(account)
                .build();
                
        Currency transactionCurrency = FakeCurrencies.EUR;
        LocalDate postingDate = bale.getPostingDate();

        // When
        Set<FxRate> result = baleFxRateEnricher.enrichWithFxRates(bale, transactionCurrency);

        // Then
        assertNotNull(result, "FX rates should not be null");
        verify(readBradFxRateUseCase).addFxRate(eq(result), eq(transactionCurrency.getCode()), eq("USD"), eq(postingDate));
        verify(readBradFxRateUseCase).addFxRate(eq(result), eq(transactionCurrency.getCode()), eq("EUR"), eq(postingDate));
    }

    @Test
    void enrichWithFxRates_WithNullAccount_ShouldOnlyAddUsdRate() throws Exception {
        // Given
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder()
                .account(null)
                .build();
        Currency currency = FakeCurrencies.USD;
        LocalDate postingDate = bale.getPostingDate();

        // When
        Set<FxRate> result = baleFxRateEnricher.enrichWithFxRates(bale, currency);

        // Then
        assertNotNull(result, "FX rates should not be null");
        verify(readBradFxRateUseCase).addFxRate(eq(result), eq(currency.getCode()), eq("USD"), eq(postingDate));
        verify(readBradFxRateUseCase, never()).addFxRate(any(), any(), eq("EUR"), any());
    }

    @Test
    void enrichWithFxRates_WithFxRateException_ShouldThrowFxRateUnavailableException() throws Exception {
        // Given
        Bale bale = FakeBale.getFakeBale(1).get(0);
        Currency currency = FakeCurrencies.USD;
        
        doThrow(new RuntimeException("FX rate service unavailable"))
                .when(readBradFxRateUseCase).addFxRate(any(), any(), any(), any());

        // When & Then
        FxRateUnavailableException exception = assertThrows(FxRateUnavailableException.class,
                () -> baleFxRateEnricher.enrichWithFxRates(bale, currency));
        
        assertTrue(exception.getMessage().contains("Failed to enrich bale with FX rates"));
    }

    @Test
    void addFxRates_WithValidBale_ShouldReturnEnrichedBale() throws Exception {
        // Given
        Currency transactionCurrency = FakeCurrencies.EUR;
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder()
                .transactionCurrency(transactionCurrency)
                .build();

        // When
        Bale result = baleFxRateEnricher.addFxRates(bale);

        // Then
        assertNotNull(result, "Result should not be null");
        assertNotNull(result.getFxRates(), "FX rates should be set");
        assertEquals(bale.getEntryNo(), result.getEntryNo(), "Entry number should be preserved");
    }

    @Test
    void addFxRates_WithNullBale_ShouldThrowIllegalArgumentException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> baleFxRateEnricher.addFxRates(null));
        
        assertEquals("Bale cannot be null", exception.getMessage());
    }

    @Test
    void addFxRates_WithNullAccount_ShouldThrowIllegalArgumentException() {
        // Given
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder()
                .account(null)
                .build();

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> baleFxRateEnricher.addFxRates(bale));
        
        assertEquals("Bale account cannot be null", exception.getMessage());
    }

    @Test
    void addFxRates_WithNullTransactionCurrency_ShouldThrowIllegalArgumentException() {
        // Given
        Bale bale = FakeBale.getFakeBale(1).get(0).toBuilder()
                .transactionCurrency(null)
                .build();

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> baleFxRateEnricher.addFxRates(bale));
        
        assertEquals("Bale transaction currency cannot be null", exception.getMessage());
    }
}