package pt.jumia.services.brad.domain.usecases.bale.batch;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.item.ExecutionContext;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.executionlogs.CreateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Large dataset processing integration tests for BaleItemReader.
 * 
 * These tests verify that the BaleItemReader can handle large datasets
 * efficiently without memory issues, performance degradation, or data loss.
 */
@ExtendWith(MockitoExtension.class)
class BaleItemReaderLargeDatasetIntegrationTest {

    @Mock
    private BaleRepository baleRepository;
    
    @Mock
    private BradBaleRepository bradBaleRepository;
    
    @Mock
    private ReadViewEntityUseCase readViewEntityUseCase;
    
    @Mock
    private CreateExecutionLogsUseCase createExecutionLogsUseCase;

    private BaleItemReader reader;
    private ExecutionContext executionContext;

    @BeforeEach
    void setUp() {
        reader = new BaleItemReader(baleRepository, bradBaleRepository, 
            readViewEntityUseCase, createExecutionLogsUseCase);
        executionContext = new ExecutionContext();
    }

    @Test
    void processLargeDataset_With10000Records_ShouldProcessAllWithoutMemoryIssues() throws Exception {
        // Given - Large dataset with 10,000 records
        int totalRecords = 10000;
        int batchSize = 500;
        int expectedBatches = totalRecords / batchSize;
        
        ViewEntity viewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);
        List<ViewEntity> viewEntities = List.of(viewEntity);
        
        ExecutionLog executionLog = ExecutionLog.builder()
            .logType(ExecutionLog.ExecutionLogType.BALE)
            .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
            .build();
        
        // Mock view entity setup
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(createExecutionLogsUseCase.execute(any(ExecutionLog.class))).thenReturn(executionLog);
        
        // Mock repository responses for all batches
        for (int i = 0; i < expectedBatches; i++) {
            int offset = i * batchSize;
            List<Bale> batch = FakeBale.getFakeBale(batchSize);
            when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
                eq(executionLog), eq(offset), eq(batchSize))).thenReturn(batch);
        }
        
        // Mock final empty batch to end processing
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(totalRecords), eq(batchSize))).thenReturn(List.of());
        
        reader.open(executionContext);
        
        // When - Process all records
        List<Bale> processedBales = new ArrayList<>();
        Bale bale;
        long startTime = System.currentTimeMillis();
        
        while ((bale = reader.read()) != null) {
            processedBales.add(bale);
        }
        
        long processingTime = System.currentTimeMillis() - startTime;
        
        // Then - Verify all records processed
        assertEquals(totalRecords, processedBales.size(), 
            "Should process all " + totalRecords + " records");
        
        // Verify performance (should process 10K records in reasonable time)
        assertTrue(processingTime < 30000, 
            "Processing 10K records should take less than 30 seconds, took: " + processingTime + "ms");
        
        // Verify memory efficiency - no accumulation of large batches
        verify(baleRepository, times(expectedBatches + 1)).findAllBatched(
            anyInt(), eq(viewEntity), eq(false), eq(executionLog), anyInt(), eq(batchSize));
        
        reader.close();
    }

    @Test
    void processLargeDataset_WithMemoryPressure_ShouldMaintainStableMemoryUsage() throws Exception {
        // Given - Simulate memory pressure with very large fake records
        int totalRecords = 5000;
        int batchSize = 500;
        
        ViewEntity viewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);
        List<ViewEntity> viewEntities = List.of(viewEntity);
        
        ExecutionLog executionLog = ExecutionLog.builder()
            .logType(ExecutionLog.ExecutionLogType.BALE)
            .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
            .build();
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(createExecutionLogsUseCase.execute(any(ExecutionLog.class))).thenReturn(executionLog);
        
        // Mock repository to return batches
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), anyInt(), eq(batchSize)))
            .thenReturn(FakeBale.getFakeBale(batchSize))
            .thenReturn(FakeBale.getFakeBale(batchSize))
            .thenReturn(FakeBale.getFakeBale(batchSize))
            .thenReturn(FakeBale.getFakeBale(batchSize))
            .thenReturn(FakeBale.getFakeBale(batchSize))
            .thenReturn(FakeBale.getFakeBale(batchSize))
            .thenReturn(FakeBale.getFakeBale(batchSize))
            .thenReturn(FakeBale.getFakeBale(batchSize))
            .thenReturn(FakeBale.getFakeBale(batchSize))
            .thenReturn(FakeBale.getFakeBale(batchSize))
            .thenReturn(List.of()); // End processing
        
        reader.open(executionContext);
        
        // When - Process with memory monitoring
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemoryUsed = initialMemory;
        
        int recordsProcessed = 0;
        Bale bale;
        
        while ((bale = reader.read()) != null) {
            recordsProcessed++;
            
            // Monitor memory usage every 100 records
            if (recordsProcessed % 100 == 0) {
                long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                maxMemoryUsed = Math.max(maxMemoryUsed, currentMemory);
            }
        }
        
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // Then - Verify memory stability
        assertEquals(totalRecords, recordsProcessed, "Should process all records");
        
        // Memory should not continuously grow (allow for 50MB variation)
        long memoryGrowth = finalMemory - initialMemory;
        assertTrue(memoryGrowth < 50_000_000, 
            "Memory growth should be minimal, was: " + memoryGrowth + " bytes");
        
        reader.close();
    }

    @Test
    void processLargeDataset_WithConcurrentAccess_ShouldMaintainConsistency() throws Exception {
        // Given - Setup for concurrent processing
        int recordsPerReader = 1000;
        int numberOfReaders = 5;
        
        ViewEntity viewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);
        List<ViewEntity> viewEntities = List.of(viewEntity);
        
        ExecutionLog executionLog = ExecutionLog.builder()
            .logType(ExecutionLog.ExecutionLogType.BALE)
            .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
            .build();
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(createExecutionLogsUseCase.execute(any(ExecutionLog.class))).thenReturn(executionLog);
        
        // Mock repository responses
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), anyInt(), anyInt()))
            .thenReturn(FakeBale.getFakeBale(500))
            .thenReturn(FakeBale.getFakeBale(500))
            .thenReturn(List.of()); // End processing
        
        ExecutorService executor = Executors.newFixedThreadPool(numberOfReaders);
        List<Future<Integer>> futures = new ArrayList<>();
        
        // When - Process concurrently with multiple readers
        for (int i = 0; i < numberOfReaders; i++) {
            final int readerIndex = i;
            Future<Integer> future = executor.submit(() -> {
                BaleItemReader concurrentReader = new BaleItemReader(baleRepository, 
                    bradBaleRepository, readViewEntityUseCase, createExecutionLogsUseCase);
                ExecutionContext readerContext = new ExecutionContext();
                readerContext.putString("readerId", "reader-" + readerIndex);
                
                try {
                    concurrentReader.open(readerContext);
                    
                    int count = 0;
                    Bale bale;
                    while ((bale = concurrentReader.read()) != null) {
                        count++;
                        if (count >= recordsPerReader) break; // Limit for test
                    }
                    
                    concurrentReader.close();
                    return count;
                } catch (Exception e) {
                    throw new RuntimeException("Concurrent processing failed", e);
                }
            });
            futures.add(future);
        }
        
        // Then - Verify all readers processed successfully
        int totalProcessed = 0;
        for (Future<Integer> future : futures) {
            int processed = future.get(30, TimeUnit.SECONDS);
            totalProcessed += processed;
            assertTrue(processed > 0, "Each reader should process at least some records");
        }
        
        executor.shutdown();
        assertTrue(executor.awaitTermination(10, TimeUnit.SECONDS), 
            "Executor should terminate within 10 seconds");
        
        assertTrue(totalProcessed > 0, "Should process records across all readers");
        
        // Verify repository was called from multiple threads
        verify(baleRepository, atLeast(numberOfReaders)).findAllBatched(
            anyInt(), eq(viewEntity), eq(false), eq(executionLog), anyInt(), anyInt());
    }

    @Test
    void processLargeDataset_WithRestartScenario_ShouldResumeFromCorrectPosition() throws Exception {
        // Given - Large dataset with restart scenario
        int totalRecords = 2000;
        int batchSize = 500;
        int restartPoint = 750; // Restart after processing 1.5 batches
        
        ViewEntity viewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);
        List<ViewEntity> viewEntities = List.of(viewEntity);
        
        ExecutionLog executionLog = ExecutionLog.builder()
            .logType(ExecutionLog.ExecutionLogType.BALE)
            .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
            .build();
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(createExecutionLogsUseCase.execute(any(ExecutionLog.class))).thenReturn(executionLog);
        
        // Mock repository responses for initial processing
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(0), eq(batchSize))).thenReturn(FakeBale.getFakeBale(batchSize));
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(500), eq(batchSize))).thenReturn(FakeBale.getFakeBale(batchSize));
        
        // Mock repository responses for restart processing
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(restartPoint), eq(batchSize))).thenReturn(FakeBale.getFakeBale(batchSize));
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(1250), eq(batchSize))).thenReturn(FakeBale.getFakeBale(batchSize));
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(1750), eq(batchSize))).thenReturn(FakeBale.getFakeBale(250));
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(2000), eq(batchSize))).thenReturn(List.of());
        
        // When - Initial processing (partial)
        reader.open(executionContext);
        
        int initialProcessed = 0;
        Bale bale;
        while ((bale = reader.read()) != null && initialProcessed < restartPoint) {
            initialProcessed++;
        }
        
        // Simulate restart with preserved execution context
        reader.update(executionContext);
        reader.close();
        
        // Create new reader instance (simulating restart)
        BaleItemReader restartReader = new BaleItemReader(baleRepository, 
            bradBaleRepository, readViewEntityUseCase, createExecutionLogsUseCase);
        
        // Restore state from execution context
        executionContext.putInt("currentBaleOffset", restartPoint);
        executionContext.putInt("viewEntityIndex", 0);
        
        restartReader.open(executionContext);
        
        int restartProcessed = 0;
        while ((bale = restartReader.read()) != null) {
            restartProcessed++;
        }
        
        restartReader.close();
        
        // Then - Verify restart behavior
        assertEquals(restartPoint, initialProcessed, 
            "Should have processed " + restartPoint + " records initially");
        
        int expectedRemainingRecords = totalRecords - restartPoint;
        assertEquals(expectedRemainingRecords, restartProcessed, 
            "Should process remaining " + expectedRemainingRecords + " records after restart");
        
        // Verify repository was called with correct offset for restart
        verify(baleRepository).findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(restartPoint), eq(batchSize));
    }

    @Test
    void processLargeDataset_WithMultipleViewEntities_ShouldProcessAllEntitiesEfficiently() throws Exception {
        // Given - Multiple view entities with large datasets
        int recordsPerEntity = 1000;
        int numberOfEntities = 10;
        
        List<ViewEntity> viewEntities = FakeViewEntity.getFakeViewEntity(numberOfEntities);
        
        ExecutionLog executionLog = ExecutionLog.builder()
            .logType(ExecutionLog.ExecutionLogType.BALE)
            .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
            .build();
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(createExecutionLogsUseCase.execute(any(ExecutionLog.class))).thenReturn(executionLog);
        
        // Mock repository responses for each view entity
        for (ViewEntity viewEntity : viewEntities) {
            when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
                eq(executionLog), eq(0), eq(500))).thenReturn(FakeBale.getFakeBale(500));
            when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
                eq(executionLog), eq(500), eq(500))).thenReturn(FakeBale.getFakeBale(500));
            when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
                eq(executionLog), eq(1000), eq(500))).thenReturn(List.of());
        }
        
        reader.open(executionContext);
        
        // When - Process all entities
        long startTime = System.currentTimeMillis();
        int totalProcessed = 0;
        Bale bale;
        
        while ((bale = reader.read()) != null) {
            totalProcessed++;
        }
        
        long processingTime = System.currentTimeMillis() - startTime;
        
        // Then - Verify all entities processed
        int expectedTotal = numberOfEntities * recordsPerEntity;
        assertEquals(expectedTotal, totalProcessed, 
            "Should process all " + expectedTotal + " records across " + numberOfEntities + " entities");
        
        // Verify performance scales reasonably
        assertTrue(processingTime < 60000, 
            "Processing " + expectedTotal + " records across " + numberOfEntities + 
            " entities should take less than 60 seconds, took: " + processingTime + "ms");
        
        // Verify repository was called for each view entity
        for (ViewEntity viewEntity : viewEntities) {
            verify(baleRepository, atLeast(1)).findAllBatched(
                anyInt(), eq(viewEntity), eq(false), eq(executionLog), anyInt(), anyInt());
        }
        
        reader.close();
    }
}