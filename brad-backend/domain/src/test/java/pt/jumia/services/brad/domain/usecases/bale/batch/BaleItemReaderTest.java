package pt.jumia.services.brad.domain.usecases.bale.batch;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.item.ExecutionContext;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.executionlogs.CreateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for BaleItemReader focusing on critical audit fixes.
 * 
 * Tests cover:
 * - Memory management with cursor-based reading
 * - ItemStream implementation for restart capability
 * - ExecutionContext state persistence and restoration
 * - Thread safety with stateless design
 * - Transaction boundary separation
 * - Batch size limits and memory safeguards
 */
@ExtendWith(MockitoExtension.class)
class BaleItemReaderTest {

    @Mock
    private BaleRepository baleRepository;
    
    @Mock
    private BradBaleRepository bradBaleRepository;
    
    @Mock
    private ReadViewEntityUseCase readViewEntityUseCase;
    
    @Mock
    private CreateExecutionLogsUseCase createExecutionLogsUseCase;

    private BaleItemReader reader;
    private ExecutionContext executionContext;

    @BeforeEach
    void setUp() {
        reader = new BaleItemReader(baleRepository, bradBaleRepository, 
            readViewEntityUseCase, createExecutionLogsUseCase);
        executionContext = new ExecutionContext();
    }

    @Test
    void constructor_ShouldCreateReaderSuccessfully() {
        // Then
        assertNotNull(reader, "BaleItemReader should be created successfully");
    }

    @Test
    void open_WithEmptyExecutionContext_ShouldInitializeFromBeginning() throws Exception {
        // Given
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0),
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);

        // When
        reader.open(executionContext);

        // Then
        assertEquals(0, executionContext.getInt("viewEntityIndex", -1), 
            "Should initialize viewEntityIndex to 0");
        assertEquals(0, executionContext.getInt("currentBaleOffset", -1), 
            "Should initialize currentBaleOffset to 0");
        assertNull(executionContext.getString("currentCompanyId", null), 
            "Should initialize currentCompanyId to null");
        assertNull(executionContext.get("currentEntryNo"), 
            "Should initialize currentEntryNo to null");
    }

    @Test
    void open_WithExistingExecutionContext_ShouldRestoreState() throws Exception {
        // Given
        executionContext.putInt("viewEntityIndex", 2);
        executionContext.putInt("currentBaleOffset", 150);
        executionContext.putString("currentCompanyId", "TEST_COMPANY");
        executionContext.putInt("currentEntryNo", 12345);

        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(3).toArray(new ViewEntity[0])
        );
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);

        // When
        reader.open(executionContext);

        // Then
        assertEquals(2, executionContext.getInt("viewEntityIndex", -1), 
            "Should restore viewEntityIndex");
        assertEquals(150, executionContext.getInt("currentBaleOffset", -1), 
            "Should restore currentBaleOffset");
        assertEquals("TEST_COMPANY", executionContext.getString("currentCompanyId", null), 
            "Should restore currentCompanyId");
        assertEquals(12345, executionContext.get("currentEntryNo"), 
            "Should restore currentEntryNo");
    }

    @Test
    void update_ShouldPersistCurrentState() throws Exception {
        // Given
        reader.open(executionContext);
        
        // Simulate state changes during processing
        executionContext.putInt("viewEntityIndex", 1);
        executionContext.putInt("currentBaleOffset", 75);
        executionContext.putString("currentCompanyId", "UPDATED_COMPANY");
        executionContext.putInt("currentEntryNo", 67890);

        // When
        reader.update(executionContext);

        // Then
        assertEquals(1, executionContext.getInt("viewEntityIndex", -1), 
            "Should persist updated viewEntityIndex");
        assertEquals(75, executionContext.getInt("currentBaleOffset", -1), 
            "Should persist updated currentBaleOffset");
        assertEquals("UPDATED_COMPANY", executionContext.getString("currentCompanyId", null), 
            "Should persist updated currentCompanyId");
        assertEquals(67890, executionContext.get("currentEntryNo"), 
            "Should persist updated currentEntryNo");
    }

    @Test
    void close_ShouldCleanupResources() throws Exception {
        // Given
        reader.open(executionContext);

        // When
        reader.close();

        // Then - Should not throw any exceptions and cleanup gracefully
        assertDoesNotThrow(() -> reader.close(), "close should not throw exceptions");
    }

    @Test
    void read_WithNoBales_ShouldReturnNull() throws Exception {
        // Given
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE))
            .thenReturn(Collections.emptyList());
        
        reader.open(executionContext);

        // When
        Bale result = reader.read();

        // Then
        assertNull(result, "Should return null when no bales available");
    }

    @Test
    void read_WithBalesAvailable_ShouldReturnBalesSequentially() throws Exception {
        // Given
        ViewEntity viewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);
        List<ViewEntity> viewEntities = Arrays.asList(viewEntity);
        List<Bale> bales = FakeBale.getFakeBale(3);
        
        ExecutionLog executionLog = ExecutionLog.builder()
            .logType(ExecutionLog.ExecutionLogType.BALE)
            .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
            .build();

        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(createExecutionLogsUseCase.execute(any(ExecutionLog.class))).thenReturn(executionLog);
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(0), eq(500))).thenReturn(bales);
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(3), eq(500))).thenReturn(Collections.emptyList());

        reader.open(executionContext);

        // When & Then
        Bale firstBale = reader.read();
        assertNotNull(firstBale, "First read should return a bale");
        assertEquals(bales.get(0), firstBale, "Should return first bale");

        Bale secondBale = reader.read();
        assertNotNull(secondBale, "Second read should return a bale");
        assertEquals(bales.get(1), secondBale, "Should return second bale");

        Bale thirdBale = reader.read();
        assertNotNull(thirdBale, "Third read should return a bale");
        assertEquals(bales.get(2), thirdBale, "Should return third bale");

        Bale fourthBale = reader.read();
        assertNull(fourthBale, "Fourth read should return null (no more bales)");
    }

    @Test
    void read_WithMemorySafeguard_ShouldTruncateLargeBatches() throws Exception {
        // Given
        ViewEntity viewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);
        List<ViewEntity> viewEntities = Arrays.asList(viewEntity);
        
        // Create a batch larger than MAX_MEMORY_BATCH_SIZE (1000)
        List<Bale> largeBatch = FakeBale.getFakeBale(1200);
        
        ExecutionLog executionLog = ExecutionLog.builder()
            .logType(ExecutionLog.ExecutionLogType.BALE)
            .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
            .build();

        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(createExecutionLogsUseCase.execute(any(ExecutionLog.class))).thenReturn(executionLog);
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(0), eq(500))).thenReturn(largeBatch);

        reader.open(executionContext);

        // When
        int readCount = 0;
        while (reader.read() != null && readCount < 1100) { // Safety limit
            readCount++;
        }

        // Then
        assertTrue(readCount <= 1000, 
            "Should not read more than MAX_MEMORY_BATCH_SIZE (1000) items due to memory safeguard");
    }

    @Test
    void read_ShouldUpdateExecutionContextState() throws Exception {
        // Given
        ViewEntity viewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);
        List<ViewEntity> viewEntities = Arrays.asList(viewEntity);
        List<Bale> bales = FakeBale.getFakeBale(2);
        
        ExecutionLog executionLog = ExecutionLog.builder()
            .logType(ExecutionLog.ExecutionLogType.BALE)
            .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
            .build();

        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(createExecutionLogsUseCase.execute(any(ExecutionLog.class))).thenReturn(executionLog);
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(0), eq(500))).thenReturn(bales);

        reader.open(executionContext);

        // When
        reader.read(); // Read first bale
        reader.update(executionContext);

        // Then
        assertEquals(0, executionContext.getInt("viewEntityIndex", -1), 
            "Should maintain current view entity index");
        assertEquals(1, executionContext.getInt("currentBaleOffset", -1), 
            "Should update current bale offset");
    }

    @Test
    void read_WithRepositoryException_ShouldPropagateException() throws Exception {
        // Given
        ViewEntity viewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);
        List<ViewEntity> viewEntities = Arrays.asList(viewEntity);
        
        ExecutionLog executionLog = ExecutionLog.builder()
            .logType(ExecutionLog.ExecutionLogType.BALE)
            .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
            .build();

        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(createExecutionLogsUseCase.execute(any(ExecutionLog.class))).thenReturn(executionLog);
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity), eq(false), 
            eq(executionLog), eq(0), eq(500)))
            .thenThrow(new RuntimeException("Database connection failed"));

        reader.open(executionContext);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> reader.read(),
            "Should propagate repository exceptions");
        assertTrue(exception.getMessage().contains("Failed to load bales batch"),
            "Should wrap original exception with descriptive message");
    }

    @Test
    void threadSafety_MultipleReadersWithSeparateExecutionContexts_ShouldNotInterfere() throws Exception {
        // Given
        BaleItemReader reader1 = new BaleItemReader(baleRepository, bradBaleRepository, 
            readViewEntityUseCase, createExecutionLogsUseCase);
        BaleItemReader reader2 = new BaleItemReader(baleRepository, bradBaleRepository, 
            readViewEntityUseCase, createExecutionLogsUseCase);
        
        ExecutionContext context1 = new ExecutionContext();
        ExecutionContext context2 = new ExecutionContext();
        
        ViewEntity viewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);
        List<ViewEntity> viewEntities = Arrays.asList(viewEntity);
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);

        // When
        reader1.open(context1);
        reader2.open(context2);
        
        // Simulate different states
        context1.putInt("viewEntityIndex", 1);
        context1.putInt("currentBaleOffset", 50);
        
        context2.putInt("viewEntityIndex", 2);
        context2.putInt("currentBaleOffset", 100);
        
        reader1.update(context1);
        reader2.update(context2);

        // Then
        assertEquals(1, context1.getInt("viewEntityIndex", -1), 
            "Reader1 state should remain independent");
        assertEquals(50, context1.getInt("currentBaleOffset", -1), 
            "Reader1 offset should remain independent");
        
        assertEquals(2, context2.getInt("viewEntityIndex", -1), 
            "Reader2 state should remain independent");
        assertEquals(100, context2.getInt("currentBaleOffset", -1), 
            "Reader2 offset should remain independent");
    }

    @Test
    void restartScenario_ShouldResumeFromLastPersistedState() throws Exception {
        // Given - Simulate a job that was interrupted
        ViewEntity viewEntity1 = FakeViewEntity.getFakeViewEntity(1).get(0);
        ViewEntity viewEntity2 = FakeViewEntity.getFakeViewEntity(1).get(0);
        List<ViewEntity> viewEntities = Arrays.asList(viewEntity1, viewEntity2);
        
        // Simulate restart state - was processing second view entity at offset 25
        executionContext.putInt("viewEntityIndex", 1);
        executionContext.putInt("currentBaleOffset", 25);
        executionContext.putString("currentCompanyId", "RESTART_COMPANY");
        executionContext.putInt("currentEntryNo", 54321);
        
        List<Bale> resumeBales = FakeBale.getFakeBale(3);
        ExecutionLog executionLog = ExecutionLog.builder()
            .logType(ExecutionLog.ExecutionLogType.BALE)
            .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
            .build();

        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(createExecutionLogsUseCase.execute(any(ExecutionLog.class))).thenReturn(executionLog);
        when(baleRepository.findAllBatched(anyInt(), eq(viewEntity2), eq(false), 
            eq(executionLog), eq(25), eq(500))).thenReturn(resumeBales);

        // When
        reader.open(executionContext);
        Bale resumedBale = reader.read();

        // Then
        assertNotNull(resumedBale, "Should resume reading from persisted state");
        assertEquals(resumeBales.get(0), resumedBale, "Should return first bale from resume point");
        
        // Verify it resumed from correct position
        verify(baleRepository).findAllBatched(anyInt(), eq(viewEntity2), eq(false), 
            eq(executionLog), eq(25), eq(500));
    }
}
