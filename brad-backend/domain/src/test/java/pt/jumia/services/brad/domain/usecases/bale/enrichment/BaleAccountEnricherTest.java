package pt.jumia.services.brad.domain.usecases.bale.enrichment;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for BaleAccountEnricher focusing on account fetching and exception handling.
 * 
 * Tests cover:
 * - Account fetching logic and success scenarios
 * - Proper use of NotFoundException (existing domain exception)
 * - Error handling for missing accounts
 * - NAV reference validation and processing
 */
@ExtendWith(MockitoExtension.class)
class BaleAccountEnricherTest {

    @Mock
    private ReadAccountsUseCase readAccountsUseCase;

    private BaleAccountEnricher enricher;

    @BeforeEach
    void setUp() {
        enricher = new BaleAccountEnricher(readAccountsUseCase);
    }

    @Test
    void fetchAccount_WithValidNavReference_ShouldReturnAccount() throws NotFoundException {
        // Given
        Bale bale = FakeBale.getFakeBale(1).get(0);
        Account expectedAccount = FakeAccounts.getFakeAccounts(1, Account.Type.BANK_ACCOUNT).get(0);
        String navReference = bale.getAccount().getNavReference();
        
        when(readAccountsUseCase.executeByNavReference(navReference)).thenReturn(expectedAccount);

        // When
        Account result = enricher.fetchAccount(bale);

        // Then
        assertNotNull(result, "Should return an account");
        assertEquals(expectedAccount, result, "Should return the expected account");
        verify(readAccountsUseCase).executeByNavReference(navReference);
    }

    @Test
    void fetchAccount_WithNonExistentNavReference_ShouldThrowNotFoundException() {
        // Given
        Bale bale = FakeBale.getFakeBale(1).get(0);
        String navReference = bale.getAccount().getNavReference();
        
        when(readAccountsUseCase.executeByNavReference(navReference)).thenReturn(null);

        // When & Then
        NotFoundException exception = assertThrows(NotFoundException.class,
            () -> enricher.fetchAccount(bale),
            "Should throw NotFoundException when account is not found");
        
        assertTrue(exception.getMessage().contains("Account"),
            "Exception message should mention Account");
        assertTrue(exception.getMessage().contains(navReference),
            "Exception message should include the NAV reference");
        verify(readAccountsUseCase).executeByNavReference(navReference);
    }

    @Test
    void fetchAccount_WithUseCaseException_ShouldThrowNotFoundException() {
        // Given
        Bale bale = FakeBale.getFakeBale(1).get(0);
        String navReference = bale.getAccount().getNavReference();
        
        when(readAccountsUseCase.executeByNavReference(navReference))
            .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then
        NotFoundException exception = assertThrows(NotFoundException.class,
            () -> enricher.fetchAccount(bale),
            "Should wrap use case exceptions in NotFoundException");
        
        assertTrue(exception.getMessage().contains("Account"),
            "Exception message should mention Account");
        assertTrue(exception.getMessage().contains(navReference),
            "Exception message should include the NAV reference");
        verify(readAccountsUseCase).executeByNavReference(navReference);
    }

    @Test
    void fetchAccount_WithNullBaleAccount_ShouldThrowNullPointerException() {
        // Given
        Bale baleWithNullAccount = FakeBale.getFakeBale(1).get(0).toBuilder()
            .account(null)
            .build();

        // When & Then
        assertThrows(NullPointerException.class,
            () -> enricher.fetchAccount(baleWithNullAccount),
            "Should throw NullPointerException when bale account is null");
        
        verifyNoInteractions(readAccountsUseCase);
    }

    @Test
    void fetchAccount_WithNullNavReference_ShouldThrowNullPointerException() {
        // Given
        Account accountWithNullNavRef = Account.builder()
            .navReference(null)
            .build();
        
        Bale baleWithNullNavRef = FakeBale.getFakeBale(1).get(0).toBuilder()
            .account(accountWithNullNavRef)
            .build();

        // When & Then
        assertThrows(NullPointerException.class,
            () -> enricher.fetchAccount(baleWithNullNavRef),
            "Should throw NullPointerException when NAV reference is null");
        
        verifyNoInteractions(readAccountsUseCase);
    }

    @Test
    void fetchAccount_WithDifferentNavReferences_ShouldCallUseCaseWithCorrectParameters() throws NotFoundException {
        // Given
        String navRef1 = "NAV_REF_001";
        String navRef2 = "NAV_REF_002";
        String navRef3 = "NAV_REF_003";
        
        Account account1 = Account.builder().navReference(navRef1).build();
        Account account2 = Account.builder().navReference(navRef2).build();
        Account account3 = Account.builder().navReference(navRef3).build();
        
        Bale bale1 = FakeBale.getFakeBale(1).get(0).toBuilder().account(account1).build();
        Bale bale2 = FakeBale.getFakeBale(1).get(0).toBuilder().account(account2).build();
        Bale bale3 = FakeBale.getFakeBale(1).get(0).toBuilder().account(account3).build();
        
        Account expectedAccount1 = FakeAccounts.getFakeAccounts(1, Account.Type.BANK_ACCOUNT).get(0);
        Account expectedAccount2 = FakeAccounts.getFakeAccounts(1, Account.Type.BANK_ACCOUNT).get(0);
        Account expectedAccount3 = FakeAccounts.getFakeAccounts(1, Account.Type.BANK_ACCOUNT).get(0);
        
        when(readAccountsUseCase.executeByNavReference(navRef1)).thenReturn(expectedAccount1);
        when(readAccountsUseCase.executeByNavReference(navRef2)).thenReturn(expectedAccount2);
        when(readAccountsUseCase.executeByNavReference(navRef3)).thenReturn(expectedAccount3);

        // When
        Account result1 = enricher.fetchAccount(bale1);
        Account result2 = enricher.fetchAccount(bale2);
        Account result3 = enricher.fetchAccount(bale3);

        // Then
        assertEquals(expectedAccount1, result1, "Should return correct account for first NAV reference");
        assertEquals(expectedAccount2, result2, "Should return correct account for second NAV reference");
        assertEquals(expectedAccount3, result3, "Should return correct account for third NAV reference");
        
        verify(readAccountsUseCase).executeByNavReference(navRef1);
        verify(readAccountsUseCase).executeByNavReference(navRef2);
        verify(readAccountsUseCase).executeByNavReference(navRef3);
    }

    @Test
    void fetchAccount_WithSameNavReferenceMultipleTimes_ShouldCallUseCaseEachTime() throws NotFoundException {
        // Given
        Bale bale = FakeBale.getFakeBale(1).get(0);
        Account expectedAccount = FakeAccounts.getFakeAccounts(1, Account.Type.BANK_ACCOUNT).get(0);
        String navReference = bale.getAccount().getNavReference();
        
        when(readAccountsUseCase.executeByNavReference(navReference)).thenReturn(expectedAccount);

        // When
        Account result1 = enricher.fetchAccount(bale);
        Account result2 = enricher.fetchAccount(bale);
        Account result3 = enricher.fetchAccount(bale);

        // Then
        assertEquals(expectedAccount, result1, "First call should return expected account");
        assertEquals(expectedAccount, result2, "Second call should return expected account");
        assertEquals(expectedAccount, result3, "Third call should return expected account");
        
        // Should call use case each time (no caching)
        verify(readAccountsUseCase, times(3)).executeByNavReference(navReference);
    }

    @Test
    void fetchAccount_WithEmptyNavReference_ShouldPassToUseCase() throws NotFoundException {
        // Given
        Account accountWithEmptyNavRef = Account.builder()
            .navReference("")
            .build();
        
        Bale baleWithEmptyNavRef = FakeBale.getFakeBale(1).get(0).toBuilder()
            .account(accountWithEmptyNavRef)
            .build();
        
        Account expectedAccount = FakeAccounts.getFakeAccounts(1, Account.Type.BANK_ACCOUNT).get(0);
        when(readAccountsUseCase.executeByNavReference("")).thenReturn(expectedAccount);

        // When
        Account result = enricher.fetchAccount(baleWithEmptyNavRef);

        // Then
        assertEquals(expectedAccount, result, "Should return account even for empty NAV reference");
        verify(readAccountsUseCase).executeByNavReference("");
    }

    @Test
    void fetchAccount_WithWhitespaceNavReference_ShouldPassToUseCase() throws NotFoundException {
        // Given
        Account accountWithWhitespaceNavRef = Account.builder()
            .navReference("   ")
            .build();
        
        Bale baleWithWhitespaceNavRef = FakeBale.getFakeBale(1).get(0).toBuilder()
            .account(accountWithWhitespaceNavRef)
            .build();
        
        Account expectedAccount = FakeAccounts.getFakeAccounts(1, Account.Type.BANK_ACCOUNT).get(0);
        when(readAccountsUseCase.executeByNavReference("   ")).thenReturn(expectedAccount);

        // When
        Account result = enricher.fetchAccount(baleWithWhitespaceNavRef);

        // Then
        assertEquals(expectedAccount, result, "Should return account for whitespace NAV reference");
        verify(readAccountsUseCase).executeByNavReference("   ");
    }
}
