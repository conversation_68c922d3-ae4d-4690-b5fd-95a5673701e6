package pt.jumia.services.brad.domain.usecases.bale.validation;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BaleDataConsistencyValidatorTest {

    @Mock
    private BaleRepository baleRepository;
    
    @Mock
    private BradBaleRepository bradBaleRepository;
    
    @Mock
    private ReadViewEntityUseCase readViewEntityUseCase;

    private BaleDataConsistencyValidator validator;

    @BeforeEach
    void setUp() {
        validator = new BaleDataConsistencyValidator(baleRepository, bradBaleRepository, readViewEntityUseCase);
    }

    @Test
    void validatePreProcessingConditions_WithValidConditions_ShouldPass() throws Exception {
        // Given
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0),
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(bradBaleRepository.findLastBaleInBradOfCompanyId("test")).thenReturn(Optional.empty());

        // When & Then
        assertDoesNotThrow(() -> validator.validatePreProcessingConditions());
        
        verify(readViewEntityUseCase).execute(ViewEntity.EntityType.BALE);
        verify(baleRepository).refresh();
        verify(bradBaleRepository).findLastBaleInBradOfCompanyId("test");
    }

    @Test
    void validatePreProcessingConditions_WithNoViewEntities_ShouldThrowException() throws Exception {
        // Given
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(Collections.emptyList());

        // When & Then
        DataConsistencyException exception = assertThrows(DataConsistencyException.class,
                () -> validator.validatePreProcessingConditions());
        
        assertTrue(exception.getMessage().contains("No bale view entities found"));
    }

    @Test
    void validatePreProcessingConditions_WithInvalidViewEntity_ShouldThrowException() throws Exception {
        // Given
        ViewEntity invalidViewEntity = ViewEntity.builder()
                .viewName(null)
                .build();
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE))
                .thenReturn(Arrays.asList(invalidViewEntity));

        // When & Then
        DataConsistencyException exception = assertThrows(DataConsistencyException.class,
                () -> validator.validatePreProcessingConditions());
        
        assertTrue(exception.getMessage().contains("Invalid view entity found"));
    }

    @Test
    void validatePreProcessingConditions_WithDatabaseError_ShouldThrowException() throws Exception {
        // Given
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        doThrow(new RuntimeException("Database connection failed"))
                .when(baleRepository).refresh();

        // When & Then
        DataConsistencyException exception = assertThrows(DataConsistencyException.class,
                () -> validator.validatePreProcessingConditions());
        
        assertTrue(exception.getMessage().contains("Database connectivity validation failed"));
    }

    @Test
    void validatePostProcessingResults_WithMatchingCounts_ShouldPass() throws Exception {
        // Given
        long expectedCount = 1000L;
        long actualCount = 1000L;

        // When & Then
        assertDoesNotThrow(() -> validator.validatePostProcessingResults(expectedCount, actualCount));
    }

    @Test
    void validatePostProcessingResults_WithMinorDiscrepancy_ShouldLogWarning() throws Exception {
        // Given
        long expectedCount = 1000L;
        long actualCount = 995L; // 0.5% difference

        // When & Then
        assertDoesNotThrow(() -> validator.validatePostProcessingResults(expectedCount, actualCount));
    }

    @Test
    void validatePostProcessingResults_WithMajorDiscrepancy_ShouldThrowException() throws Exception {
        // Given
        long expectedCount = 1000L;
        long actualCount = 900L; // 10% difference

        // When & Then
        DataConsistencyException exception = assertThrows(DataConsistencyException.class,
                () -> validator.validatePostProcessingResults(expectedCount, actualCount));
        
        assertTrue(exception.getMessage().contains("Significant processing count discrepancy"));
    }

    @Test
    void validateRestartConditions_WithValidConditions_ShouldPass() throws Exception {
        // Given
        List<ViewEntity> viewEntities = Arrays.asList(
            FakeViewEntity.getFakeViewEntity(1).get(0)
        );
        
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(viewEntities);
        when(bradBaleRepository.findLastBaleInBradOfCompanyId("test")).thenReturn(Optional.empty());

        // When & Then
        assertDoesNotThrow(() -> validator.validateRestartConditions());
    }

    @Test
    void validateRestartConditions_WithInvalidConditions_ShouldThrowException() throws Exception {
        // Given
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(Collections.emptyList());

        // When & Then
        DataConsistencyException exception = assertThrows(DataConsistencyException.class,
                () -> validator.validateRestartConditions());
        
        assertTrue(exception.getMessage().contains("Restart validation failed"));
    }
}