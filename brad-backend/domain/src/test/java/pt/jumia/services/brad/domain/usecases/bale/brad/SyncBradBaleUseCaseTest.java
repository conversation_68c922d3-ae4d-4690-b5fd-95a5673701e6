package pt.jumia.services.brad.domain.usecases.bale.brad;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.fxrates.brad.ReadBradFxRateUseCase;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SyncBradBaleUseCase focusing on simplified Spring Batch integration.
 * 
 * Tests cover:
 * - Individual bale processing (processBale method)
 * - Batch synchronization (execute method)
 * - Error handling and validation
 * - FX rate enrichment integration
 */
@ExtendWith(MockitoExtension.class)
public class SyncBradBaleUseCaseTest {

    private static final List<Bale> BALE_LIST = new ArrayList<>(FakeBale.getFakeBale(10));
    
    @Mock
    private BradBaleRepository bradBaleRepository;

    @Mock
    private ReadCurrenciesUseCase readCurrenciesUseCase;

    @Mock
    private ReadAccountsUseCase readAccountsUseCase;

    @Mock
    private ReadBradFxRateUseCase readBradFxRateUseCase;

    @InjectMocks
    private SyncBradBaleUseCase syncBradBaleUseCase;

    private List<Bale> testBales;
    private Account testAccount;
    private Currency testCurrency;

    @BeforeEach
    void setUp() {
        testAccount = FakeAccounts.getFakeAccounts(1, null).get(0);
        testCurrency = FakeCurrencies.ALL_CURRENCIES.get(0);
        testBales = FakeBale.getFakeBale(3);
    }

    // ========== TESTS FOR SIMPLIFIED EXECUTE METHOD ==========

    @Test
    public void execute_withValidBales_shouldSyncSuccessfully() throws DatabaseErrorsException {
        // Given
        when(bradBaleRepository.sync(anyList())).thenReturn(BALE_LIST);
        
        // When
        List<Bale> result = syncBradBaleUseCase.execute(BALE_LIST);

        // Then
        assertEquals(BALE_LIST, result);
        verify(bradBaleRepository).sync(BALE_LIST);
    }

    @Test
    public void execute_withEmptyList_shouldReturnEmptyList() throws DatabaseErrorsException {
        // Given
        List<Bale> emptyList = List.of();
        
        // When
        List<Bale> result = syncBradBaleUseCase.execute(emptyList);

        // Then
        assertTrue(result.isEmpty());
        verify(bradBaleRepository, never()).sync(any());
    }

    @Test
    public void execute_withNullList_shouldReturnEmptyList() throws DatabaseErrorsException {
        // When
        List<Bale> result = syncBradBaleUseCase.execute(null);

        // Then
        assertTrue(result.isEmpty());
        verify(bradBaleRepository, never()).sync(any());
    }

    @Test
    public void execute_withRepositoryException_shouldThrowDatabaseErrorsException() {
        // Given
        when(bradBaleRepository.sync(anyList())).thenThrow(new RuntimeException("Database error"));
        
        // When & Then
        DatabaseErrorsException exception = assertThrows(DatabaseErrorsException.class, 
            () -> syncBradBaleUseCase.execute(BALE_LIST));
        
        assertTrue(exception.getMessage().contains("Bale sync failed"));
    }

    // ========== TESTS FOR PROCESS BALE METHOD ==========

    @Test
    public void processBale_withValidBale_shouldEnrichSuccessfully() throws Exception {
        // Given
        Bale inputBale = testBales.get(0);
        Set<FxRate> mockFxRates = new HashSet<>();
        
        when(readAccountsUseCase.executeByNavReference(anyString())).thenReturn(testAccount);
        when(readCurrenciesUseCase.execute(anyString())).thenReturn(testCurrency);
        doNothing().when(readBradFxRateUseCase).addFxRate(any(), anyString(), anyString(), any());

        // When
        Bale result = syncBradBaleUseCase.processBale(inputBale);

        // Then
        assertNotNull(result);
        assertEquals(testAccount, result.getAccount());
        assertEquals(testCurrency, result.getTransactionCurrency());
        assertNotNull(result.getFxRates());
        
        verify(readAccountsUseCase).executeByNavReference(inputBale.getAccount().getNavReference());
        verify(readCurrenciesUseCase).execute(anyString());
        verify(readBradFxRateUseCase, atLeastOnce()).addFxRate(any(), anyString(), anyString(), any());
    }

    @Test
    public void processBale_withNullBale_shouldThrowException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> syncBradBaleUseCase.processBale(null));
        
        assertEquals("Bale cannot be null", exception.getMessage());
    }

    @Test
    public void processBale_withInvalidBale_shouldThrowException() {
        // Given
        Bale invalidBale = Bale.builder().build(); // Missing required fields
        
        // When & Then
        assertThrows(Exception.class, () -> syncBradBaleUseCase.processBale(invalidBale));
    }

    @Test
    public void processBale_withAccountLookupFailure_shouldThrowException() {
        // Given
        Bale inputBale = testBales.get(0);
        when(readAccountsUseCase.executeByNavReference(anyString()))
            .thenThrow(new NotFoundException("Account not found"));
        
        // When & Then
        Exception exception = assertThrows(Exception.class, 
            () -> syncBradBaleUseCase.processBale(inputBale));
        
        assertTrue(exception.getMessage().contains("Failed to fetch account"));
    }

    @Test
    public void processBale_withCurrencyLookupFailure_shouldThrowException() {
        // Given
        Bale inputBale = testBales.get(0);
        when(readAccountsUseCase.executeByNavReference(anyString())).thenReturn(testAccount);
        when(readCurrenciesUseCase.execute(anyString()))
            .thenThrow(new NotFoundException("Currency not found"));
        
        // When & Then
        Exception exception = assertThrows(Exception.class, 
            () -> syncBradBaleUseCase.processBale(inputBale));
        
        assertTrue(exception.getMessage().contains("Failed to determine currency"));
    }

    @Test
    public void processBale_withFxRateEnrichment_shouldAddCorrectRates() throws Exception {
        // Given
        Bale inputBale = testBales.get(0);
        when(readAccountsUseCase.executeByNavReference(anyString())).thenReturn(testAccount);
        when(readCurrenciesUseCase.execute(anyString())).thenReturn(testCurrency);
        
        // When
        Bale result = syncBradBaleUseCase.processBale(inputBale);

        // Then
        verify(readBradFxRateUseCase).addFxRate(any(), eq(testCurrency.getCode()), eq("USD"), any());
        if (testAccount.getCountry() != null && testAccount.getCountry().getCurrency() != null) {
            verify(readBradFxRateUseCase).addFxRate(any(), eq(testCurrency.getCode()), 
                eq(testAccount.getCountry().getCurrency().getCode()), any());
        }
    }
}
