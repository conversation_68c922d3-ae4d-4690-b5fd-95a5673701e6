package pt.jumia.services.brad.domain.usecases.bale.enrichment;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;

/**
 * Focused service responsible only for fetching and attaching Account data to bales.
 * Extracted from SyncBradBaleUseCase to follow Single Responsibility Principle.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BaleAccountEnricher {

    private final ReadAccountsUseCase readAccountsUseCase;

    /**
     * Fetches and enriches a bale with account information.
     * 
     * @param bale the bale to enrich with account data
     * @return the account associated with the bale
     * @throws NotFoundException if the account cannot be found
     */
    public Account fetchAccount(Bale bale) throws NotFoundException {
        try {
            String navReference = bale.getAccount().getNavReference();
            log.debug("Fetching account for NAV reference: {}", navReference);
            
            Account account = readAccountsUseCase.executeByNavReference(navReference);
            
            if (account == null) {
                throw NotFoundException.createNotFound(Account.class, navReference);
            }
            
            log.debug("Successfully fetched account for NAV reference: {}", navReference);
            return account;
            
        } catch (NotFoundException e) {
            throw e; // Re-throw specific exception
        } catch (Exception e) {
            String navRef = bale.getAccount() != null ? bale.getAccount().getNavReference() : "null";
            throw NotFoundException.createNotFound(Account.class, navRef);
        }
    }
}
