package pt.jumia.services.brad.domain.entities;

import lombok.Builder;
import lombok.Getter;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Value
@Builder(toBuilder = true)
public class FxRate {

    Integer id;
    Currency baseCurrency;
    Currency quoteCurrency;
    LocalDate rateDate;
    BigDecimal bid;
    LocalDateTime bisLoadedAt;
    Integer skAudInsert;
    Integer skAudUpdate;
    LocalDateTime timestampLastUpdate;

    @Getter
    public enum SelectFields implements BaseSelectFields {
        ID("id", "id"),
        BASE_CURRENCY("baseCurrency", "baseCurrency"),
        QUOTE_CURRENCY("quoteCurrency", "quoteCurrency"),
        RATE_DATE("rateDate", "rateDate"),
        BID("bid", "bid"),
        BIS_LOADED_AT("bisLoadedAt", "bisLoadedAt"),
        SK_AUD_INSERT("skAudInsert", "skAudInsert"),
        SK_AUD_UPDATE("skAudUpdate", "skAudUpdate"),
        TIMESTAMP_LAST_UPDATE("timestampLastUpdate", "timestampLastUpdate");

        public final String queryField;
        public final String selectCode;

        SelectFields(String queryField, String selectCode) {
            this.queryField = queryField;
            this.selectCode = selectCode;
        }
    }

    public enum SortingFields {
        BASE_CURRENCY,
        QUOTE_CURRENCY,
        RATE_DATE,
        BID,
        BIS_LOADED_AT,
        SK_AUD_INSERT,
        SK_AUD_UPDATE,
        TIMESTAMP_LAST_UPDATE
    }

    public FxRate withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .build();
    }

}
