package pt.jumia.services.brad.domain.entities;

import lombok.Builder;
import lombok.Value;

import java.math.BigDecimal;
import java.time.LocalDateTime;


@Value
@Builder(toBuilder = true)
public class FinrecStatement {

    String tranID;
    LocalDateTime initialDate;
    LocalDateTime finalDate;
    String accountNumber;
    BigDecimal openingBalance;
    String currency;
    String type;
    String hasTransaction;
    BigDecimal runningBalance;
    String reference;
    String valueDate;
    String description;
    BigDecimal tranAmount;
    String direction;
    String tranDate;
    Integer skAudInsert;
    LocalDateTime timestampRunAt;
}
