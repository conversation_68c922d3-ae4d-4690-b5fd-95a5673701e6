package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public interface FakeBale {


    Bale FAKE_BALE = Bale.builder()
            .idCompany("IDC")
            .entryNo(1)
            .documentNo("documentNo")
            .documentType("documentType")
            .postingDate(LocalDate.now())
            .account(FakeAccounts.FAKE_ACCOUNT)
            .accountPostingGroup("bankAccountPostingGroup")
            .description("description")
            .sourceCode("sourceCode")
            .reasonCode("reasonCode")
            .busLine("busLine")
            .department("department")
            .direction(Direction.CREDIT)
            .amount(BigDecimal.ONE)
            .remainingAmount(BigDecimal.ONE)
            .transactionCurrency(FakeCurrencies.NGN)
            .amountLcy(BigDecimal.ONE)
            .balanceAccountType("balAccType")
            .isOpen(true)
            .isReversed(true)
            .postedBy("postedBy")
            .externalDocumentNo("externalDocumentNo")
            .baleTimestamp("0x000000000000000")
            .accountTimestamp("0x000000000000000")
            .reconcileStatus(ReconcileStatus.NOT_RECONCILED)
            .build();

    static List<Bale> getFakeBale(int amount) {

        List<Bale> fakeBale = new ArrayList<>();
        for (int i = 1; i <= amount; i++) {
            fakeBale.add(FAKE_BALE.toBuilder()
                    .entryNo(FAKE_BALE.getEntryNo() + i)
                    .documentNo(FAKE_BALE.getDocumentNo() + i)
                    .documentType(FAKE_BALE.getDocumentType() + i)
                    .postingDate(LocalDate.now())
                    .account(FakeAccounts.FAKE_ACCOUNT)
                    .accountPostingGroup(FAKE_BALE.getAccountPostingGroup() + i)
                    .description(FAKE_BALE.getDescription() + i)
                    .sourceCode(FAKE_BALE.getSourceCode() + i)
                    .reasonCode(FAKE_BALE.getReasonCode() + i)
                    .busLine(FAKE_BALE.getBusLine() + i)
                    .department(FAKE_BALE.getDepartment() + i)
                    .direction(Direction.CREDIT)
                    .amount(BigDecimal.ONE)
                    .remainingAmount(BigDecimal.ONE)
                    .transactionCurrency(FakeCurrencies.NGN)
                    .amountLcy(BigDecimal.ONE)
                    .balanceAccountType(FAKE_BALE.getBalanceAccountType() + i)
                    .isOpen(true)
                    .isReversed(true)
                    .postedBy(FAKE_BALE.getPostedBy() + i)
                    .externalDocumentNo(FAKE_BALE.getExternalDocumentNo() + i)
                    .baleTimestamp(FakeBale.FAKE_BALE.getBaleTimestamp() + i)
                    .accountTimestamp(FakeBale.FAKE_BALE.getAccountTimestamp() + i)
                    .reconcileStatus(FAKE_BALE.getReconcileStatus())
                    .build());
        }
        return fakeBale;

    }



}

