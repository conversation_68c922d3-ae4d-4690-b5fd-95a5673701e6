package pt.jumia.services.brad.domain.usecases.bale.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class BaleJobOrchestrator {

    @Qualifier("jobLauncher")
    private final JobLauncher jobLauncher;

    @Qualifier("baleJob")
    private final Job baleJob;

    private final ReadViewEntityUseCase readViewEntityUseCase;

    public Object triggerBaleSync(Integer entryNo) throws Exception {
        log.info("Triggering Spring Batch bale sync job for entry number: {}", entryNo);

        try {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("entryNo", entryNo);
            parameters.put("timestamp", System.currentTimeMillis());
            parameters.put("triggeredBy", "manual-sync-endpoint");

            JobExecution jobExecution = launchBatchJob(parameters);
            return new BatchJobResult(
                    jobExecution.getJobId(),
                    jobExecution.getStatus().toString(),
                    jobExecution.getStatus().isUnsuccessful() == false
            );

        } catch (Exception e) {
            log.error("Failed to trigger bale sync job for entry number: {}", entryNo, e);
            throw new RuntimeException("Bale sync job launch failed", e);
        }
    }

    public Object triggerBaleSyncForView(Integer entryNo, Long baleViewEntityId) throws Exception {
        log.info("Triggering Spring Batch bale sync job for entry number: {} in view: {}", entryNo, baleViewEntityId);

        try {
            ViewEntity baleViewEntity = readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE);

            Map<String, Object> parameters = new HashMap<>();
            parameters.put("entryNo", entryNo);
            parameters.put("baleViewEntityId", baleViewEntityId);
            parameters.put("viewName", baleViewEntity.getViewName());
            parameters.put("timestamp", System.currentTimeMillis());
            parameters.put("triggeredBy", "manual-sync-view-endpoint");

            JobExecution jobExecution = launchBatchJob(parameters);
            return new BatchJobResult(
                    jobExecution.getJobId(),
                    jobExecution.getStatus().toString(),
                    jobExecution.getStatus().isUnsuccessful() == false
            );

        } catch (Exception e) {
            log.error("Failed to trigger bale sync job for entry number: {} in view: {}", entryNo, baleViewEntityId, e);
            throw new RuntimeException("Bale sync job launch failed", e);
        }
    }

    public Object triggerBaleSyncForViews(List<Integer> baleViewIds) throws Exception {
        log.info("Triggering Spring Batch bale sync job for {} view IDs: {}", baleViewIds.size(), baleViewIds);

        try {
            List<ViewEntity> baleViewEntities = new ArrayList<>();
            for (Integer viewId : baleViewIds) {
                try {
                    ViewEntity viewEntity = readViewEntityUseCase.execute(viewId, ViewEntity.EntityType.BALE);
                    baleViewEntities.add(viewEntity);
                } catch (Exception e) {
                    log.warn("Bale view entity with ID {} not found, skipping", viewId);
                }
            }

            if (baleViewEntities.isEmpty()) {
                throw new IllegalArgumentException("No valid bale view entities found for provided IDs: " + baleViewIds);
            }

            Map<String, Object> parameters = new HashMap<>();
            parameters.put("baleViewIds", baleViewIds);
            parameters.put("viewCount", baleViewEntities.size());
            parameters.put("timestamp", System.currentTimeMillis());
            parameters.put("triggeredBy", "manual-sync-views-endpoint");

            JobExecution jobExecution = launchBatchJob(parameters);
            return new BatchJobResult(
                    jobExecution.getJobId(),
                    jobExecution.getStatus().toString(),
                    jobExecution.getStatus().isUnsuccessful() == false
            );

        } catch (Exception e) {
            log.error("Failed to trigger bale sync job for view IDs: {}", baleViewIds, e);
            throw new RuntimeException("Bale sync job launch failed", e);
        }
    }

    protected JobExecution launchBatchJob(Map<String, Object> parameters) throws Exception {
        if (jobLauncher == null || baleJob == null) {
            throw new IllegalStateException(
                    "Spring Batch dependencies not properly injected. " +
                            "Ensure JobLauncher and Job beans are available in the application context."
            );
        }

        log.info("Launching Spring Batch job with parameters: {}", parameters);

        try {
            JobParametersBuilder builder = new JobParametersBuilder();

            for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                if (value instanceof String) {
                    builder.addString(key, (String) value);
                } else if (value instanceof Long) {
                    builder.addLong(key, (Long) value);
                } else if (value instanceof Integer) {
                    builder.addLong(key, ((Integer) value).longValue());
                } else if (value instanceof Double) {
                    builder.addDouble(key, (Double) value);
                } else {
                    builder.addString(key, String.valueOf(value));
                }
            }

            JobParameters jobParameters = builder.toJobParameters();

            JobExecution jobExecution = jobLauncher.run(baleJob, jobParameters);

            log.info("Spring Batch job launched successfully. Job ID: {}, Status: {}",
                    jobExecution.getJobId(), jobExecution.getStatus());

            return jobExecution;

        } catch (Exception e) {
            log.error("Failed to launch Spring Batch job with parameters: {}", parameters, e);
            throw new RuntimeException("Spring Batch job launch failed: " + e.getMessage(), e);
        }
    }
}
