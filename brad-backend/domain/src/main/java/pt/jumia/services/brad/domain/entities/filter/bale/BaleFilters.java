package pt.jumia.services.brad.domain.entities.filter.bale;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.shared.BaseFilters;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BaleFilters extends BaseFilters {

    private String idCompany;
    private String accountNumber;
    private List<Integer> entryNo;
    private String documentNo;
    private String documentType;
    private LocalDate postingDateStart;
    private LocalDate postingDateEnd;
    private String accountPostingGroup;
    private String description;
    private String sourceCode;
    private String reasonCode;
    private String busLine;
    private String department;
    private List<String> direction;
    private BigDecimal amount;
    private BigDecimal remainingAmount;
    private List<String> transactionCurrency;
    private BigDecimal amountLcy;
    private String balanceAccountType;
    private Boolean isOpen;
    private Boolean isReversed;
    private String postedBy;
    private String externalDocumentNo;
    private String baleTimestamp;
    private String accountTimestamp;
    private boolean exactFilters;

    //reconciliation related filters
    private Integer reconciliationId;
    private String reconciliationCreator;
    private LocalDate reconciliationCreationDateStart;
    private LocalDate reconciliationCreationDateEnd;
    private String reconciliationReviewer;
    private LocalDate reconciliationReviewDateStart;
    private LocalDate reconciliationReviewDateEnd;
    private List<String> reconciliationStatus;

    public Map<String, ?> getAsMap(){
        HashMap<String, Object> map = new HashMap<>();

        if (Objects.nonNull(idCompany)) {
            map.put("idCompany", idCompany);
        }
        if (Objects.nonNull(accountNumber)) {
            map.put("account", accountNumber);
        }
        if (Objects.nonNull(entryNo)) {
            map.put("entryNo", entryNo);
        }
        if (Objects.nonNull(documentNo)) {
            map.put("documentNo", documentNo);
        }
        if (Objects.nonNull(documentType)) {
            map.put("documentType", documentType);
        }
        if (Objects.nonNull(postingDateStart)) {
            map.put("postingDateStart", postingDateStart);
        }
        if (Objects.nonNull(postingDateEnd)) {
            map.put("postingDateEnd", postingDateEnd);
        }
        if (Objects.nonNull(accountPostingGroup)) {
            map.put("bankAccountPostingGroup", accountPostingGroup);
        }
        if (Objects.nonNull(description)) {
            map.put("description", description);
        }
        if (Objects.nonNull(sourceCode)) {
            map.put("sourceCode", sourceCode);
        }
        if (Objects.nonNull(reasonCode)) {
            map.put("reasonCode", reasonCode);
        }
        if (Objects.nonNull(busLine)) {
            map.put("busLine", busLine);
        }
        if (Objects.nonNull(department)) {
            map.put("department", department);
        }
        if (!Objects.isNull(direction)) {
            map.put("direction", direction);
        }
        if (Objects.nonNull(amount)) {
            map.put("amount", amount);
        }
        if (Objects.nonNull(remainingAmount)) {
            map.put("remainingAmount", remainingAmount);
        }
        if (Objects.nonNull(transactionCurrency)) {
            map.put("transactionCurrency", transactionCurrency);
        }
        if (Objects.nonNull(amountLcy)) {
            map.put("amountLcy", amountLcy);
        }
        if (Objects.nonNull(balanceAccountType)) {
            map.put("balanceAccountType", balanceAccountType);
        }
        if (Objects.nonNull(isOpen)) {
            map.put("isOpen", isOpen);
        }
        if (Objects.nonNull(isReversed)) {
            map.put("isReversed", isReversed);
        }
        if (Objects.nonNull(postedBy)) {
            map.put("postedBy", postedBy);
        }
        if (Objects.nonNull(externalDocumentNo)) {
            map.put("externalDocumentNo", externalDocumentNo);
        }
        if (Objects.nonNull(baleTimestamp)) {
            map.put("baleTimestamp", baleTimestamp);
        }
        if (Objects.nonNull(accountTimestamp)) {
            map.put("bankAccountTimestamp", accountTimestamp);
        }
        if (Objects.nonNull(reconciliationCreator)) {
            map.put("reconciliationCreator", reconciliationCreator);
        }
        if (Objects.nonNull(reconciliationCreationDateStart)) {
            map.put("reconciliationCreationDateStart", reconciliationCreationDateStart);
        }
        if (Objects.nonNull(reconciliationCreationDateEnd)) {
            map.put("reconciliationCreationDateEnd", reconciliationCreationDateEnd);
        }
        if (Objects.nonNull(reconciliationReviewer)) {
            map.put("reconciliationReviewer", reconciliationReviewer);
        }
        if (Objects.nonNull(reconciliationReviewDateStart)) {
            map.put("reconciliationReviewDateStart", reconciliationReviewDateStart);
        }
        if (Objects.nonNull(reconciliationReviewDateEnd)) {
            map.put("reconciliationReviewDateEnd", reconciliationReviewDateEnd);
        }
        if (Objects.nonNull(reconciliationStatus)) {
            map.put("reconciliationStatus", reconciliationStatus);
        }

        return map;
    }
}
