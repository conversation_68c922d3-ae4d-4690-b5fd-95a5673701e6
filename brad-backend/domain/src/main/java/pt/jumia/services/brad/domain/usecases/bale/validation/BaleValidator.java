package pt.jumia.services.brad.domain.usecases.bale.validation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.exceptions.InvalidEntityException;

/**
 * Focused service responsible only for validating bale data integrity and business rules.
 * Extracted from SyncBradBaleUseCase to follow Single Responsibility Principle.
 */
@Slf4j
@Component
public class BaleValidator {

    /**
     * Validates a bale for processing requirements.
     * 
     * @param bale the bale to validate
     * @throws InvalidEntityException if validation fails
     */
    public void validateBale(Bale bale) throws InvalidEntityException {
        if (bale == null) {
            throw InvalidEntityException.createInvalidEntity(Bale.class, "cannot be null");
        }

        validateAccount(bale);
        validateEntryNumber(bale);
        
        log.debug("Bale validation passed for entry number: {}", bale.getEntryNo());
    }

    private void validateAccount(Bale bale) throws InvalidEntityException {
        if (bale.getAccount() == null) {
            throw InvalidEntityException.createInvalidEntity(Bale.class, "account cannot be null for entry: " + bale.getEntryNo());
        }

        if (bale.getAccount().getNavReference() == null || 
            bale.getAccount().getNavReference().trim().isEmpty()) {
            throw InvalidEntityException.createInvalidEntity(Bale.class, 
                "account NAV reference cannot be null or empty for entry: " + bale.getEntryNo());
        }
    }

    private void validateEntryNumber(Bale bale) throws InvalidEntityException {
        if (bale.getEntryNo() == null) {
            throw InvalidEntityException.createInvalidEntity(Bale.class, "entry number cannot be null");
        }
    }
}
