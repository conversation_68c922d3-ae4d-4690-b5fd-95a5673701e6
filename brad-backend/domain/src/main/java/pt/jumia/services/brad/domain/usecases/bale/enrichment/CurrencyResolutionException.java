package pt.jumia.services.brad.domain.usecases.bale.enrichment;

/**
 * Exception thrown when currency determination fails.
 * Part of the specific exception hierarchy recommended in the audit.
 */
public class CurrencyResolutionException extends Exception {

    public CurrencyResolutionException(String message) {
        super(message);
    }

    public CurrencyResolutionException(String message, Throwable cause) {
        super(message, cause);
    }
}
