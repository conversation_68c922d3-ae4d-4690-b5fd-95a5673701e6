package pt.jumia.services.brad.domain.entities.dtos;

import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Country;

@Data
@Builder(toBuilder = true)
public class GroupedAccountDailySummaryDto {

    private String groupLabel;
    private BigDecimal initialBalanceUsd;
    private BigDecimal finalBalanceUsd;
    private BigDecimal initialBalanceLcy;
    private BigDecimal finalBalanceLcy;
    private BigDecimal creditAmountLcy;
    private BigDecimal debitAmountLcy;
    private BigDecimal creditAmountUsd;
    private BigDecimal debitAmountUsd;
    private Account account;
    private Country country;

}
