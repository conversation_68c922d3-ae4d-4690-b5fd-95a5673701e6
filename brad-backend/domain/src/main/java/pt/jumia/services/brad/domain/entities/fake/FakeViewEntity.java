package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.ViewEntity;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public interface FakeViewEntity {

    ViewEntity FAKE_VIEW_ENTITY = ViewEntity.builder()
            .id(1L)
            .entityType(ViewEntity.EntityType.BALE)
            .driver("com.microsoft.sqlserver.jdbc.SQLServerDriver")
            .jdbcConnectionUrl("jdbcConnectionUrl")
            .databaseName("databaseName")
            .schemaName("schemaName")
            .viewName("viewName")
            .createdAt(LocalDateTime.now())
            .createdBy("createdBy")
            .updatedAt(LocalDateTime.now())
            .updatedBy("updatedBy")
            .build();

    static List<ViewEntity> getFakeViewEntity(int amount) {

            List<ViewEntity> fakeViewEntity = new ArrayList<>();
            for (int i = 0; i < amount; i++) {
                fakeViewEntity.add(FAKE_VIEW_ENTITY.toBuilder()
                        .id(FAKE_VIEW_ENTITY.getId() + i)
                        .jdbcConnectionUrl(FAKE_VIEW_ENTITY.getJdbcConnectionUrl() + i)
                        .databaseName(FAKE_VIEW_ENTITY.getDatabaseName() + i)
                        .schemaName(FAKE_VIEW_ENTITY.getSchemaName() + i)
                        .viewName(FAKE_VIEW_ENTITY.getViewName() + i)
                        .createdAt(LocalDateTime.now())
                        .createdBy(FAKE_VIEW_ENTITY.getCreatedBy() + i)
                        .updatedAt(LocalDateTime.now())
                        .updatedBy(FAKE_VIEW_ENTITY.getUpdatedBy() + i)
                        .build());
            }
            return fakeViewEntity;
    }



}

