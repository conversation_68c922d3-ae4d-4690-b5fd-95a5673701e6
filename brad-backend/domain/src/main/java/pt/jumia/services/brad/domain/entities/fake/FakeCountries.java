package pt.jumia.services.brad.domain.entities.fake;
import pt.jumia.services.brad.domain.entities.account.Country;

import java.time.LocalDateTime;
import java.util.List;

public interface FakeCountries {

    String FAKE_USER = "fakeUser";

    Country NIGERIA = Country.builder()
            .name("Nigeria")
            .code("NG")
            .currency(FakeCurrencies.NGN)
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Country EGYPT = Country.builder()
            .name("Egypt")
            .code("EG")
            .currency(FakeCurrencies.EGP)
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Country KENYA = Country.builder()
            .name("Kenya")
            .code("KE")
            .currency(FakeCurrencies.KES)
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Country MOROCCO = Country.builder()
            .name("Morocco")
            .code("MA")
            .currency(FakeCurrencies.MAD)
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Country IVORY_COAST = Country.builder()
            .name("Ivory Coast")
            .code("CI")
            .currency(FakeCurrencies.XOF)
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Country ALGERIA = Country.builder()
            .name("Algeria")
            .code("DZ")
            .currency(FakeCurrencies.DZD)
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Country GHANA = Country.builder()
            .name("Ghana")
            .code("GH")
            .currency(FakeCurrencies.GHS)
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Country SENEGAL = Country.builder()
            .name("Senegal")
            .code("SN")
            .currency(FakeCurrencies.XOF)
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Country TUNISIA = Country.builder()
            .name("Tunisia")
            .code("TN")
            .currency(FakeCurrencies.TND)
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Country UGANDA = Country.builder()
            .name("Uganda")
            .code("UG")
            .currency(FakeCurrencies.UGX)
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Country SOUTH_AFRICA = Country.builder()
            .name("South Africa")
            .code("ZA")
            .currency(FakeCurrencies.ZAR)
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    List<Country> ALL_COUNTRIES = List.of(NIGERIA, EGYPT, KENYA, MOROCCO, IVORY_COAST, ALGERIA, GHANA, SENEGAL, TUNISIA, UGANDA, SOUTH_AFRICA);

    Country NIGERIA_WITH_ID = Country.builder()
            .id(1L)
            .name("Nigeria")
            .code("NG")
            .currency(FakeCurrencies.NGN)
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

}
