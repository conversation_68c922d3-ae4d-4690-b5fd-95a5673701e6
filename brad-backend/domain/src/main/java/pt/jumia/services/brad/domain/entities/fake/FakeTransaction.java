package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public interface FakeTransaction {
    
    Transaction FAKE_TRANSACTION = Transaction.builder()
            .id(1L)
            .type("TR")
            .currency(FakeCurrencies.EUR)
            .valueDate(LocalDate.now())
            .transactionDate(LocalDate.now())
            .statementDate(LocalDate.now())
            .direction(Direction.CREDIT)
            .amount(BigDecimal.ONE)
            .reference("TEST_REFERENCE")
            .description("TEST_DESCRIPTION")
            .accountStatement(FakeAccountStatements.FAKE_ACCOUNT_STATEMENT)
            .remittanceInformation("TEST_REMITTANCE_INFORMATION")
            .orderingPartyName("TEST_ORDERING_PARTY_NAME")
            .fxRates(Set.of())
            .reconcileStatus(ReconcileStatus.NOT_RECONCILED)
            .createdAt(LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS))
            .createdBy("TEST")
            .updatedAt(LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS))
            .updatedBy("TEST")
            .build();



    static List<Transaction> getFakeCreditTransactions(int amount, AccountStatement statement) {
        return getFakeTransactions(amount, statement, Direction.CREDIT);
    }

    static List<Transaction> getFakeDebitTransactions(int amount, AccountStatement statement) {
        return getFakeTransactions(amount, statement, Direction.DEBIT);
    }

    static List<Transaction> getFakeTransactions(int amount, AccountStatement statement, Direction direction) {
        List<Transaction> fakeTransactions = new ArrayList<>();
        for (int i = 1; i <= amount; i++) {
            fakeTransactions.add(FAKE_TRANSACTION.toBuilder()
                    .id(FAKE_TRANSACTION.getId() + i)
                    .type(FAKE_TRANSACTION.getType() + i)
                    .currency(statement.getCurrency())
                    .valueDate(statement.getInitialDate() == null ? FAKE_TRANSACTION.getValueDate() : statement.getInitialDate())
                    .transactionDate(statement.getInitialDate() == null ? FAKE_TRANSACTION.getTransactionDate() : statement.getInitialDate())
                    .statementDate(statement.getInitialDate() == null ? FAKE_TRANSACTION.getStatementDate() : statement.getInitialDate())
                    .direction(direction)
                    .amount(FAKE_TRANSACTION.getAmount())
                    .reference(FAKE_TRANSACTION.getReference() + i)
                    .description(FAKE_TRANSACTION.getDescription() + i)
                    .accountStatement(statement)
                    .remittanceInformation(FAKE_TRANSACTION.getRemittanceInformation() + i)
                    .orderingPartyName(FAKE_TRANSACTION.getOrderingPartyName() + i)
                    .reconcileStatus(ReconcileStatus.NOT_RECONCILED)
                    .createdAt(LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS))
                    .createdBy(FAKE_TRANSACTION.getCreatedBy())
                    .updatedAt(LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS))
                    .updatedBy(FAKE_TRANSACTION.getUpdatedBy())
                    .build());
        }

        return fakeTransactions;
    }

}
