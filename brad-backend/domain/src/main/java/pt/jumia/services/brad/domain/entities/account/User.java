package pt.jumia.services.brad.domain.entities.account;

import lombok.*;
import org.jetbrains.annotations.Nullable;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

import java.time.LocalDateTime;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor
public class User {

    @Nullable
    Long id;

    @NonNull
    String name;

    @NonNull
    String email;

    Account account;

    LocalDateTime createdAt;

    String createdBy;

    LocalDateTime updatedAt;

    String updatedBy;

    @NonNull
    String hrRole;

    @NonNull
    String permissionType;
    
    String mobilePhoneNumber;

    @NonNull
    Status status;


    public User withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .account(account == null ? null : account.withoutDbFields())
                .updatedAt(null)
                .updatedBy(null)
                .createdAt(null)
                .createdBy(null)
                .hrRole(hrRole)
                .permissionType(permissionType)
                .mobilePhoneNumber(mobilePhoneNumber)
                .status(status)
                .build();
    }

    @Getter
    public enum SelectFields implements BaseSelectFields {
        ID("id", "id"),
        USER_NAME("name", "name"),
        EMAIL("email", "email"),
        CREATED_AT("createdAt", "createdAt"),
        CREATED_BY("createdBy", "createdBy"),
        UPDATED_AT("updatedAt", "updatedAt"),
        UPDATED_BY("updatedBy", "updatedBy"),
        HR_ROLE("hrRole", "hrRole"),
        PERMISSION_TYPE("permissionType", "permissionType"),
        MOBILE_PHONE_NUMBER("mobilePhoneNumber", "mobilePhoneNumber"),
        STATUS("status", "status");

        public final String queryField;
        public final String selectCode;

        SelectFields(String queryField, String selectCode) {
            this.queryField = queryField;
            this.selectCode = selectCode;
        }

    }

    @Getter
    @RequiredArgsConstructor
    public enum Status {
        ACTIVE,
        INACTIVE
    }

        public enum SortingFields {
        ID,
        USER_NAME,
        EMAIL,
        CREATED_AT,
        CREATED_BY,
        UPDATED_AT,
        UPDATED_BY,
        HR_ROLE,
        PERMISSION_TYPE,
        MOBILE_PHONE_NUMBER
    }

}
