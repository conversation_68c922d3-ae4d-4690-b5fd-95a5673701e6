package pt.jumia.services.brad.domain.entities.filter.threshold;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.shared.BaseFilters;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ThresholdFilters extends BaseFilters {

    private String countryCode;
    private String currencyCode;
    private LocalDateTime createdAt;

    public Map<String, ?> getAsMap() {
        HashMap<String, Object> map = new HashMap<>();

        if (!Objects.isNull(this.countryCode)){
            map.put("countryName", this.countryCode);
        }
        if (!Objects.isNull(this.currencyCode)){
            map.put("currencyCode", this.currencyCode);
        }
        if (!Objects.isNull(this.createdAt)){
            map.put("createdAt", this.createdAt);
        }

        return map;
    }
}
