package pt.jumia.services.brad.domain.entities.filter.setting;

import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.Setting;
import pt.jumia.services.brad.domain.entities.Setting.SortingFields;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class SettingSortFilters extends SortFilters<SortingFields> {

    public SettingSortFilters(Setting.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
