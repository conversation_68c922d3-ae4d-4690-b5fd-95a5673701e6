package pt.jumia.services.brad.domain.entities.fake;

import java.util.List;
import pt.jumia.services.brad.domain.entities.Setting;

public interface FakeSettings {

    Setting SETTING_DEFAULT = Setting.builder()
        .property("document.automatic.max-attempts")
        .type(Setting.Type.DEFAULT)
        .value("3")
        .description("Max allowed attempts to process automatic queue documents for Seller NG Jumia")
        .build();


    Setting SETTING_OVERRIDE_ONE = Setting.builder()
        .property("document.automatic.max-attempts")
        .type(Setting.Type.OVERRIDE)
        .value("4")
        .overrideKey("override1")
        .description("Max allowed attempts to process automatic queue documents for Seller NG Jumia")
        .build();

    Setting SETTING_OVERRIDE_TWO = Setting.builder()
        .property("document.automatic.max-attempts")
        .type(Setting.Type.OVERRIDE)
        .value("3")
        .overrideKey("override2")
        .description("Max allowed attempts to process automatic queue documents for Seller NG Jumia")
        .build();

    List<Setting> ALL_DEFAULT = List.of(
        SETTING_DEFAULT
    );

    List<Setting> ALL_OVERRIDE = List.of(
        SETTING_OVERRIDE_ONE,
        SETTING_OVERRIDE_TWO
    );

    List<Setting> ALL = List.of(
        SETTING_DEFAULT,
        SETTING_OVERRIDE_ONE,
        SETTING_OVERRIDE_TWO
    );

}
