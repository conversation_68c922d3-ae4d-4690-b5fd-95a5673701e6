package pt.jumia.services.brad.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.jetbrains.annotations.Nullable;
import pt.jumia.services.brad.domain.enumerations.AuditedEntities;

import java.time.LocalDateTime;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@SuppressWarnings("PMD.AvoidFieldNameMatchingTypeName")
public class AuditedEntity<E> {

    private E entity;
    private RevisionInfo revisionInfo;
    private OperationType operationType;
    private AuditedEntities auditedEntity;

    /**
     * The revision info for the audited entity.
     */
    @Data
    @Builder(toBuilder = true)
    @AllArgsConstructor
    public static class RevisionInfo {
        private LocalDateTime datetime;
        @Nullable
        private String email;
    }

    /**
     * The operation types that can occur when auditing.
     */
    public enum OperationType {
        CREATE, UPDATE, DELETE
    }

    public enum SortingFields {
        REV, DATE, EMAIL
    }
}
