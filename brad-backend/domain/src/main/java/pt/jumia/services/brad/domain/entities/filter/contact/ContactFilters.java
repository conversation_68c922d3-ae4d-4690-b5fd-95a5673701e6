package pt.jumia.services.brad.domain.entities.filter.contact;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.shared.BaseFilters;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContactFilters extends BaseFilters {

    private List<String> contactType;
    private String name;
    private String email;
    private Long accountID;
    private List<Long> accountIds;
    private List<Long> countryCodes;
    private LocalDateTime createdAt;

    public Map<String, ?> getAsMap() {
        HashMap<String, Object> map = new HashMap<>();

        if (!Objects.isNull(this.contactType)) {
            map.put("contactType", this.contactType);
        }
        if (!Objects.isNull(this.name)) {
            map.put("name", this.name);
        }
        if (!Objects.isNull(this.email)) {
            map.put("email", this.email);
        }
        if (!Objects.isNull(this.accountID)) {
            map.put("accountID", this.accountID);
        }

        return map;
    }

}
