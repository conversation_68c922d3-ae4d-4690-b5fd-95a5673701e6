package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public interface FakeFxRates {

   FxRate BASE_FX_RATE = FxRate.builder()
        .bisLoadedAt(LocalDateTime.now())
        .skAudInsert(1)
        .skAudUpdate(1)
        .timestampLastUpdate(LocalDateTime.now())
        .build();

   FxRate FAKE_FX_RATE_EUR_USD = FxRate.builder()
        .baseCurrency(Currency.builder().code("EUR").build())
        .quoteCurrency(Currency.builder().code("USD").build())
        .rateDate(LocalDate.now())
        .bid(new BigDecimal("1.14"))
        .bisLoadedAt(LocalDateTime.now())
        .skAudInsert(1)
        .skAudUpdate(1)
        .timestampLastUpdate(LocalDateTime.now())
        .build();

   FxRate FAKE_FX_RATE_EUR_NGN = FxRate.builder()
        .baseCurrency(Currency.builder().code("EUR").build())
        .quoteCurrency(Currency.builder().code("NGN").build())
        .rateDate(LocalDate.now())
        .bid(new BigDecimal("400.50"))
        .bisLoadedAt(LocalDateTime.now())
        .skAudInsert(1)
        .skAudUpdate(1)
        .timestampLastUpdate(LocalDateTime.now())
        .build();

   FxRate FAKE_FX_RATE_EUR_EGP = FxRate.builder()
        .baseCurrency(Currency.builder().code("EUR").build())
        .quoteCurrency(Currency.builder().code("EGP").build())
        .rateDate(LocalDate.now())
        .bid(new BigDecimal("20.50"))
        .bisLoadedAt(LocalDateTime.now())
        .skAudInsert(1)
        .skAudUpdate(1)
        .timestampLastUpdate(LocalDateTime.now())
        .build();

   List<FxRate> FAKE_FX_RATE_LIST = new ArrayList<>(
           List.of(FAKE_FX_RATE_EUR_USD,
                   FAKE_FX_RATE_EUR_NGN,
                   FAKE_FX_RATE_EUR_EGP
           )
   );
}

