package pt.jumia.services.brad.domain.entities.filter.threshold;

import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.entities.reconciliation.Threshold;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class ThresholdSortFilters extends SortFilters<Threshold.SortingFields> {

    public ThresholdSortFilters(Threshold.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
