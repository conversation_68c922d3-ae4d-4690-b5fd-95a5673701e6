package pt.jumia.services.brad.domain.entities.filter.country;

import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class CountrySortFilters extends SortFilters<Country.SortingFields> {

    public CountrySortFilters(Country.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
