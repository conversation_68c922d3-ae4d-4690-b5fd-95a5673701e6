package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Contact;
import pt.jumia.services.brad.domain.enumerations.ContactType;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public interface FakeContacts {

    Contact FAKE_CONTACT = Contact.builder()
            .contactType(ContactType.OTHERS.name())
            .name("fakeName")
            .email("fakeEmail")
            .mobilePhoneNumber("**********")
            .account(FakeAccounts.getFakeAccounts(1, null).get(0))
            .createdAt(LocalDateTime.now())
            .createdBy("fakeUser")
            .updatedAt(LocalDateTime.now())
            .updatedBy("fakeUser")
            .build();



    static List<Contact> getFakeContacts(int amount) {
        List<Account> fakeAccounts = FakeAccounts.getFakeAccounts(amount, null);
        List<Contact> fakeContacts = new ArrayList<>();
        for (int i = 1; i <= amount; i++) {
            fakeContacts.add(FAKE_CONTACT.toBuilder()
                    .contactType(ContactType.OTHERS.name())
                    .name(FAKE_CONTACT.getName() + i)
                    .email(FAKE_CONTACT.getEmail() + i)
                    .mobilePhoneNumber(FAKE_CONTACT.getMobilePhoneNumber())
                    .account(fakeAccounts.get(i - 1))
                    .createdAt(LocalDateTime.now())
                    .createdBy(FAKE_CONTACT.getCreatedBy() + i)
                    .updatedAt(LocalDateTime.now())
                    .updatedBy(FAKE_CONTACT.getUpdatedBy() + i)
                    .build());
        }

        return fakeContacts;
    }
}
