package pt.jumia.services.brad.domain.entities.filter.user;


import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.account.User;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class UserSortFilters extends SortFilters<User.SortingFields> {

    public UserSortFilters(User.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
