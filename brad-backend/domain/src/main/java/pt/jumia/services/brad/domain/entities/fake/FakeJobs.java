package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.Jobs;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public interface FakeJobs {

    Jobs FAKE_JOB_1 = Jobs.builder()
            .jobName("JobName1")
            .cronExpression("CronExpression1")
            .state("State1")
            .build();

    Jobs FAKE_JOB_2 = Jobs.builder()
            .jobName("JobName2")
            .cronExpression("CronExpression2")
            .state("State2")
            .build();

    List<Jobs> ALL = Collections.unmodifiableList(new ArrayList<>() {{
        add(FAKE_JOB_1);
        add(FAKE_JOB_2);
    }});

}
