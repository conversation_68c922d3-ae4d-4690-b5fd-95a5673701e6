package pt.jumia.services.brad.domain.entities.filter.accountstatement;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.shared.BaseFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountStatementFilters extends BaseFilters {

    private List<String> currencyCodes;
    private String statementId;
    private Long previousStatementID;
    private LocalDate initialDateStart;
    private LocalDate initialDateEnd;
    private LocalDate finalDateStart;
    private LocalDate finalDateEnd;
    private List<String> initialDirection;
    private List<String> finalDirection;
    private BigDecimal initialAmount;
    private BigDecimal finalAmount;
    private List<AccountStatementStatus> status;
    private List<AccountStatementStatus.Description> statusDescription;
    private Long accountID;
    private String partitionKey;
    private LocalDateTime createdAtStart;
    private LocalDateTime createdAtEnd;

    public Map<String, ?> getAsMap() {
        HashMap<String, Object> map = new HashMap<>();

        if (!Objects.isNull(this.currencyCodes)) {
            map.put("currency", this.currencyCodes);
        }
        if (!Objects.isNull(this.statementId)) {
            map.put("statementId", this.statementId);
        }
        if (!Objects.isNull(this.previousStatementID)) {
            map.put("previousStatementID", this.previousStatementID);
        }
        if (!Objects.isNull(this.initialDateStart)) {
            map.put("initialDateStart", this.initialDateStart);
        }
        if (!Objects.isNull(this.initialDateEnd)) {
            map.put("initialDateEnd", this.initialDateEnd);
        }
        if (!Objects.isNull(this.finalDateStart)) {
            map.put("finalDateStart", this.finalDateStart);
        }
        if (!Objects.isNull(this.finalDateEnd)) {
            map.put("finalDateEnd", this.finalDateEnd);
        }
        if (!Objects.isNull(this.initialDirection)) {
            map.put("initialDirection", this.initialDirection);
        }
        if (!Objects.isNull(this.finalDirection)) {
            map.put("finalDirection", this.finalDirection);
        }
        if (!Objects.isNull(this.initialAmount)) {
            map.put("initialAmount", this.initialAmount);
        }
        if (!Objects.isNull(this.finalAmount)) {
            map.put("finalAmount", this.finalAmount);
        }
        if (!Objects.isNull(this.status)) {
            map.put("status", this.status);
        }
        if (!Objects.isNull(this.statusDescription)) {
            map.put("statusDescription", this.statusDescription);
        }
        if (!Objects.isNull(this.accountID)) {
            map.put("accountID", this.accountID);
        }
        if (!Objects.isNull(this.partitionKey)) {
            map.put("partitionKey", this.partitionKey);
        }
        if (!Objects.isNull(this.createdAtStart)){
            map.put("createdAtStart", this.createdAtStart);
        }
        if (!Objects.isNull(this.createdAtEnd)){
            map.put("createdAtEnd", this.createdAtEnd);
        }

        return map;
    }

}
