package pt.jumia.services.brad.domain.entities.filter.user;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.account.User.Status;
import pt.jumia.services.brad.domain.entities.filter.shared.BaseFilters;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserFilters extends BaseFilters {

    private String userName;
    private String email;
    private Long accountID;
    private List<Long> accountIds;
    private List<Long> countryCodes;
    private LocalDateTime createdAt;
    private String hrRole;
    private String permissionType;
    private String mobilePhoneNumber;
    private Status status;

    public Map<String, ?> getAsMap() {
        HashMap<String, Object> map = new HashMap<>();

        if (!Objects.isNull(this.userName)) {
            map.put("userName", this.userName);
        }
        if (!Objects.isNull(this.email)) {
            map.put("email", this.email);
        }
        if (!Objects.isNull(this.accountID)) {
            map.put("accountID", this.accountID);
        }
        if (!Objects.isNull(this.accountIds)) {
            map.put("accountIds", this.accountIds);
        }
        if (!Objects.isNull(this.hrRole)) {
            map.put("hrRole", this.hrRole);
        }
        if (!Objects.isNull(this.permissionType)) {
            map.put("permissionType", this.permissionType);
        }
        if (!Objects.isNull(this.mobilePhoneNumber)) {
            map.put("mobilePhoneNumber", this.mobilePhoneNumber);
        }
        if (!Objects.isNull(this.status)) {
            map.put("status", this.status.name());
        }

        return map;
    }


}
