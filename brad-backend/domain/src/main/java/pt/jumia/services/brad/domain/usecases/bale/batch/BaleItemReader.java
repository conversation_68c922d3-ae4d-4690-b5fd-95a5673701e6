package pt.jumia.services.brad.domain.usecases.bale.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.*;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.executionlogs.CreateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.List;
import java.util.Optional;

/**
 * Thread-safe ItemStreamReader for processing Bales with cursor-based reading.
 * Implements proper state management using ExecutionContext for restart capability.
 * 
 * Key Features:
 * - Memory-efficient cursor-based reading (configurable batch size)
 * - Full restart capability with state persistence
 * - Thread-safe implementation using ExecutionContext
 * - Separated transaction boundaries for execution logs
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BaleItemReader implements ItemStreamReader<Bale> {

    private final BaleRepository baleRepository;
    private final BradBaleRepository bradBaleRepository;
    private final ReadViewEntityUseCase readViewEntityUseCase;
    private final CreateExecutionLogsUseCase createExecutionLogsUseCase;

    // Configuration constants
    private static final int DEFAULT_BATCH_SIZE = 500;
    private static final int MAX_MEMORY_BATCH_SIZE = 1000;
    
    // ExecutionContext keys for state persistence
    private static final String VIEW_ENTITY_INDEX_KEY = "viewEntityIndex";
    private static final String CURRENT_BALE_OFFSET_KEY = "currentBaleOffset";
    private static final String CURRENT_VIEW_ENTITY_KEY = "currentViewEntity";
    private static final String CURRENT_COMPANY_ID_KEY = "currentCompanyId";
    private static final String CURRENT_ENTRY_NO_KEY = "currentEntryNo";
    private static final String INITIALIZED_KEY = "initialized";
    private static final String CURRENT_BATCH_INDEX_KEY = "currentBatchIndex";
    private static final String CURRENT_BATCH_SIZE_KEY = "currentBatchSize";
    
    // Transient state (not persisted)
    private List<ViewEntity> viewEntities;
    private List<Bale> currentBatch;
    private int currentBatchIndex = 0;
    private ExecutionContext executionContext;

    @Override
    public Bale read() throws Exception {
        // Return next bale from current batch if available
        if (currentBatch != null && currentBatchIndex < currentBatch.size()) {
            Bale bale = currentBatch.get(currentBatchIndex++);
            
            // Update state in ExecutionContext for restart capability
            if (executionContext != null) {
                executionContext.putInt(CURRENT_BALE_OFFSET_KEY, 
                    executionContext.getInt(CURRENT_BALE_OFFSET_KEY, 0) + 1);
                executionContext.putInt(CURRENT_BATCH_INDEX_KEY, currentBatchIndex);
            }
            
            return bale;
        }
        
        // Load next batch if current batch is exhausted
        if (loadNextBatch()) {
            return read(); // Recursive call to return first item from new batch
        }
        
        return null; // No more items to read
    }

    @Override
    public void open(ExecutionContext executionContext) throws ItemStreamException {
        this.executionContext = executionContext;
        log.info("Opening Bale ItemReader");
        
        try {
            // Load view entities (this is a small dataset, safe to load in memory)
            viewEntities = readViewEntityUseCase.execute(ViewEntity.EntityType.BALE);
            if (viewEntities.isEmpty()) {
                log.info("No bale view entities found");
                return;
            }
            
            log.info("Found {} bale view entities to process", viewEntities.size());
            
            // Restore state from ExecutionContext if restarting
            if (executionContext != null && executionContext.containsKey(INITIALIZED_KEY)) {
                log.info("Restoring reader state from previous execution");
                
                // Validate and correct corrupted state
                validateAndCorrectState(executionContext);
                
                // Restore batch index if available
                currentBatchIndex = executionContext.getInt(CURRENT_BATCH_INDEX_KEY, 0);
            } else {
                log.info("Starting fresh execution");
                if (executionContext != null) {
                    executionContext.putInt(VIEW_ENTITY_INDEX_KEY, 0);
                    executionContext.putInt(CURRENT_BALE_OFFSET_KEY, 0);
                    executionContext.putInt(CURRENT_BATCH_INDEX_KEY, 0);
                    executionContext.putString(INITIALIZED_KEY, "true");
                }
            }
        } catch (Exception e) {
            throw new ItemStreamException("Failed to open BaleItemReader", e);
        }
    }
    
    @Override
    public void close() throws ItemStreamException {
        log.info("Closing Bale ItemReader");
        currentBatch = null;
        currentBatchIndex = 0;
        viewEntities = null;
        executionContext = null;
    }
    
    @Override
    public void update(ExecutionContext executionContext) throws ItemStreamException {
        this.executionContext = executionContext;
        // Persist current batch state for restart capability
        if (currentBatch != null) {
            executionContext.putInt(CURRENT_BATCH_INDEX_KEY, currentBatchIndex);
            executionContext.putInt(CURRENT_BATCH_SIZE_KEY, currentBatch.size());
        }
        
        log.debug("Updated execution context - view entity index: {}, bale offset: {}, batch index: {}", 
            executionContext.getInt(VIEW_ENTITY_INDEX_KEY, 0),
            executionContext.getInt(CURRENT_BALE_OFFSET_KEY, 0),
            currentBatchIndex);
    }

    /**
     * Loads the next batch of bales using cursor-based reading.
     * Returns true if a batch was loaded, false if no more data available.
     */
    private boolean loadNextBatch() throws Exception {
        if (executionContext == null || viewEntities == null || viewEntities.isEmpty()) {
            return false;
        }
        
        int viewEntityIndex = executionContext.getInt(VIEW_ENTITY_INDEX_KEY, 0);
        int currentOffset = executionContext.getInt(CURRENT_BALE_OFFSET_KEY, 0);
        
        // Check if we've processed all view entities
        if (viewEntityIndex >= viewEntities.size()) {
            log.info("All view entities processed");
            return false;
        }
        
        ViewEntity currentViewEntity = viewEntities.get(viewEntityIndex);
        String currentCompanyId = executionContext.getString(CURRENT_COMPANY_ID_KEY, null);
        Integer currentEntryNo = executionContext.getInt(CURRENT_ENTRY_NO_KEY, 0);
        
        // Initialize for new view entity
        if (currentCompanyId == null || !currentCompanyId.equals(getCompanyId(currentViewEntity))) {
            currentCompanyId = initializeViewEntity(currentViewEntity, executionContext);
            currentOffset = 0;
            currentEntryNo = executionContext.getInt(CURRENT_ENTRY_NO_KEY, 0);
        }
        
        // Load batch with cursor-based reading
        currentBatch = loadBalesBatch(currentViewEntity, currentEntryNo, currentOffset, DEFAULT_BATCH_SIZE);
        currentBatchIndex = 0;
        
        if (currentBatch.isEmpty()) {
            log.info("No more bales for view entity: {}, moving to next", currentViewEntity.getViewName());
            // Move to next view entity
            executionContext.putInt(VIEW_ENTITY_INDEX_KEY, viewEntityIndex + 1);
            executionContext.putInt(CURRENT_BALE_OFFSET_KEY, 0);
            executionContext.putInt(CURRENT_BATCH_INDEX_KEY, 0);
            executionContext.remove(CURRENT_COMPANY_ID_KEY);
            executionContext.remove(CURRENT_ENTRY_NO_KEY);
            
            return loadNextBatch(); // Try next view entity
        }
        
        log.info("Loaded batch of {} bales for view entity: {} (offset: {})", 
            currentBatch.size(), currentViewEntity.getViewName(), currentOffset);
        
        return true;
    }
    
    /**
     * Initializes processing for a new view entity.
     * Separated from reader transaction to avoid transaction boundary issues.
     */
    private String initializeViewEntity(ViewEntity viewEntity, ExecutionContext executionContext) throws Exception {
        log.info("Initializing view entity: {}", viewEntity.getViewName());
        
        String companyId = baleRepository.fetchCompanyId(viewEntity, false);
        if (companyId != null) {
            bradBaleRepository.createPartition(companyId);
        }
        
        Optional<Bale> lastBale = bradBaleRepository.findLastBaleInBradOfCompanyId(companyId);
        Integer entryNo = lastBale.map(Bale::getEntryNo).orElse(0);
        
        // Store state in ExecutionContext
        executionContext.putString(CURRENT_COMPANY_ID_KEY, companyId);
        executionContext.putInt(CURRENT_ENTRY_NO_KEY, entryNo);
        
        log.info("Initialized view entity: {} with company ID: {} starting from entry: {}", 
            viewEntity.getViewName(), companyId, entryNo);
        
        return companyId;
    }
    
    /**
     * Loads a batch of bales using cursor-based reading to prevent memory issues.
     */
    private List<Bale> loadBalesBatch(ViewEntity viewEntity, Integer startEntryNo, int offset, int batchSize) {
        try {
            // Enforce memory-safe batch size before loading
            int safeBatchSize = Math.min(batchSize, MAX_MEMORY_BATCH_SIZE);
            if (safeBatchSize != batchSize) {
                log.warn("Reducing batch size from {} to {} for memory safety", batchSize, safeBatchSize);
            }
            
            // Create execution log in separate transaction (moved out of reader transaction)
            ExecutionLog executionLog = ExecutionLog.builder()
                    .logType(ExecutionLog.ExecutionLogType.BALE)
                    .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
                    .build();
            ExecutionLog currentExecutionLog = createExecutionLogsUseCase.execute(executionLog);
            
            // Use cursor-based reading with memory-safe batch size
            List<Bale> bales = baleRepository.findAllBatched(startEntryNo, viewEntity, false, currentExecutionLog, offset, safeBatchSize);
            
            log.debug("Loaded {} bales for view entity: {} at offset: {}", 
                bales.size(), viewEntity.getViewName(), offset);
            
            return bales;
            
        } catch (Exception e) {
            log.error("Error loading bales batch for view entity: {} at offset: {}", 
                viewEntity.getViewName(), offset, e);
            throw new RuntimeException("Failed to load bales batch for view entity: " + viewEntity.getViewName(), e);
        }
    }
    
    private String getCompanyId(ViewEntity viewEntity) {
        try {
            return baleRepository.fetchCompanyId(viewEntity, false);
        } catch (Exception e) {
            log.warn("Could not fetch company ID for view entity: {}", viewEntity.getViewName(), e);
            return null;
        }
    }
    
    /**
     * Validates and corrects potentially corrupted state from previous execution.
     * This ensures that invalid state values are reset to safe defaults.
     */
    private void validateAndCorrectState(ExecutionContext executionContext) {
        // Validate view entity index
        int viewEntityIndex = executionContext.getInt(VIEW_ENTITY_INDEX_KEY, 0);
        if (viewEntityIndex < 0 || viewEntityIndex >= viewEntities.size()) {
            log.warn("Invalid view entity index: {}, resetting to 0", viewEntityIndex);
            executionContext.putInt(VIEW_ENTITY_INDEX_KEY, 0);
        }
        
        // Validate bale offset
        int currentOffset = executionContext.getInt(CURRENT_BALE_OFFSET_KEY, 0);
        if (currentOffset < 0) {
            log.warn("Invalid bale offset: {}, resetting to 0", currentOffset);
            executionContext.putInt(CURRENT_BALE_OFFSET_KEY, 0);
        }
        
        // Validate batch index
        int batchIndex = executionContext.getInt(CURRENT_BATCH_INDEX_KEY, 0);
        if (batchIndex < 0) {
            log.warn("Invalid batch index: {}, resetting to 0", batchIndex);
            executionContext.putInt(CURRENT_BATCH_INDEX_KEY, 0);
        }
    }
}
