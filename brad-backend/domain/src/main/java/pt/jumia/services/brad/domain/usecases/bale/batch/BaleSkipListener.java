package pt.jumia.services.brad.domain.usecases.bale.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.SkipListener;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.error.BaleErrorClassifier;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
@RequiredArgsConstructor
public class BaleSkipListener implements SkipListener<Bale, Bale> {

    private final ConcurrentMap<String, AtomicInteger> errorCounts = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, ExecutionLog.SyncingError> errorSamples = new ConcurrentHashMap<>();
    private StepExecution stepExecution;

    @BeforeStep
    public void beforeStep(StepExecution stepExecution) {
        this.stepExecution = stepExecution;
        // Clear any previous error tracking
        errorCounts.clear();
        errorSamples.clear();
        log.info("BaleSkipListener initialized for step: {}", stepExecution.getStepName());
    }

    @Override
    public void onSkipInRead(Throwable t) {
        log.error("Skipped item during read phase due to error: {}", t.getMessage(), t);
        
        ExecutionLog.SyncingError syncingError = createSyncingError(t, "READ_PHASE", null);
        trackError(syncingError);
        
        // Store error in step execution context for later aggregation
        storeErrorInStepContext(syncingError);
    }

    @Override
    public void onSkipInWrite(Bale item, Throwable t) {
        log.error("Skipped bale during write phase - Entry: {}, Account: {}, Error: {}", 
                item != null ? item.getEntryNo() : "unknown",
                item != null && item.getAccount() != null ? item.getAccount().getAccountNumber() : "unknown", 
                t.getMessage(), t);
        
        ExecutionLog.SyncingError syncingError = createSyncingError(t, "WRITE_PHASE", item);
        trackError(syncingError);
        
        // Store error in step execution context for later aggregation
        storeErrorInStepContext(syncingError);
    }

    @Override
    public void onSkipInProcess(Bale item, Throwable t) {
        log.warn("Skipped bale during process phase - Entry: {}, Account: {}, Error: {}", 
                item != null ? item.getEntryNo() : "unknown",
                item != null && item.getAccount() != null ? item.getAccount().getAccountNumber() : "unknown", 
                t.getMessage());
        
        ExecutionLog.SyncingError syncingError = createSyncingError(t, "PROCESS_PHASE", item);
        trackError(syncingError);
        
        // Store error in step execution context for later aggregation
        storeErrorInStepContext(syncingError);
    }

    private ExecutionLog.SyncingError createSyncingError(Throwable t, String phase, Bale item) {
        BaleErrorClassifier.ErrorCategory category = BaleErrorClassifier.categorizeError(t);
        
        return ExecutionLog.SyncingError.builder()
                .errorDescription(t.getMessage())
                .errorCategory(category.name())
                .operationContext(phase)
                .affectedRecordId(item != null ? String.valueOf(item.getId()) : null)
                .accountNumber(item != null && item.getAccount() != null ? item.getAccount().getAccountNumber() : null)
                .entryNo(item != null ? item.getEntryNo() : null)
                .build();
    }

    private void trackError(ExecutionLog.SyncingError syncingError) {
        String errorKey = syncingError.getErrorCategory() + ":" + syncingError.getOperationContext();
        
        // Count errors by category and phase
        errorCounts.computeIfAbsent(errorKey, k -> new AtomicInteger(0)).incrementAndGet();
        
        // Keep the first sample of each error type for detailed reporting
        errorSamples.putIfAbsent(errorKey, syncingError);
        
        log.debug("Error tracked - Category: {}, Phase: {}, Count: {}", 
                syncingError.getErrorCategory(), 
                syncingError.getOperationContext(),
                errorCounts.get(errorKey).get());
    }

    private void storeErrorInStepContext(ExecutionLog.SyncingError syncingError) {
        if (stepExecution != null && stepExecution.getExecutionContext() != null) {
            String errorKey = "skip_errors_" + System.currentTimeMillis();
            stepExecution.getExecutionContext().putString(errorKey, 
                    syncingError.getErrorDescription() + "|" + 
                    syncingError.getErrorCategory() + "|" + 
                    syncingError.getOperationContext() + "|" + 
                    (syncingError.getAccountNumber() != null ? syncingError.getAccountNumber() : "") + "|" + 
                    (syncingError.getEntryNo() != null ? syncingError.getEntryNo() : ""));
        }
    }

    public void logErrorSummary() {
        if (errorCounts.isEmpty()) {
            log.info("=== SKIP ERROR SUMMARY: No errors encountered ===");
            return;
        }

        log.info("=== SKIP ERROR SUMMARY ===");
        
        int totalErrors = errorCounts.values().stream()
                .mapToInt(AtomicInteger::get)
                .sum();
        
        log.info("Total skipped items: {}", totalErrors);
        log.info("Error categories: {}", errorCounts.size());
        
        errorCounts.forEach((key, count) -> {
            ExecutionLog.SyncingError sample = errorSamples.get(key);
            log.info("  {} ({}): {} occurrences - Sample: {}", 
                    key, 
                    sample != null ? sample.getErrorCategory() : "UNKNOWN",
                    count.get(), 
                    sample != null ? sample.getErrorDescription() : "No sample");
        });
        
        log.info("=== END SKIP ERROR SUMMARY ===");
    }

    public ConcurrentMap<String, AtomicInteger> getErrorCounts() {
        return errorCounts;
    }

    public ConcurrentMap<String, ExecutionLog.SyncingError> getErrorSamples() {
        return errorSamples;
    }
}
