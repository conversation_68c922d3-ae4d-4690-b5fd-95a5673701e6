package pt.jumia.services.brad.domain.usecases.bale.enrichment;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;

/**
 * Focused service responsible only for determining the correct Currency for bales.
 * Extracted from SyncBradBaleUseCase to follow Single Responsibility Principle.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BaleCurrencyResolver {

    private final ReadCurrenciesUseCase readCurrenciesUseCase;
    
    private static final String USD = "USD";

    /**
     * Determines the appropriate currency for a bale based on account information.
     * 
     * @param bale the bale requiring currency determination
     * @param account the account associated with the bale
     * @return the resolved currency
     * @throws CurrencyResolutionException if currency cannot be determined
     */
    public Currency determineCurrency(Bale bale, Account account) throws CurrencyResolutionException {
        try {
            Currency currency;
            
            if (account.getCurrency() != null && account.getCurrency().getId() != null) {
                log.debug("Using account currency ID: {} for bale: {}", 
                    account.getCurrency().getId(), bale.getEntryNo());
                currency = readCurrenciesUseCase.execute(account.getCurrency().getId());
            } else {
                log.debug("Account currency not available, defaulting to USD for bale: {}", bale.getEntryNo());
                currency = readCurrenciesUseCase.execute(USD);
            }
            
            if (currency == null) {
                throw new CurrencyResolutionException("Currency resolution returned null for bale: " + bale.getEntryNo());
            }
            
            log.debug("Successfully resolved currency: {} for bale: {}", currency.getCode(), bale.getEntryNo());
            return currency;
            
        } catch (CurrencyResolutionException e) {
            throw e; // Re-throw specific exception
        } catch (Exception e) {
            throw new CurrencyResolutionException("Failed to determine currency for bale: " + bale.getEntryNo(), e);
        }
    }
}
