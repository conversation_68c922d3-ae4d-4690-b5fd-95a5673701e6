package pt.jumia.services.brad.domain.usecases.bale.brad;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.exceptions.InvalidEntityException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.usecases.bale.BaleProcessingException;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.BaleAccountEnricher;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.BaleCurrencyResolver;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.BaleFxRateEnricher;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.CurrencyResolutionException;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.FxRateUnavailableException;
import pt.jumia.services.brad.domain.usecases.bale.validation.BaleValidator;

import java.util.List;
import java.util.Set;

/**
 * Refactored SyncBradBaleUseCase acting as an orchestrator for bale processing.
 * 
 * Responsibilities:
 * - Orchestrates bale processing workflow using focused service components
 * - Maintains Spring Batch job triggering capabilities
 * - Provides legacy batch processing support during migration
 * 
 * Architecture follows audit recommendations:
 * - Delegates validation to BaleValidator
 * - Delegates account enrichment to BaleAccountEnricher
 * - Delegates currency resolution to BaleCurrencyResolver
 * - Delegates FX rate enrichment to BaleFxRateEnricher
 */
@Slf4j
@AllArgsConstructor
@Component
public class SyncBradBaleUseCase {

    private final BradBaleRepository bradBaleRepository;
    
    // Focused service components (audit recommendation)
    private final BaleValidator baleValidator;
    private final BaleAccountEnricher baleAccountEnricher;
    private final BaleCurrencyResolver baleCurrencyResolver;
    private final BaleFxRateEnricher baleFxRateEnricher;

    public List<Bale> execute(List<Bale> baleList) throws DatabaseErrorsException {
        if (baleList == null || baleList.isEmpty()) {
            log.debug("No bales to sync");
            return List.of();
        }

        log.info("Syncing {} bales to Brad database", baleList.size());

        try {
            List<Bale> syncedBales = bradBaleRepository.sync(baleList);

            log.info("Successfully synced {} bales to Brad database", syncedBales.size());
            return syncedBales;

        } catch (Exception e) {
            log.error("Failed to sync {} bales to Brad database", baleList.size(), e);
            throw new DatabaseErrorsException("Bale sync failed: " + e.getMessage());
        }
    }

    /**
     * Processes a single bale using focused service components (orchestrator pattern).
     * Designed for Spring Batch ItemProcessor usage.
     * 
     * @param bale the bale to process
     * @return the enriched bale
     * @throws InvalidEntityException if validation fails
     * @throws NotFoundException if account not found
     * @throws CurrencyResolutionException if currency resolution fails
     * @throws FxRateUnavailableException if FX rates unavailable
     */
    public Bale processBale(Bale bale) throws InvalidEntityException, NotFoundException, 
            CurrencyResolutionException, FxRateUnavailableException {
        if (bale == null) {
            throw new IllegalArgumentException("Bale cannot be null");
        }

        log.debug("Processing bale with entry number: {}", bale.getEntryNo());

        // Step 1: Validate bale data
        baleValidator.validateBale(bale);

        // Step 2: Enrich with account information
        Account account = baleAccountEnricher.fetchAccount(bale);

        // Step 3: Resolve transaction currency
        Currency currency = baleCurrencyResolver.determineCurrency(bale, account);

        // Step 4: Enrich with FX rates
        Set<FxRate> fxRates = baleFxRateEnricher.enrichWithFxRates(bale, currency);

        // Step 5: Build enriched bale
        Bale enrichedBale = bale.toBuilder()
                .account(account)
                .transactionCurrency(currency)
                .fxRates(fxRates)
                .build();

        log.debug("Successfully processed bale with entry number: {}", bale.getEntryNo());
        return enrichedBale;
    }

    /**
     * Legacy method for adding FX rates to existing bales.
     * Delegates to focused service component.
     * 
     * @param bale the bale to add FX rates to
     * @return the bale with added FX rates
     * @throws BaleProcessingException if FX rate addition fails
     */

    public Bale addFxRates(Bale bale) throws BaleProcessingException {
        try {
            return baleFxRateEnricher.addFxRates(bale);
        } catch (FxRateUnavailableException e) {
            throw new BaleProcessingException("Failed to add FX rates to bale: " + 
                (bale != null ? bale.getEntryNo() : "null"), e);
        }
    }
}
