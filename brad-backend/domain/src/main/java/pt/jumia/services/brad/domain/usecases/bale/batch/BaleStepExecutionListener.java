package pt.jumia.services.brad.domain.usecases.bale.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.error.BaleErrorClassifier;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Component
@RequiredArgsConstructor
public class BaleStepExecutionListener implements StepExecutionListener {

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    @Override
    public void beforeStep(StepExecution stepExecution) {
        Long executionLogId = null;
        try {
            executionLogId = stepExecution.getJobExecution().getExecutionContext().getLong("executionLogId");
        } catch (Exception e) {
            log.debug("No executionLogId found in job execution context: {}", e.getMessage());
        }
        
        log.info("=== STARTING BALE PROCESSING STEP (ExecutionLog: {}) ===", executionLogId != null ? executionLogId : "none");
        log.info("Step start time: {}", LocalDateTime.now().format(TIME_FORMATTER));
        log.info("Step configuration - Chunk size: {}, Skip limit: {}, Retry limit: {}", 
                 500, 50, 3);
        
        // Store start time in execution context
        stepExecution.getExecutionContext().putLong("stepStartTime", System.currentTimeMillis());
    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        Long executionLogId = null;
        try {
            executionLogId = stepExecution.getJobExecution().getExecutionContext().getLong("executionLogId");
        } catch (Exception e) {
            log.debug("No executionLogId found in job execution context: {}", e.getMessage());
        }
        
        // Calculate execution time
        Long startTime = stepExecution.getExecutionContext().getLong("stepStartTime");
        String duration = startTime != null ? 
                String.format("%.2f seconds", (System.currentTimeMillis() - startTime) / 1000.0) : "unknown";
        
        long readCount = stepExecution.getReadCount();
        long writeCount = stepExecution.getWriteCount();
        long skipCount = stepExecution.getSkipCount();
        long processSkipCount = stepExecution.getProcessSkipCount();
        long writeSkipCount = stepExecution.getWriteSkipCount();
        long readSkipCount = stepExecution.getReadSkipCount();
        long commitCount = stepExecution.getCommitCount();
        long rollbackCount = stepExecution.getRollbackCount();
        
        log.info("=== BALE PROCESSING STEP SUMMARY (ExecutionLog: {}) ===", executionLogId != null ? executionLogId : "none");
        log.info("Step end time: {}", LocalDateTime.now().format(TIME_FORMATTER));
        log.info("Step duration: {}", duration);
        log.info("=== PROCESSING STATISTICS ===");
        log.info("Items read: {}", readCount);
        log.info("Items written: {}", writeCount);
        log.info("Items skipped: {} (Read: {}, Process: {}, Write: {})", 
                skipCount, readSkipCount, processSkipCount, writeSkipCount);
        log.info("Commits: {}", commitCount);
        log.info("Rollbacks: {}", rollbackCount);
        
        // Calculate performance metrics
        double successRate = readCount > 0 ? (double) writeCount / readCount * 100 : 0;
        double errorRate = readCount > 0 ? (double) skipCount / readCount * 100 : 0;
        double throughput = startTime != null && System.currentTimeMillis() - startTime > 0 ? 
                (double) readCount / ((System.currentTimeMillis() - startTime) / 1000.0) : 0;
        
        log.info("=== PERFORMANCE METRICS ===");
        log.info("Success rate: {:.2f}%", successRate);
        log.info("Error rate: {:.2f}%", errorRate);
        log.info("Throughput: {:.2f} items/second", throughput);
        
        // Detailed error analysis
        if (stepExecution.getFailureExceptions() != null && !stepExecution.getFailureExceptions().isEmpty()) {
            log.error("=== STEP FAILURE ANALYSIS ===");
            log.error("Step completed with {} failure exceptions:", stepExecution.getFailureExceptions().size());
            
            stepExecution.getFailureExceptions().forEach(exception -> {
                BaleErrorClassifier.ErrorCategory category = BaleErrorClassifier.categorizeError(exception);
                log.error("  - [{}] {}: {}", 
                        category.name(),
                        exception.getClass().getSimpleName(), 
                        exception.getMessage());
            });
        }
        
        // Skip analysis
        if (skipCount > 0) {
            log.warn("=== SKIP ANALYSIS ===");
            log.warn("Total items skipped: {}", skipCount);
            if (readSkipCount > 0) {
                log.warn("Read phase skips: {} ({:.2f}% of total skips)", 
                        readSkipCount, (double) readSkipCount / skipCount * 100);
            }
            if (processSkipCount > 0) {
                log.warn("Process phase skips: {} ({:.2f}% of total skips)", 
                        processSkipCount, (double) processSkipCount / skipCount * 100);
            }
            if (writeSkipCount > 0) {
                log.warn("Write phase skips: {} ({:.2f}% of total skips)", 
                        writeSkipCount, (double) writeSkipCount / skipCount * 100);
            }
        }
        
        // Memory usage (if available)
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        log.info("=== MEMORY USAGE ===");
        log.info("Memory used: {} MB", usedMemory / 1024 / 1024);
        log.info("Memory free: {} MB", freeMemory / 1024 / 1024);
        log.info("Memory total: {} MB", totalMemory / 1024 / 1024);
        
        // Quality assessment
        if (readCount > 0) {
            String qualityAssessment = assessDataQuality(successRate, errorRate, skipCount, readCount);
            log.info("=== DATA QUALITY ASSESSMENT ===");
            log.info("Quality score: {}", qualityAssessment);
        }
        
        log.info("=== END BALE PROCESSING STEP SUMMARY ===");
        
        // Determine exit status
        if (stepExecution.getStatus().isUnsuccessful()) {
            log.error("Bale processing step failed for ExecutionLog: {}", executionLogId);
            return ExitStatus.FAILED;
        } else if (skipCount > 0) {
            log.warn("Bale processing step completed with skipped items for ExecutionLog: {}", executionLogId);
            return new ExitStatus("COMPLETED_WITH_SKIPS");
        } else {
            log.info("Bale processing step completed successfully for ExecutionLog: {}", executionLogId);
            return ExitStatus.COMPLETED;
        }
    }

    private String assessDataQuality(double successRate, double errorRate, long skipCount, long readCount) {
        if (successRate >= 95.0) {
            return "EXCELLENT (Success rate: " + String.format("%.2f", successRate) + "%)";
        } else if (successRate >= 85.0) {
            return "GOOD (Success rate: " + String.format("%.2f", successRate) + "%)";
        } else if (successRate >= 70.0) {
            return "FAIR (Success rate: " + String.format("%.2f", successRate) + "%)";
        } else if (successRate >= 50.0) {
            return "POOR (Success rate: " + String.format("%.2f", successRate) + "%)";
        } else {
            return "CRITICAL (Success rate: " + String.format("%.2f", successRate) + "%)";
        }
    }
}
