package pt.jumia.services.brad.domain.entities.filter.apilog;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.shared.BaseFilters;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApiLogFilters extends BaseFilters {

    private List<String> logType;
    private String relatedEntityId;
    private List<String> logStatus;
    private String request;
    private LocalDateTime createdAtStart;
    private LocalDateTime createdAtEnd;

    public Map<String, ?> getAsMap(){
        HashMap<String, Object> map = new HashMap<>();

        if (!Objects.isNull(this.logType)){
            map.put("logType", this.logType);
        }
        if (!Objects.isNull(this.relatedEntityId)){
            map.put("relatedEntityId", this.relatedEntityId);
        }
        if (!Objects.isNull(this.logStatus)){
            map.put("logStatus", this.logStatus);
        }
        if (!Objects.isNull(this.request)){
            map.put("request", this.request);
        }
        if (!Objects.isNull(this.createdAtStart)){
            map.put("createdAtStart", this.createdAtStart);
        }
        if (!Objects.isNull(this.createdAtEnd)){
            map.put("createdAtEnd", this.createdAtEnd);
        }
        if (!Objects.isNull(this.getSelectedFields())){
            map.put("selectedFields", this.getSelectedFields());
        }
        return map;
    }


}
