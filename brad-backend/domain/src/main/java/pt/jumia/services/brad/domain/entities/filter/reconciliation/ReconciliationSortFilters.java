package pt.jumia.services.brad.domain.entities.filter.reconciliation;


import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class ReconciliationSortFilters extends SortFilters<Reconciliation.SortingFields> {

    public ReconciliationSortFilters(Reconciliation.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
