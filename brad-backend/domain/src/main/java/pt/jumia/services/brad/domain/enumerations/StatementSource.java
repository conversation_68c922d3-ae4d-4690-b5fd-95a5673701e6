package pt.jumia.services.brad.domain.enumerations;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Getter
@AllArgsConstructor
public enum StatementSource {

        API("API"),
        SFTP("SFTP"),
        MANUAL_UPLOAD("MANUAL_UPLOAD");

        private final String value;

        public static List<StatementSource> getValues() {
                List<StatementSource> values = new ArrayList<>(StatementSource.values().length);
                Collections.addAll(values, StatementSource.values());
                return values;
        }
}
