package pt.jumia.services.brad.domain.usecases.bale.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.error.BaleErrorClassifier;
import pt.jumia.services.brad.domain.usecases.executionlogs.ReadExecutionLogsUseCase;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class BaleErrorReporter {

    private final ReadExecutionLogsUseCase readExecutionLogsUseCase;

    /**
     * Generates a comprehensive error summary for a given ExecutionLog
     * @param executionLogId The ID of the ExecutionLog to analyze
     * @return Formatted error summary string
     */
    public String generateErrorSummary(Long executionLogId) {
        try {
            ExecutionLog executionLog = readExecutionLogsUseCase.execute(executionLogId);
            return generateErrorSummary(executionLog);
        } catch (Exception e) {
            log.error("Failed to generate error summary for ExecutionLog {}: {}", executionLogId, e.getMessage());
            return "Error generating summary: " + e.getMessage();
        }
    }

    /**
     * Generates a comprehensive error summary for a given ExecutionLog
     * @param executionLog The ExecutionLog to analyze
     * @return Formatted error summary string
     */
    public String generateErrorSummary(ExecutionLog executionLog) {
        if (executionLog == null) {
            return "No ExecutionLog provided";
        }

        StringBuilder summary = new StringBuilder();
        
        summary.append("=== BALE SYNC ERROR SUMMARY ===\n");
        summary.append(String.format("ExecutionLog ID: %d\n", executionLog.getId()));
        summary.append(String.format("Log Type: %s\n", executionLog.getLogType()));
        summary.append(String.format("Status: %s\n", executionLog.getLogStatus()));
        summary.append(String.format("Records Amount: %d\n", executionLog.getRecordsAmount() != null ? executionLog.getRecordsAmount() : 0));
        
        if (executionLog.getExecutionStartTime() != null) {
            summary.append(String.format("Started: %s\n", executionLog.getExecutionStartTime()));
        }
        
        if (executionLog.getExecutionEndTime() != null) {
            summary.append(String.format("Ended: %s\n", executionLog.getExecutionEndTime()));
        }
        
        summary.append("\n");

        List<ExecutionLog.SyncingError> errors = executionLog.getErrors();
        if (errors == null || errors.isEmpty()) {
            summary.append("No errors recorded.\n");
        } else {
            summary.append(generateDetailedErrorAnalysis(errors));
        }

        summary.append("=== END ERROR SUMMARY ===\n");
        
        return summary.toString();
    }

    /**
     * Generates detailed error analysis from a list of SyncingErrors
     */
    private String generateDetailedErrorAnalysis(List<ExecutionLog.SyncingError> errors) {
        StringBuilder analysis = new StringBuilder();
        
        analysis.append(String.format("Total Errors: %d\n\n", errors.size()));
        
        // Group errors by category
        Map<String, List<ExecutionLog.SyncingError>> errorsByCategory = errors.stream()
                .collect(Collectors.groupingBy(
                        error -> error.getErrorCategory() != null ? error.getErrorCategory() : "UNKNOWN"
                ));
        
        analysis.append("=== ERROR BREAKDOWN BY CATEGORY ===\n");
        errorsByCategory.forEach((category, categoryErrors) -> {
            analysis.append(String.format("%s: %d errors (%.1f%%)\n", 
                    category, 
                    categoryErrors.size(),
                    (double) categoryErrors.size() / errors.size() * 100));
            
            // Show sample errors for each category
            categoryErrors.stream()
                    .limit(3)
                    .forEach(error -> analysis.append(String.format("  - %s\n", error.getErrorDescription())));
            
            if (categoryErrors.size() > 3) {
                analysis.append(String.format("  ... and %d more\n", categoryErrors.size() - 3));
            }
            analysis.append("\n");
        });
        
        // Group errors by operation context
        Map<String, List<ExecutionLog.SyncingError>> errorsByContext = errors.stream()
                .collect(Collectors.groupingBy(
                        error -> error.getOperationContext() != null ? error.getOperationContext() : "UNKNOWN"
                ));
        
        analysis.append("=== ERROR BREAKDOWN BY OPERATION PHASE ===\n");
        errorsByContext.forEach((context, contextErrors) -> {
            analysis.append(String.format("%s: %d errors (%.1f%%)\n", 
                    context, 
                    contextErrors.size(),
                    (double) contextErrors.size() / errors.size() * 100));
        });
        analysis.append("\n");
        
        // Critical error analysis
        List<ExecutionLog.SyncingError> criticalErrors = errors.stream()
                .filter(error -> "CRITICAL".equals(error.getErrorCategory()))
                .collect(Collectors.toList());
        
        if (!criticalErrors.isEmpty()) {
            analysis.append("=== CRITICAL ERRORS REQUIRE IMMEDIATE ATTENTION ===\n");
            criticalErrors.forEach(error -> {
                analysis.append(String.format("- [%s] %s", 
                        error.getOperationContext(),
                        error.getErrorDescription()));
                
                if (error.getAccountNumber() != null) {
                    analysis.append(String.format(" (Account: %s)", error.getAccountNumber()));
                }
                
                if (error.getEntryNo() != null) {
                    analysis.append(String.format(" (Entry: %s)", error.getEntryNo()));
                }
                
                analysis.append("\n");
            });
            analysis.append("\n");
        }
        
        // Account-specific error analysis
        Map<String, List<ExecutionLog.SyncingError>> errorsByAccount = errors.stream()
                .filter(error -> error.getAccountNumber() != null)
                .collect(Collectors.groupingBy(ExecutionLog.SyncingError::getAccountNumber));
        
        if (!errorsByAccount.isEmpty()) {
            analysis.append("=== ERRORS BY ACCOUNT ===\n");
            errorsByAccount.entrySet().stream()
                    .sorted((e1, e2) -> Integer.compare(e2.getValue().size(), e1.getValue().size()))
                    .limit(10)
                    .forEach(entry -> {
                        analysis.append(String.format("Account %s: %d errors\n", 
                                entry.getKey(), entry.getValue().size()));
                    });
            
            if (errorsByAccount.size() > 10) {
                analysis.append(String.format("... and %d more accounts with errors\n", 
                        errorsByAccount.size() - 10));
            }
            analysis.append("\n");
        }
        
        // Recovery recommendations
        analysis.append("=== RECOVERY RECOMMENDATIONS ===\n");
        analysis.append(generateRecoveryRecommendations(errors));
        
        return analysis.toString();
    }

    /**
     * Generates recovery recommendations based on error patterns
     */
    private String generateRecoveryRecommendations(List<ExecutionLog.SyncingError> errors) {
        StringBuilder recommendations = new StringBuilder();
        
        Map<String, Long> errorCategories = errors.stream()
                .collect(Collectors.groupingBy(
                        error -> error.getErrorCategory() != null ? error.getErrorCategory() : "UNKNOWN",
                        Collectors.counting()
                ));
        
        if (errorCategories.containsKey("CRITICAL")) {
            recommendations.append("1. IMMEDIATE: Address critical errors before retrying\n");
            recommendations.append("   - Check database connections and timeouts\n");
            recommendations.append("   - Verify system memory and resources\n");
            recommendations.append("   - Review authentication and permissions\n");
        }
        
        if (errorCategories.containsKey("RECOVERABLE")) {
            recommendations.append("2. RECOVERABLE: These errors can be addressed by:\n");
            recommendations.append("   - Updating missing account information\n");
            recommendations.append("   - Ensuring FX rates are available\n");
            recommendations.append("   - Validating currency configurations\n");
        }
        
        if (errorCategories.containsKey("DATA_ERROR")) {
            recommendations.append("3. DATA_ERROR: Review and correct data issues:\n");
            recommendations.append("   - Validate input data formats\n");
            recommendations.append("   - Check for missing required fields\n");
            recommendations.append("   - Verify data consistency\n");
        }
        
        // Check for patterns that suggest specific actions
        long readErrors = errors.stream()
                .filter(error -> error.getOperationContext() != null && 
                        error.getOperationContext().contains("READ"))
                .count();
        
        long processErrors = errors.stream()
                .filter(error -> error.getOperationContext() != null && 
                        error.getOperationContext().contains("PROCESS"))
                .count();
        
        long writeErrors = errors.stream()
                .filter(error -> error.getOperationContext() != null && 
                        error.getOperationContext().contains("WRITE"))
                .count();
        
        if (readErrors > processErrors && readErrors > writeErrors) {
            recommendations.append("4. READ PHASE: Most errors in read phase suggest:\n");
            recommendations.append("   - Source data quality issues\n");
            recommendations.append("   - Connection problems with source system\n");
        } else if (processErrors > readErrors && processErrors > writeErrors) {
            recommendations.append("4. PROCESS PHASE: Most errors in process phase suggest:\n");
            recommendations.append("   - Business logic validation failures\n");
            recommendations.append("   - Missing reference data\n");
        } else if (writeErrors > readErrors && writeErrors > processErrors) {
            recommendations.append("4. WRITE PHASE: Most errors in write phase suggest:\n");
            recommendations.append("   - Target database issues\n");
            recommendations.append("   - Constraint violations\n");
        }
        
        recommendations.append("\n5. MONITORING: Review logs for detailed error context\n");
        recommendations.append("6. RETRY: Consider rerunning after addressing above issues\n");
        
        return recommendations.toString();
    }

    /**
     * Logs a comprehensive error summary to the application logs
     */
    public void logErrorSummary(Long executionLogId) {
        String summary = generateErrorSummary(executionLogId);
        log.info("BALE SYNC ERROR SUMMARY:\n{}", summary);
    }

    /**
     * Logs a comprehensive error summary to the application logs
     */
    public void logErrorSummary(ExecutionLog executionLog) {
        String summary = generateErrorSummary(executionLog);
        log.info("BALE SYNC ERROR SUMMARY:\n{}", summary);
    }
}
