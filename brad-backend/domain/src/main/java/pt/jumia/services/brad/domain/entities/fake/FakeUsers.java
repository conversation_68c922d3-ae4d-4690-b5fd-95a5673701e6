package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.User;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import pt.jumia.services.brad.domain.entities.account.User.Status;

public interface FakeUsers {

    User FAKE_USER = User.builder()
            .name("fakeName")
            .email("fakeEmail")
            .account(FakeAccounts.getFakeAccounts(1, null).get(0))
            .createdAt(LocalDateTime.now())
            .createdBy("fakeUser")
            .updatedAt(LocalDateTime.now())
            .updatedBy("fakeUser")
            .hrRole("fakeRole")
            .permissionType("fakePermission")
            .mobilePhoneNumber("*********")
            .status(Status.ACTIVE)
            .build();



    static List<User> getFakeUsers(int amount) {
        List<Account> fakeAccounts = FakeAccounts.getFakeAccounts(amount, null);
        List<User> fakeContacts = new ArrayList<>();
        for (int i = 1; i <= amount; i++) {
            fakeContacts.add(FAKE_USER.toBuilder()
                    .name(FAKE_USER.getName() + i)
                    .email(FAKE_USER.getEmail() + i)
                    .account(fakeAccounts.get(i - 1))
                    .status(Status.ACTIVE)
                    .createdAt(LocalDateTime.now())
                    .createdBy(FAKE_USER.getCreatedBy() + i)
                    .updatedAt(LocalDateTime.now())
                    .updatedBy(FAKE_USER.getUpdatedBy() + i)
                    .build());
        }

        return fakeContacts;
    }
}
