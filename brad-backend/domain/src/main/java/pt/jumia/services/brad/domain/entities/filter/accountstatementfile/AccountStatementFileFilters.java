package pt.jumia.services.brad.domain.entities.filter.accountstatementfile;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;

@Data
@Builder(toBuilder = true)
@EqualsAndHashCode
@ToString(callSuper = true)
public class AccountStatementFileFilters {

    private List<Long> ids;
    private String name;
    private ProcessingStatus processingStatus;
    private Long accountId;
    private Long statementId;
    private LocalDateTime createdAtFrom;
    private LocalDateTime createdAtTo;

    public Map<String, ?> getAsMap() {

        HashMap<String, Object> map = new HashMap<>();

        if (!Objects.isNull(this.ids)) {
            map.put("ids", this.ids);
        }
        if (!Objects.isNull(this.name)) {
            map.put("name", this.name);
        }
        if (!Objects.isNull(this.processingStatus)) {
            map.put("processingStatus", this.processingStatus.name());
        }
        if (!Objects.isNull(this.accountId)) {
            map.put("accountId", this.accountId);
        }
        if (!Objects.isNull(this.statementId)) {
            map.put("statementId", this.statementId);
        }
        if (!Objects.isNull(this.createdAtFrom)) {
            map.put("createdAtFrom", this.createdAtFrom);
        }
        if (!Objects.isNull(this.createdAtTo)) {
            map.put("createdAtTo", this.createdAtTo);
        }

        return map;
    }

}
