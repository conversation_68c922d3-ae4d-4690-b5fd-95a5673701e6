package pt.jumia.services.brad.domain.entities.filter.apilog;


import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.ApiLog;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class ApiLogSortFilters extends SortFilters<ApiLog.SortingFields> {

    public ApiLogSortFilters(ApiLog.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
