package pt.jumia.services.brad.domain.entities;

import lombok.Builder;
import lombok.Getter;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Value
@Builder(toBuilder = true)
public class Bale {

    Long id;
    String idCompany;
    Account account;
    Integer entryNo;
    String documentNo;
    String documentType;
    LocalDate postingDate;
    String accountPostingGroup;
    String description;
    String sourceCode;
    String reasonCode;
    String busLine;
    String department;
    Direction direction;
    Set<FxRate> fxRates;
    BigDecimal amount;
    BigDecimal remainingAmount;
    Currency transactionCurrency;
    BigDecimal amountLcy;
    String balanceAccountNumber;
    String balanceAccountType;
    Boolean isOpen;
    Boolean isReversed;
    String postedBy;
    String externalDocumentNo;
    String baleTimestamp;
    String accountTimestamp;
    ReconcileStatus reconcileStatus;

    Reconciliation reconciliation;

    public BigDecimal getAmountLocalCurrency() {
        if (this.transactionCurrency == null) {
            return null;
        }
        return convertCurrency(this.amount, this.transactionCurrency.getCode(),
                this.getAccount().getCountry().getCurrency().getCode());
    }

    public BigDecimal getAmountUsd() {
        if (this.transactionCurrency == null) {
            return null;
        }
        return convertCurrency(this.amount, this.transactionCurrency.getCode(), "USD");
    }

    private BigDecimal convertCurrency(BigDecimal amount, String currency, String quoteCurrency) {
        FxRate fxRate = this.fxRates.stream().filter(fx ->
                        fx.getBaseCurrency().getCode().equals(currency) &&
                                fx.getQuoteCurrency().getCode().equals(quoteCurrency))
                .findFirst().orElse(null);

        return fxRate != null ? fxRate.getBid().multiply(amount) : currency.equals(quoteCurrency) ? amount : null;
    }

    @Getter
    public enum GroupingFields {
        ID_COMPANY("idCompany", false, true),
        ACCOUNT_NUMBER("accountNumber", false, true),
        ACCOUNT_NAME("accountName", false, true),
        ACCOUNT_TYPE("accountType", false, true),
        KYRIBA_REFERENCE("kyribaReference", false, true),
        ENTRY_NO("entryNo", false, true),
        DOCUMENT_NO("documentNo", true, true),
        DOCUMENT_TYPE("documentType", false, true),
        POSTING_DATE("postingDate", true, true),
        BANK_ACCOUNT("bankAccount.id", false, true),
        BANK_ACCOUNT_POSTING_GROUP("bankAccountPostingGroup", false, true),
        DESCRIPTION("description", false, true),
        SOURCE_CODE("sourceCode", true, true),
        REASON_CODE("reasonCode", false, true),
        BUS_LINE("busLine", false, true),
        DEPARTMENT("department", false, true),
        DIRECTION("direction", true, true),
        AMOUNT("amount", true, true),
        REMAINING_AMOUNT("remainingAmount", false, true),
        TRANSACTION_CURRENCY("transactionCurrency.code", false, true),
        AMOUNT_LCY("amountLcy", false, true),
        BALANCE_ACCOUNT_NO("balanceAccountNo", false, true),
        BALANCE_ACCOUNT_TYPE("balanceAccountType", false, true),
        IS_OPEN("isOpen", false, true),
        REVERSED("isReversed", false, true),
        POSTED_BY("postedBy", false, true),
        EXTERNAL_DOCUMENT_NO("externalDocumentNo", true, true),
        BALE_TIMESTAMP("baleTimestamp", false, true),
        BANK_ACCOUNT_TIMESTAMP("bankAccountTimestamp", false, true),
        RECONCILIATION_CREATOR("creator", true, false),
        RECONCILIATION_CREATION_DATE("creationDate", true, false);

        public final String groupField;
        public final boolean active;
        public final boolean belongsToMainEntity;

        GroupingFields(String groupField, boolean active, boolean belongsToMainEntity) {
            this.groupField = groupField;
            this.active = active;
            this.belongsToMainEntity = belongsToMainEntity;
        }

        public static GroupingFields fromValue(String value) {
            for (GroupingFields groupingFields : GroupingFields.values()) {
                if (groupingFields.name().equalsIgnoreCase(value)) {
                    return groupingFields;
                }
            }
            throw new IllegalArgumentException("Invalid GroupingFields value: " + value);
        }

        public static List<GroupingFields> fromValues(List<String> values) {
            return values.stream()
                    .map(GroupingFields::fromValue)
                    .collect(Collectors.toList());
        }
    }

    @Getter
    public enum SelectFields implements BaseSelectFields {

            ID("id", "id"),
            ID_COMPANY("idCompany", "idCompany"),
            ACCOUNT_NUMBER("accountNumber", "accountNumber"),
            ACCOUNT_NAME("accountName", "accountName"),
            ACCOUNT_TYPE("accountType", "accountType"),
            CURRENCY("currency", "currency"),
            KYRIBA_REFERENCE("kyribaReference", "kyribaReference"),
            ENTRY_NO("entryNo", "entryNo"),
            DOCUMENT_NO("documentNo", "documentNo"),
            DOCUMENT_TYPE("documentType", "documentType"),
            POSTING_DATE("postingDate", "postingDate"),
            BANK_ACCOUNT("bankAccount", "bankAccount"),
            BANK_ACCOUNT_POSTING_GROUP("bankAccountPostingGroup", "bankAccountPostingGroup"),
            DESCRIPTION("description", "description"),
            SOURCE_CODE("sourceCode", "sourceCode"),
            REASON_CODE("reasonCode", "reasonCode"),
            BUS_LINE("busLine", "busLine"),
            DEPARTMENT("department", "department"),
            AMOUNT("amount", "amount"),
            REMAINING_AMOUNT("remainingAmount", "remainingAmount"),
            TRANSACTION_CURRENCY("transactionCurrency", "transactionCurrency"),
            AMOUNT_LCY("amountLcy", "amountLcy"),
            BALANCE_ACCOUNT_NO("balanceAccountNo", "balanceAccountNo"),
            BALANCE_ACCOUNT_TYPE("balanceAccountType", "balanceAccountType"),
            IS_OPEN("isOpen", "isOpen"),
            REVERSED("isReversed", "isReversed"),
            POSTED_BY("postedBy", "postedBy"),
            EXTERNAL_DOCUMENT_NO("externalDocumentNo", "externalDocumentNo"),
            BALE_TIMESTAMP("baleTimestamp", "baleTimestamp"),
            BANK_ACCOUNT_TIMESTAMP("bankAccountTimestamp", "bankAccountTimestamp");

            public final String queryField;
            public final String selectCode;

            private static final List<String> ignoredFields = List.of("select");

            SelectFields(String queryField, String selectCode) {
                this.queryField = queryField;
                this.selectCode = selectCode;
            }



    }

    public enum SortingFields {
        ID,
        ID_COMPANY,
        ACCOUNT_NUMBER,
        ACCOUNT_NAME,
        ACCOUNT_TYPE,
        CURRENCY,
        KYRIBA_REFERENCE,
        ENTRY_NO,
        DOCUMENT_NO,
        DOCUMENT_TYPE,
        POSTING_DATE,
        BANK_ACCOUNT,
        BANK_ACCOUNT_POSTING_GROUP,
        DESCRIPTION,
        SOURCE_CODE,
        REASON_CODE,
        BUS_LINE,
        DEPARTMENT,
        DIRECTION,
        AMOUNT,
        REMAINING_AMOUNT,
        TRANSACTION_CURRENCY,
        AMOUNT_LCY,
        BALANCE_ACCOUNT_NO,
        BALANCE_ACCOUNT_TYPE,
        IS_OPEN,
        REVERSED,
        POSTED_BY,
        EXTERNAL_DOCUMENT_NO,
        BALE_TIMESTAMP,
        BANK_ACCOUNT_TIMESTAMP
    }


}
