package pt.jumia.services.brad.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NonNull;
import lombok.Value;

import java.time.LocalDateTime;

/**
 * Business representation of View Entity
 */
@Value
@Builder(toBuilder = true)
@AllArgsConstructor
public class ViewEntity {

    Long id;

    EntityType entityType;

    @NonNull
    String driver;

    @NonNull
    String jdbcConnectionUrl;

    @NonNull
    String databaseName;

    @NonNull
    String schemaName;

    @NonNull
    String viewName;

    LocalDateTime createdAt;

    String createdBy;

    LocalDateTime updatedAt;

    String updatedBy;

    public ViewEntity withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .updatedAt(null)
                .updatedBy(null)
                .build();
    }

    public enum EntityType {
        BALE,
        FX_RATE
    }

}
