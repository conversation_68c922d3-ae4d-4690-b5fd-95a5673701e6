package pt.jumia.services.brad.domain.usecases.bale.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.exceptions.InvalidEntityException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.usecases.bale.BaleProcessingException;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCase;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.CurrencyResolutionException;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.FxRateUnavailableException;

/**
 * Enhanced BaleItemProcessor with specific exception handling.
 * Implements audit recommendations for fault tolerance and error handling.
 * 
 * Exception Handling Strategy:
 * - InvalidEntityException: Skip invalid bales (business rule violations)
 * - NotFoundException: Skip bales with missing account data
 * - CurrencyResolutionException: Skip bales with currency issues
 * - FxRateUnavailableException: Skip bales with FX rate problems
 * - Other exceptions: Propagate for retry/fail handling
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BaleItemProcessor implements ItemProcessor<Bale, Bale> {

    private final SyncBradBaleUseCase syncBradBaleUseCase;

    @Override
    public Bale process(Bale bale) throws Exception {
        if (bale == null) {
            log.debug("Received null bale, skipping");
            return null;
        }

        try {
            log.debug("Processing bale with entry number: {}", bale.getEntryNo());

            Bale enrichedBale = syncBradBaleUseCase.processBale(bale);

            log.debug("Successfully processed bale with entry number: {}", bale.getEntryNo());
            return enrichedBale;

        } catch (InvalidEntityException e) {
            // Skip bales that fail validation (business rule violations)
            log.warn("Skipping bale {} due to validation failure: {}", bale.getEntryNo(), e.getMessage());
            return null;
            
        } catch (NotFoundException e) {
            // Skip bales with missing account data
            log.warn("Skipping bale {} due to missing account: {}", bale.getEntryNo(), e.getMessage());
            return null;
            
        } catch (CurrencyResolutionException e) {
            // Skip bales with currency determination issues
            log.warn("Skipping bale {} due to currency resolution failure: {}", bale.getEntryNo(), e.getMessage());
            return null;
            
        } catch (FxRateUnavailableException e) {
            // Skip bales with FX rate retrieval issues
            log.warn("Skipping bale {} due to FX rate unavailability: {}", bale.getEntryNo(), e.getMessage());
            return null;
            
        } catch (Exception e) {
            // Propagate unexpected exceptions for retry/fail handling
            log.error("Unexpected error processing bale {}: {}", bale.getEntryNo(), e.getMessage(), e);
            throw e;
        }
    }
}
