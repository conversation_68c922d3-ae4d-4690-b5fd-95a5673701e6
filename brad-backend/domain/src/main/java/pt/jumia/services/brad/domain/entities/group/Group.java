package pt.jumia.services.brad.domain.entities.group;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Setter
@Getter
@AllArgsConstructor
public abstract class Group<E> {

    private List<E> groupings;
    private List<List<String>> result = new ArrayList<>();

    public Group (String resultString, List<E> groupings) {
        String[] resultStringArray = resultString.split("], \\[");
        for (String s : resultStringArray) {
            s = s.replace("[", "").replace("]", "");
            if (s.equals("null")) {
                continue;
            }
            String[] sArray = s.split(", ");
            List<String> sList = new ArrayList<>(Arrays.asList(sArray));
            this.result.add(sList);
        }
        this.groupings = new ArrayList<>(groupings);
    }

}
