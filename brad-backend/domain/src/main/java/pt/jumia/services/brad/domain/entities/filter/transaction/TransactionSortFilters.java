package pt.jumia.services.brad.domain.entities.filter.transaction;


import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class TransactionSortFilters extends SortFilters<Transaction.SortingFields> {

    public TransactionSortFilters(Transaction.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
