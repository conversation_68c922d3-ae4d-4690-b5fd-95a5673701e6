package pt.jumia.services.brad.domain.enumerations;

public enum DocumentType {

    FORM, OTHERS;

    public static DocumentType fromString(String value) {
        if (value != null) {
            for (DocumentType documentType : DocumentType.values()) {
                if (value.equalsIgnoreCase(documentType.toString())) {
                    return documentType;
                }
            }
        }
        throw new IllegalArgumentException("Invalid document type value: " + value);
    }

}
