package pt.jumia.services.brad.domain.entities.group;

import org.apache.commons.lang3.StringUtils;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.enumerations.Direction;

import java.util.List;

public class TransactionGroup extends Group<Transaction.GroupingFields> {

    public TransactionGroup(String resultString, List<Transaction.GroupingFields> groupings) {
        super(resultString, groupings);
        for (int i = 0; i < groupings.size(); i++) {
            if (groupings.get(i).equals(Transaction.GroupingFields.DIRECTION)) {
                for (List<String> result : this.getResult()) {
                    if (!StringUtils.isEmpty(result.get(i))) {
                        result.set(i, Direction.getDirection(result.get(i)).name());
                    } else {
                        result.set(i, StringUtils.EMPTY);
                    }
                }
            }
        }
    }
}
