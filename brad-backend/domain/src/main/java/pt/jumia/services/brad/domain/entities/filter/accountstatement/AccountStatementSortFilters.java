package pt.jumia.services.brad.domain.entities.filter.accountstatement;

import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class AccountStatementSortFilters extends SortFilters<AccountStatement.SortingFields> {

    public AccountStatementSortFilters(AccountStatement.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
