package pt.jumia.services.brad.domain.usecases.bale.enrichment;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.usecases.fxrates.brad.ReadBradFxRateUseCase;

import java.util.HashSet;
import java.util.Set;

/**
 * Focused service responsible only for enriching bales with FxRate data.
 * Extracted from SyncBradBaleUseCase to follow Single Responsibility Principle.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BaleFxRateEnricher {

    private final ReadBradFxRateUseCase readBradFxRateUseCase;
    
    private static final String USD = "USD";

    /**
     * Enriches a bale with FX rate information.
     * 
     * @param bale the bale to enrich
     * @param transactionCurrency the transaction currency for the bale
     * @return set of FX rates for the bale
     * @throws FxRateUnavailableException if FX rates cannot be retrieved
     */
    public Set<FxRate> enrichWithFxRates(Bale bale, Currency transactionCurrency) throws FxRateUnavailableException {
        try {
            Set<FxRate> fxRates = new HashSet<>();
            String baleCurrencyCode = transactionCurrency.getCode();

            log.debug("Enriching bale {} with FX rates for currency: {}", bale.getEntryNo(), baleCurrencyCode);

            // Add USD FX rate
            this.readBradFxRateUseCase.addFxRate(fxRates, baleCurrencyCode, USD, bale.getPostingDate());

            // Add country currency FX rate if available
            if (bale.getAccount() != null &&
                bale.getAccount().getCountry() != null && 
                bale.getAccount().getCountry().getCurrency() != null) {
                
                String countryCurrencyCode = bale.getAccount().getCountry().getCurrency().getCode();
                this.readBradFxRateUseCase.addFxRate(fxRates, baleCurrencyCode, 
                    countryCurrencyCode, bale.getPostingDate());
                    
                log.debug("Added country currency FX rate: {} for bale: {}", 
                    countryCurrencyCode, bale.getEntryNo());
            }

            log.debug("Successfully enriched bale {} with {} FX rates", bale.getEntryNo(), fxRates.size());
            return fxRates;
            
        } catch (Exception e) {
            throw new FxRateUnavailableException("Failed to enrich bale with FX rates: " + bale.getEntryNo(), e);
        }
    }

    /**
     * Adds FX rates to an existing bale.
     * 
     * @param bale the bale to add FX rates to
     * @return the bale with added FX rates
     * @throws FxRateUnavailableException if FX rates cannot be added
     */
    public Bale addFxRates(Bale bale) throws FxRateUnavailableException {
        if (bale == null) {
            throw new IllegalArgumentException("Bale cannot be null");
        }

        if (bale.getAccount() == null) {
            throw new IllegalArgumentException("Bale account cannot be null");
        }

        Currency transactionCurrency = bale.getTransactionCurrency();
        if (transactionCurrency == null) {
            throw new IllegalArgumentException("Bale transaction currency cannot be null");
        }

        Set<FxRate> fxRates = enrichWithFxRates(bale, transactionCurrency);

        return bale.toBuilder()
                .fxRates(fxRates)
                .build();
    }
}
