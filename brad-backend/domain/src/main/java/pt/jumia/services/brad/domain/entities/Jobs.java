package pt.jumia.services.brad.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;

import java.time.LocalDateTime;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor
public class Jobs {
    String jobName;
    String cronExpression;
    String state;
    String timezone;
    LocalDateTime lastFiredTime;
    LocalDateTime nextFireTime;


    public Jobs toEntity() {
        return Jobs
                .builder()
                .jobName(this.jobName)
                .cronExpression(this.cronExpression)
                .state(this.state)
                .timezone(this.timezone)
                .lastFiredTime(this.lastFiredTime)
                .nextFireTime(this.nextFireTime)
                .build();
    }
}
