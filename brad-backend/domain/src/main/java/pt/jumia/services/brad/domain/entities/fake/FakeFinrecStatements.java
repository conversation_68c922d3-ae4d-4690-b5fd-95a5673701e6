package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FinrecStatement;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public interface FakeFinrecStatements {


    FinrecStatement FAKE_FINREC_STATEMENT_TEMPLATE = FinrecStatement.builder()
            .tranID("tranID")
            .initialDate(LocalDateTime.now())
            .finalDate(LocalDateTime.now())
            .accountNumber("accountNumber")
            .openingBalance(BigDecimal.TEN)
            .currency(FakeCurrencies.NGN.getCode())
            .type("type")
            .hasTransaction("Y")
            .runningBalance(BigDecimal.TEN)
            .reference("reference")
            .valueDate(LocalDateTime.now().toString())
            .description("description")
            .tranAmount(BigDecimal.TEN)
            .direction("C")
            .tranDate(LocalDateTime.now().toString())
            .skAudInsert(1)
            .timestampRunAt(LocalDateTime.now())
            .build();


    static List<FinrecStatement> getFakeFinrecStatementsForAnAccount(String accountNumber, LocalDateTime startingDay,
                                                                        Currency currency, BigDecimal openingBalance,
                                                                        BigDecimal transactionAmount,
                                                                        int amountCredit, int amountDebit) {

        List<FinrecStatement> fakeFinrecStatements = new ArrayList<>();
        getFakeCreditFinrecStatementsForAnAccount(
                accountNumber, startingDay, currency, openingBalance, transactionAmount, amountCredit, fakeFinrecStatements
        );
        getFakeDebitFinrecStatementsForAnAccount(
                accountNumber, startingDay, currency, openingBalance, transactionAmount, amountDebit, fakeFinrecStatements
        );

        return fakeFinrecStatements;

    }

    static List<FinrecStatement> getFakeCreditFinrecStatementsForAnAccount(String accountNumber, LocalDateTime startingDay,
                                                                              Currency currency, BigDecimal openingBalance,
                                                                              BigDecimal transactionAmount, int amount,
                                                                              List<FinrecStatement> finrecStatements) {

        int initialSize = finrecStatements.size();

        for (int i = initialSize; i < amount + initialSize; i++) {
            finrecStatements.add(FAKE_FINREC_STATEMENT_TEMPLATE.toBuilder()
                    .tranID(FAKE_FINREC_STATEMENT_TEMPLATE.getTranID() + i)
                    .initialDate(startingDay)
                    .finalDate(startingDay)
                    .accountNumber(accountNumber)
                    .openingBalance(openingBalance)
                    .currency(currency.getCode())
                    .type(FAKE_FINREC_STATEMENT_TEMPLATE.getType() + i)
                    .hasTransaction(FAKE_FINREC_STATEMENT_TEMPLATE.getHasTransaction())
                    .runningBalance(finrecStatements.isEmpty() ? openingBalance.add(transactionAmount) :
                            finrecStatements.get(i - 1).getRunningBalance().add(transactionAmount)
                    )
                    .reference(FAKE_FINREC_STATEMENT_TEMPLATE.getReference() + i)
                    .valueDate(startingDay.toString())
                    .description(FAKE_FINREC_STATEMENT_TEMPLATE.getDescription() + i)
                    .tranAmount(transactionAmount)
                    .direction("C")
                    .tranDate(startingDay.toString())
                    .skAudInsert(FAKE_FINREC_STATEMENT_TEMPLATE.getSkAudInsert() + i)
                    .timestampRunAt(LocalDateTime.now())
                    .build()
            );
        }
        return finrecStatements;

    }

    static List<FinrecStatement> getFakeDebitFinrecStatementsForAnAccount(String accountNumber, LocalDateTime startingDay,
                                                                             Currency currency, BigDecimal openingBalance,
                                                                             BigDecimal transactionAmount, int amount,
                                                                             List<FinrecStatement> finrecStatements) {

        int initialSize = finrecStatements.size();

        for (int i = initialSize; i < amount + initialSize; i++) {
            finrecStatements.add(FAKE_FINREC_STATEMENT_TEMPLATE.toBuilder()
                    .tranID(FAKE_FINREC_STATEMENT_TEMPLATE.getTranID() + i)
                    .initialDate(startingDay)
                    .finalDate(startingDay)
                    .accountNumber(accountNumber)
                    .openingBalance(openingBalance)
                    .currency(currency.getCode())
                    .type(FAKE_FINREC_STATEMENT_TEMPLATE.getType() + i)
                    .hasTransaction(FAKE_FINREC_STATEMENT_TEMPLATE.getHasTransaction())
                    .runningBalance(finrecStatements.isEmpty() ? openingBalance.subtract(transactionAmount) :
                            finrecStatements.get(i - 1).getRunningBalance().subtract(transactionAmount)
                    )
                    .reference(FAKE_FINREC_STATEMENT_TEMPLATE.getReference() + i)
                    .valueDate(startingDay.toString())
                    .description(FAKE_FINREC_STATEMENT_TEMPLATE.getDescription() + i)
                    .tranAmount(transactionAmount)
                    .direction("D")
                    .tranDate(startingDay.toString())
                    .skAudInsert(FAKE_FINREC_STATEMENT_TEMPLATE.getSkAudInsert() + i)
                    .timestampRunAt(LocalDateTime.now())
                    .build()
            );
        }
        return finrecStatements;

    }

    static List<FinrecStatement> getSimpleFinrecStatements(int amount) {
        List<FinrecStatement> finrecStatements = new ArrayList<>();
        for (int i = 0; i < amount; i++) {
            finrecStatements.add(FAKE_FINREC_STATEMENT_TEMPLATE.toBuilder()
                    .tranID(FAKE_FINREC_STATEMENT_TEMPLATE.getTranID() + i)
                    .initialDate(LocalDateTime.now())
                    .finalDate(LocalDateTime.now())
                    .accountNumber(FAKE_FINREC_STATEMENT_TEMPLATE.getAccountNumber() + i)
                    .openingBalance(FAKE_FINREC_STATEMENT_TEMPLATE.getOpeningBalance())
                    .currency(FAKE_FINREC_STATEMENT_TEMPLATE.getCurrency())
                    .type(FAKE_FINREC_STATEMENT_TEMPLATE.getType() + i)
                    .hasTransaction(FAKE_FINREC_STATEMENT_TEMPLATE.getHasTransaction())
                    .runningBalance(FAKE_FINREC_STATEMENT_TEMPLATE.getRunningBalance())
                    .reference(FAKE_FINREC_STATEMENT_TEMPLATE.getReference() + i)
                    .valueDate(LocalDateTime.now().toString())
                    .description(FAKE_FINREC_STATEMENT_TEMPLATE.getDescription() + i)
                    .tranAmount(FAKE_FINREC_STATEMENT_TEMPLATE.getTranAmount())
                    .direction(FAKE_FINREC_STATEMENT_TEMPLATE.getDirection())
                    .tranDate(LocalDateTime.now().toString())
                    .skAudInsert(FAKE_FINREC_STATEMENT_TEMPLATE.getSkAudInsert() + i)
                    .timestampRunAt(LocalDateTime.now())
                    .build()
            );
        }
        return finrecStatements;
    }


}

