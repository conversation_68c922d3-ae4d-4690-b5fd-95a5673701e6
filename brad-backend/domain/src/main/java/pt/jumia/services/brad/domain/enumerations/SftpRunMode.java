package pt.jumia.services.brad.domain.enumerations;

public enum SftpRunMode {
    DRY_RUN,
    LIVE;

    public static SftpRunMode fromString(String mode) {

        for (SftpRunMode sftpRunMode : SftpRunMode.values()) {
            if (sftpRunMode.name().equalsIgnoreCase(mode)) {
                return sftpRunMode;
            }
        }
        throw new IllegalArgumentException("Unknown SFTP run mode: " + mode);
    }
}
