package pt.jumia.services.brad.domain.usecases.bale.batch;

class BatchJob<PERSON>esult {
    private final Long jobId;
    private final String status;
    private final boolean successful;

    public BatchJobResult(Long jobId, String status, boolean successful) {
        this.jobId = jobId;
        this.status = status;
        this.successful = successful;
    }

    public Long getJobId() { 
        return jobId; 
    }
    
    public String getStatus() { 
        return status; 
    }
    
    public boolean isSuccessful() {
        return successful;
    }
}
