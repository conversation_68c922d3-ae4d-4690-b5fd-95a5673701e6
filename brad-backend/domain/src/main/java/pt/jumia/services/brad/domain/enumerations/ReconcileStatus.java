package pt.jumia.services.brad.domain.enumerations;

public enum ReconcileStatus {

    NOT_RECONCILED, PENDING_APPROVAL, RECONCILED;

    public static ReconcileStatus fromString(String value) {
        if (value != null) {
            for (ReconcileStatus status : ReconcileStatus.values()) {
                if (value.equalsIgnoreCase(status.toString())) {
                    return status;
                }
            }
        }
        throw new IllegalArgumentException("Invalid ReconcileStatus value: " + value);
    }

}
