package pt.jumia.services.brad.domain.usecases.fxrates.brad;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCase;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@AllArgsConstructor
public class SyncFxRateBaleUseCase {

    private BradBaleRepository bradBaleRepository;
    private SyncBradBaleUseCase syncBradBaleUseCase;

    public void execute(List<Bale> baleWithoutFxRate) {

        List<Bale> balesWithFxRate = new ArrayList<>();
        baleWithoutFxRate.forEach(bale -> {
            try {
                balesWithFxRate.add(syncBradBaleUseCase.addFxRates(bale));
            } catch (Exception e) {
                log.error("Error updating Bale {}", bale, e);
            }
        });

        this.bradBaleRepository.addFxRates(balesWithFxRate);

    }

}
