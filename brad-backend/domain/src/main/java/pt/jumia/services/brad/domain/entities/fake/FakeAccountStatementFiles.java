package pt.jumia.services.brad.domain.entities.fake;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;

public interface FakeAccountStatementFiles {

    AccountStatementFile ACCOUNT_STATEMENT_FILE_1 = AccountStatementFile.builder()
        .id(1L)
        .name("BANK_STATEMENT_FILE_1")
        .url("url")
        .processingStatus(ProcessingStatus.PROCESSED)
        .statusDescription("PROCESSED")
        .checksum("checksum")
        .executionLog(FakeExecutionLogs.FAKE_API_LOG)
        .statement(FakeAccountStatements.FAKE_ACCOUNT_STATEMENT)
        .createdAt(LocalDateTime.now())
        .createdBy("fakeUser")
        .updatedAt(LocalDateTime.now())
        .updatedBy("updateUser")
        .build();

    static List<AccountStatementFile> getFakeAccountStatementFiles(int amount) {

        List<AccountStatementFile> fakeAccountStatements = new ArrayList<>();
        for (int i = 1; i <= amount; i++) {
            fakeAccountStatements.add(ACCOUNT_STATEMENT_FILE_1.toBuilder()
                .id(ACCOUNT_STATEMENT_FILE_1.getId() + i)
                .name(ACCOUNT_STATEMENT_FILE_1.getName())
                .url("url")
                .processingStatus(ProcessingStatus.PROCESSED)
                .statusDescription("PROCESSED")
                .checksum("checksum")
                .executionLog(FakeExecutionLogs.FAKE_API_LOG)
                .statement(FakeAccountStatements.FAKE_ACCOUNT_STATEMENT)
                .createdAt(LocalDateTime.now())
                .createdBy("user" + i)
                .updatedAt(LocalDateTime.now())
                .updatedBy("updateUser" + i)

                .build());
        }

        return fakeAccountStatements;
    }

}
