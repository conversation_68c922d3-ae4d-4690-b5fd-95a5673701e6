package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.ExportLog;

import java.time.LocalDateTime;
import java.util.List;

public interface FakeExportLogs {
    
    ExportLog LOG_1 = ExportLog.builder()
            .id(1L)
            .createdAt(LocalDateTime.now().minusDays(1))
            .createdBy("fakeUser1")
            .fileName("fakeFileName1.csv")
            .status(ExportLog.Status.COMPLETED)
            .build();

    ExportLog LOG_2 = ExportLog.builder()
            .id(2L)
            .createdAt(LocalDateTime.now().minusDays(2))
            .createdBy("fakeUser2")
            .fileName("fakeFileName2.csv")
            .status(ExportLog.Status.COMPLETED)
            .build();

    ExportLog LOG_3 = ExportLog.builder()
            .id(3L)
            .createdAt(LocalDateTime.now().minusDays(3))
            .createdBy("fakeUser3")
            .fileName("fakeFileName3.csv")
            .status(ExportLog.Status.COMPLETED)
            .build();

    ExportLog LOG_4 = ExportLog.builder()
            .id(4L)
            .createdAt(LocalDateTime.now().minusDays(4))
            .createdBy("fakeUser4")
            .fileName("fakeFileName4.csv")
            .status(ExportLog.Status.COMPLETED)
            .build();

    ExportLog LOG_5 = ExportLog.builder()
            .id(5L)
            .createdAt(LocalDateTime.now().minusDays(5))
            .createdBy("fakeUser5")
            .fileName("fakeFileName5.csv")
            .status(ExportLog.Status.COMPLETED)
            .build();

    ExportLog LOG_6 = ExportLog.builder()
            .id(6L)
            .createdAt(LocalDateTime.now().minusDays(6))
            .createdBy("fakeUser6")
            .fileName("fakeFileName6.csv")
            .status(ExportLog.Status.COMPLETED)
            .build();

    ExportLog LOG_7 = ExportLog.builder()
            .id(7L)
            .createdAt(LocalDateTime.now().minusDays(7))
            .createdBy("fakeUser7")
            .fileName("fakeFileName7.csv")
            .status(ExportLog.Status.FAILED)
            .build();

    ExportLog LOG_8 = ExportLog.builder()
            .id(8L)
            .createdAt(LocalDateTime.now().minusDays(8))
            .createdBy("fakeUser8")
            .fileName("fakeFileName8.csv")
            .status(ExportLog.Status.COMPLETED)
            .build();

    ExportLog LOG_9 = ExportLog.builder()
            .id(9L)
            .createdAt(LocalDateTime.now().minusDays(9))
            .createdBy("fakeUser9")
            .fileName("fakeFileName9.csv")
            .status(ExportLog.Status.COMPLETED)
            .build();

    ExportLog LOG_10 = ExportLog.builder()
            .id(10L)
            .createdAt(LocalDateTime.now().minusDays(10))
            .createdBy("fakeUser10")
            .fileName("fakeFileName10.csv")
            .status(ExportLog.Status.COMPLETED)
            .build();

    List<ExportLog> LOGS = List.of(
            LOG_1,
            LOG_2,
            LOG_3,
            LOG_4,
            LOG_5,
            LOG_6,
            LOG_7,
            LOG_8,
            LOG_9,
            LOG_10
    );
}
