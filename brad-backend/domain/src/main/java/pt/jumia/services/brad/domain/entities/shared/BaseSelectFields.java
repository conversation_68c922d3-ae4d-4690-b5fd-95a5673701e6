package pt.jumia.services.brad.domain.entities.shared;


import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


public interface BaseSelectFields {

    String getQueryField();
    String getSelectCode();

    static <T extends Enum<T> & BaseSelectFields> T fromSelectCode(Class<T> enumClass, String selectCode) {
        if (IGNORED_FIELDS.contains(selectCode)) {
            return null;
        }
        for (T selectField : enumClass.getEnumConstants()) {
            if (selectField.getSelectCode().equalsIgnoreCase(selectCode)) {
                return selectField;
            }
        }
        throw new IllegalArgumentException("Invalid SelectField value: " + selectCode);
    }

    static <T extends Enum<T> & BaseSelectFields> List<T> fromSelectCodes(Class<T> enumClass, List<String> selectCodes) {

        if (!selectCodes.isEmpty()) {
            DEFAULT_FIELDS.stream()
                    .filter(defaultField -> !selectCodes.contains(defaultField))
                    .forEach(selectCodes::add);
        }

        return selectCodes.stream()
                .map(selectCode -> fromSelectCode(enumClass, selectCode))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    List<String> IGNORED_FIELDS = List.of("select");
    List<String> DEFAULT_FIELDS = List.of("id");

}
