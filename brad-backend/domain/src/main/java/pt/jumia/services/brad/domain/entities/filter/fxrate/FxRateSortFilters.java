package pt.jumia.services.brad.domain.entities.filter.fxrate;

import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class FxRateSortFilters extends SortFilters<FxRate.SortingFields> {

    public FxRateSortFilters(FxRate.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
