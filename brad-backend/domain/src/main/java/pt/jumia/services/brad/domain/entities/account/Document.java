package pt.jumia.services.brad.domain.entities.account;

import lombok.*;
import org.jetbrains.annotations.Nullable;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;
import pt.jumia.services.brad.domain.enumerations.DocumentType;

import java.time.LocalDateTime;

/**
 * Business representation of Document
 */
@Value
@Builder(toBuilder = true)
@AllArgsConstructor
public class Document {

    @Nullable
    Long id;

    @NonNull
    DocumentType documentType;

    @NonNull
    String name;

    @NonNull
    String description;

    String fileKey;

    String url;

    String file;

    Account account;

    LocalDateTime createdAt;

    String createdBy;

    LocalDateTime updatedAt;

    String updatedBy;

    public Document withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .account(account == null ? null : account.withoutDbFields())
                .updatedAt(null)
                .updatedBy(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

    @Getter
    public enum SelectFields implements BaseSelectFields {
        ID("id", "id"),
        DOCUMENT_TYPE("documentType", "documentType"),
        NAME("name", "name"),
        DESCRIPTION("description", "description"),
        FILE_KEY("fileKey", "fileKey"),
        URL("url", "url"),
        FILE("file", "file"),
        CREATED_AT("createdAt", "createdAt"),
        CREATED_BY("createdBy", "createdBy"),
        UPDATED_AT("updatedAt", "updatedAt"),
        UPDATED_BY("updatedBy", "updatedBy");

        public final String queryField;
        public final String selectCode;

        SelectFields(String queryField, String selectCode) {
            this.queryField = queryField;
            this.selectCode = selectCode;
        }
    }

    public enum SortingFields {
        ID,
        DOCUMENT_TYPE,
        NAME,
        DESCRIPTION,
        CREATED_AT,
        CREATED_BY,
        UPDATED_AT,
        UPDATED_BY
    }

}
