package pt.jumia.services.brad.domain.entities.filter.bale;

import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class BaleSortFilters extends SortFilters<Bale.SortingFields> {

    public BaleSortFilters(Bale.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
