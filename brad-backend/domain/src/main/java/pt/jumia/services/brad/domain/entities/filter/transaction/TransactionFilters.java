package pt.jumia.services.brad.domain.entities.filter.transaction;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.shared.BaseFilters;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransactionFilters extends BaseFilters {

    private String type;
    private String accountId;
    private String partitionKey;
    private List<String> currency;
    private LocalDate valueDateStart;
    private LocalDate valueDateEnd;
    private LocalDate transactionDateStart;
    private LocalDate transactionDateEnd;
    private LocalDate statementDateStart;
    private LocalDate statementDateEnd;
    private List<String> direction;
    private BigDecimal amount;
    private BigDecimal amountLocalCurrency;
    private BigDecimal rate;
    private String reference;
    private String description;
    private String accountStatementID;
    private String remittanceInformation;
    private String orderingPartyName;
    private LocalDateTime createdAtStart;
    private LocalDateTime createdAtEnd;
    private boolean exactFilters;

    private boolean withReconciliationFilters;
    //reconciliation related filters
    private Integer reconciliationId;
    private String reconciliationCreator;
    private LocalDate reconciliationCreationDateStart;
    private LocalDate reconciliationCreationDateEnd;
    private String reconciliationReviewer;
    private LocalDate reconciliationReviewDateStart;
    private LocalDate reconciliationReviewDateEnd;
    private List<String> reconciliationStatus;

    private boolean importedStatementOnly;

    public Map<String, ?> getAsMap() {
        HashMap<String, Object> map = new HashMap<>();

        if (!Objects.isNull(this.type)) {
            map.put("type", this.type);
        }
        if (!Objects.isNull(this.accountId)) {
            map.put("accountId", this.accountId);
        }
        if (!Objects.isNull(this.partitionKey)) {
            map.put("partitionKey", this.partitionKey);
        }
        if (!Objects.isNull(this.currency)) {
            map.put("currency", this.currency);
        }
        if (!Objects.isNull(this.valueDateStart)) {
            map.put("valueDateStart", this.valueDateStart);
        }
        if (!Objects.isNull(this.valueDateEnd)) {
            map.put("valueDateEnd", this.valueDateEnd);
        }
        if (!Objects.isNull(this.transactionDateStart)) {
            map.put("transactionDateStart", this.transactionDateStart);
        }
        if (!Objects.isNull(this.transactionDateEnd)) {
            map.put("transactionDateEnd", this.transactionDateEnd);
        }
        if (!Objects.isNull(this.statementDateStart)) {
            map.put("statementDateStart", this.statementDateStart);
        }
        if (!Objects.isNull(this.statementDateEnd)) {
            map.put("statementDateEnd", this.statementDateEnd);
        }
        if (!Objects.isNull(this.direction)) {
            map.put("direction", this.direction);
        }
        if (!Objects.isNull(this.amount)) {
            map.put("amount", this.amount);
        }
        if (!Objects.isNull(this.rate)) {
            map.put("rate", this.rate);
        }
        if (!Objects.isNull(this.reference)) {
            map.put("reference", this.reference);
        }
        if (!Objects.isNull(this.description)) {
            map.put("description", this.description);
        }
        if (!Objects.isNull(this.accountStatementID)) {
            map.put("accountStatementID", this.accountStatementID);
        }
        if (!Objects.isNull(this.createdAtStart)){
            map.put("createdAtStart", this.createdAtStart);
        }
        if (!Objects.isNull(this.createdAtEnd)){
            map.put("createdAtEnd", this.createdAtEnd);
        }
        if (!Objects.isNull(this.withReconciliationFilters)){
            map.put("withReconciliationFilters", this.withReconciliationFilters);
        }
        if (!Objects.isNull(this.reconciliationCreator)){
            map.put("reconciliationCreator", this.reconciliationCreator);
        }
        if (!Objects.isNull(this.reconciliationCreationDateStart)){
            map.put("reconciliationCreationDateStart", this.reconciliationCreationDateStart);
        }
        if (!Objects.isNull(this.reconciliationCreationDateEnd)){
            map.put("reconciliationCreationDateEnd", this.reconciliationCreationDateEnd);
        }
        if (!Objects.isNull(this.reconciliationReviewer)){
            map.put("reconciliationReviewer", this.reconciliationReviewer);
        }
        if (!Objects.isNull(this.reconciliationReviewDateStart)){
            map.put("reconciliationReviewDateStart", this.reconciliationReviewDateStart);
        }
        if (!Objects.isNull(this.reconciliationReviewDateEnd)){
            map.put("reconciliationReviewDateEnd", this.reconciliationReviewDateEnd);
        }
        if (!Objects.isNull(this.reconciliationStatus)) {
            map.put("reconciliationStatus", this.reconciliationStatus);
        }
        return map;
    }
}
