package pt.jumia.services.brad.domain.usecases.bale.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.BatchStatus;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.error.BaleErrorClassifier;
import pt.jumia.services.brad.domain.usecases.executionlogs.UpdateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.CreateExecutionLogsUseCase;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class BaleJobExecutionListener implements JobExecutionListener {

    private final UpdateExecutionLogsUseCase updateExecutionLogsUseCase;
    private final CreateExecutionLogsUseCase createExecutionLogsUseCase;
    private final BaleSkipListener baleSkipListener;
    private final BaleErrorReporter baleErrorReporter;

    @Override
    public void beforeJob(JobExecution jobExecution) {
        JobParameters jobParameters = jobExecution.getJobParameters();
        Long executionLogId = jobParameters.getLong("executionLogId");
        
        if (executionLogId != null) {
            log.info("Starting Bale sync job for ExecutionLog ID: {}", executionLogId);
            
            ExecutionLog executionLog = ExecutionLog.builder()
                    .id(executionLogId)
                    .logType(ExecutionLog.ExecutionLogType.BALE)
                    .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
                    .executionStartTime(LocalDateTime.now())
                    .build();
                    
            jobExecution.getExecutionContext().putLong("executionLogId", executionLogId);
            
            updateExecutionLogsUseCase.updateExecutionLog(executionLog);
            
            log.debug("ExecutionLog {} marked as STARTED for Spring Batch job {}", 
                     executionLogId, jobExecution.getJobId());
        } else {
            log.info("No executionLogId provided in job parameters for job: {}, creating new ExecutionLog", jobExecution.getJobId());
            
            ExecutionLog newExecutionLog = ExecutionLog.builder()
                    .logType(ExecutionLog.ExecutionLogType.BALE)
                    .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
                    .executionStartTime(LocalDateTime.now())
                    .build();
            
            ExecutionLog createdExecutionLog = createExecutionLogsUseCase.execute(newExecutionLog);
            
            // Store the created executionLogId in the execution context
            jobExecution.getExecutionContext().putLong("executionLogId", createdExecutionLog.getId());
            
            log.info("Created new ExecutionLog with ID: {} for Spring Batch job {}", 
                    createdExecutionLog.getId(), jobExecution.getJobId());
        }
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        Long executionLogId = jobExecution.getExecutionContext().getLong("executionLogId");
        
        if (executionLogId == null) {
            log.warn("No executionLogId found in job execution context for job: {}", jobExecution.getJobId());
            return;
        }

        // Collect comprehensive error information
        List<ExecutionLog.SyncingError> allErrors = collectAllErrors(jobExecution);
        
        // Log the skip listener error summary
        baleSkipListener.logErrorSummary();

        ExecutionLog executionLogBase = ExecutionLog.builder()
                .id(executionLogId)
                .logType(ExecutionLog.ExecutionLogType.BALE)
                .executionEndTime(LocalDateTime.now())
                .errors(allErrors)
                .build();

        switch (jobExecution.getStatus()) {
            case COMPLETED:
                handleJobCompletion(jobExecution, executionLogBase, allErrors);
                break;
            case FAILED:
                handleJobFailure(jobExecution, executionLogBase, allErrors);
                break;
            case STOPPED:
            case STOPPING:
                handleJobStopped(jobExecution, executionLogBase, allErrors);
                break;
            default:
                log.warn("Unexpected job status: {} for ExecutionLog: {}", 
                        jobExecution.getStatus(), executionLogId);
                updateExecutionLogsUseCase.updateExecutionLogWithError(executionLogBase, allErrors);
        }
        
        // Final comprehensive error summary
        logFinalErrorSummary(executionLogId, jobExecution, allErrors);
        
        // Generate and log comprehensive error report
        if (!allErrors.isEmpty()) {
            try {
                baleErrorReporter.logErrorSummary(executionLogBase);
            } catch (Exception e) {
                log.error("Failed to generate comprehensive error report: {}", e.getMessage());
            }
        }
    }

    private List<ExecutionLog.SyncingError> collectAllErrors(JobExecution jobExecution) {
        List<ExecutionLog.SyncingError> allErrors = new ArrayList<>();
        
        // Collect errors from skip listener
        allErrors.addAll(collectSkipListenerErrors());
        
        // Collect errors from step execution contexts and failure exceptions
        for (StepExecution stepExecution : jobExecution.getStepExecutions()) {
            allErrors.addAll(collectStepErrors(stepExecution));
        }
        
        // Collect job-level failure exceptions
        allErrors.addAll(collectJobFailureErrors(jobExecution));
        
        log.debug("Collected {} total errors from job execution", allErrors.size());
        return allErrors;
    }

    private List<ExecutionLog.SyncingError> collectSkipListenerErrors() {
        List<ExecutionLog.SyncingError> errors = new ArrayList<>();
        
        // Create summary errors from skip listener data
        Map<String, Integer> errorSummary = baleSkipListener.getErrorCounts().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().get()
                ));
        
        for (Map.Entry<String, Integer> entry : errorSummary.entrySet()) {
            String[] parts = entry.getKey().split(":");
            if (parts.length >= 2) {
                String category = parts[0];
                String phase = parts[1];
                int count = entry.getValue();
                
                ExecutionLog.SyncingError sample = baleSkipListener.getErrorSamples().get(entry.getKey());
                
                errors.add(ExecutionLog.SyncingError.builder()
                        .errorDescription(String.format("%s errors during %s: %d occurrences", 
                                category, phase, count))
                        .errorCategory(category)
                        .operationContext(phase + "_SUMMARY")
                        .affectedRecordId(String.valueOf(count))
                        .accountNumber(sample != null ? sample.getAccountNumber() : null)
                        .entryNo(sample != null ? sample.getEntryNo() : null)
                        .build());
                
                // Add sample error if available
                if (sample != null) {
                    errors.add(sample);
                }
            }
        }
        
        return errors;
    }

    private List<ExecutionLog.SyncingError> collectStepErrors(StepExecution stepExecution) {
        List<ExecutionLog.SyncingError> errors = new ArrayList<>();
        
        // Collect from step execution context
        stepExecution.getExecutionContext().entrySet().stream()
                .filter(entry -> entry.getKey().startsWith("skip_errors_"))
                .forEach(entry -> {
                    String errorData = (String) entry.getValue();
                    String[] parts = errorData.split("\\|");
                    if (parts.length >= 3) {
                        errors.add(ExecutionLog.SyncingError.builder()
                                .errorDescription(parts[0])
                                .errorCategory(parts[1])
                                .operationContext(parts[2])
                                .accountNumber(parts.length > 3 ? parts[3] : null)
                                .entryNo(parts.length > 4 && !parts[4].isEmpty() ? 
                                        Integer.parseInt(parts[4]) : null)
                                .build());
                    }
                });
        
        // Collect from failure exceptions
        stepExecution.getFailureExceptions().forEach(throwable -> {
            BaleErrorClassifier.ErrorCategory category = BaleErrorClassifier.categorizeError(throwable);
            errors.add(ExecutionLog.SyncingError.builder()
                    .errorDescription(throwable.getMessage())
                    .errorCategory(category.name())
                    .operationContext("STEP_FAILURE")
                    .build());
        });
        
        return errors;
    }

    private List<ExecutionLog.SyncingError> collectJobFailureErrors(JobExecution jobExecution) {
        List<ExecutionLog.SyncingError> errors = new ArrayList<>();
        
        if (jobExecution.getStatus() == BatchStatus.FAILED) {
            jobExecution.getAllFailureExceptions().forEach(throwable -> {
                BaleErrorClassifier.ErrorCategory category = BaleErrorClassifier.categorizeError(throwable);
                errors.add(ExecutionLog.SyncingError.builder()
                        .errorDescription(throwable.getMessage())
                        .errorCategory(category.name())
                        .operationContext("JOB_FAILURE")
                        .build());
            });
        }
        
        return errors;
    }

    private void handleJobCompletion(JobExecution jobExecution, ExecutionLog executionLog, List<ExecutionLog.SyncingError> allErrors) {
        long totalItemsProcessed = jobExecution.getStepExecutions().stream()
                .mapToLong(StepExecution::getReadCount)
                .sum();
                
        long totalItemsWritten = jobExecution.getStepExecutions().stream()
                .mapToLong(StepExecution::getWriteCount)
                .sum();
                
        long totalItemsSkipped = jobExecution.getStepExecutions().stream()
                .mapToLong(StepExecution::getSkipCount)
                .sum();

        log.info("Bale sync job completed for ExecutionLog {}: {} processed, {} written, {} skipped, {} errors",
                executionLog.getId(), totalItemsProcessed, totalItemsWritten, totalItemsSkipped, allErrors.size());

        ExecutionLog updatedLog = executionLog.toBuilder()
                .recordsAmount((int) totalItemsWritten)
                .errors(allErrors)
                .build();

        if (totalItemsSkipped > 0 || !allErrors.isEmpty()) {
            updateExecutionLogsUseCase.updateExecutionLogWithPartialSuccess(
                updatedLog, (int) totalItemsWritten, allErrors);
        } else if (totalItemsWritten > 0) {
            updateExecutionLogsUseCase.updateExecutionLogWithSuccess(updatedLog);
        } else {
            updateExecutionLogsUseCase.updateExecutionLogWithRecoverableErrors(updatedLog, allErrors);
        }
    }

    private void handleJobFailure(JobExecution jobExecution, ExecutionLog executionLog, List<ExecutionLog.SyncingError> allErrors) {
        log.error("Bale sync job failed for ExecutionLog {}: {}", 
                 executionLog.getId(), jobExecution.getExitStatus().getExitDescription());
        
        // Add job failure summary to errors
        List<ExecutionLog.SyncingError> finalErrors = new ArrayList<>(allErrors);
        finalErrors.add(ExecutionLog.SyncingError.builder()
                .errorDescription("Job failed: " + jobExecution.getExitStatus().getExitDescription())
                .errorCategory("CRITICAL")
                .operationContext("JOB_FAILURE")
                .build());
                 
        updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, finalErrors);
    }

    private void handleJobStopped(JobExecution jobExecution, ExecutionLog executionLog, List<ExecutionLog.SyncingError> allErrors) {
        log.warn("Bale sync job stopped for ExecutionLog {}: {}", 
                executionLog.getId(), jobExecution.getExitStatus().getExitDescription());
        
        // Add job stopped summary to errors
        List<ExecutionLog.SyncingError> finalErrors = new ArrayList<>(allErrors);
        finalErrors.add(ExecutionLog.SyncingError.builder()
                .errorDescription("Job stopped: " + jobExecution.getExitStatus().getExitDescription())
                .errorCategory("CRITICAL")
                .operationContext("JOB_STOPPED")
                .build());
                
        updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, finalErrors);
    }

    private void logFinalErrorSummary(Long executionLogId, JobExecution jobExecution, List<ExecutionLog.SyncingError> allErrors) {
        log.info("=== FINAL BALE SYNC ERROR SUMMARY (ExecutionLog: {}) ===", executionLogId);
        
        if (allErrors.isEmpty()) {
            log.info("No errors encountered during bale sync process");
        } else {
            log.info("Total errors collected: {}", allErrors.size());
            
            Map<String, Long> errorsByCategory = allErrors.stream()
                    .collect(Collectors.groupingBy(
                            error -> error.getErrorCategory() != null ? error.getErrorCategory() : "UNKNOWN",
                            Collectors.counting()
                    ));
            
            log.info("Errors by category:");
            errorsByCategory.forEach((category, count) -> 
                    log.info("  {}: {} errors", category, count));
            
            Map<String, Long> errorsByPhase = allErrors.stream()
                    .collect(Collectors.groupingBy(
                            error -> error.getOperationContext() != null ? error.getOperationContext() : "UNKNOWN",
                            Collectors.counting()
                    ));
            
            log.info("Errors by phase:");
            errorsByPhase.forEach((phase, count) -> 
                    log.info("  {}: {} errors", phase, count));
            
            // Log critical errors separately
            List<ExecutionLog.SyncingError> criticalErrors = allErrors.stream()
                    .filter(error -> "CRITICAL".equals(error.getErrorCategory()))
                    .collect(Collectors.toList());
            
            if (!criticalErrors.isEmpty()) {
                log.error("CRITICAL ERRORS DETECTED ({} total):", criticalErrors.size());
                criticalErrors.forEach(error -> 
                        log.error("  - {}: {}", error.getOperationContext(), error.getErrorDescription()));
            }
        }
        
        // Job execution statistics
        long totalProcessed = jobExecution.getStepExecutions().stream()
                .mapToLong(StepExecution::getReadCount).sum();
        long totalWritten = jobExecution.getStepExecutions().stream()
                .mapToLong(StepExecution::getWriteCount).sum();
        long totalSkipped = jobExecution.getStepExecutions().stream()
                .mapToLong(StepExecution::getSkipCount).sum();
        
        log.info("Final statistics - Processed: {}, Written: {}, Skipped: {}, Error Rate: {}%",
                totalProcessed, totalWritten, totalSkipped,
                totalProcessed > 0 ? String.format("%.2f", (double) allErrors.size() / totalProcessed * 100) : "0.00");
        
        log.info("=== END FINAL BALE SYNC ERROR SUMMARY ===");
    }
}
