package pt.jumia.services.brad.domain.entities.dtos;

import lombok.Builder;
import lombok.Getter;
import pt.jumia.services.brad.domain.entities.account.Account;


@Getter
@Builder(toBuilder = true)
public class AccountTroubleshootingDto {

    private final Account account;
    private final Boolean isOutOfSync;
    private final Boolean manualUploadMissing;
    private final Boolean failedStatementValidation;

    public AccountTroubleshootingDto(Account account, Boolean isOutOfSync, Boolean manualUploadMissing, Boolean failedStatementValidation) {

        this.account = account;
        this.isOutOfSync = isOutOfSync;
        this.manualUploadMissing = manualUploadMissing;
        this.failedStatementValidation = failedStatementValidation;
    }

}
