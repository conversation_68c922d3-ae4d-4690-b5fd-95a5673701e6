package pt.jumia.services.brad.domain.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "spring")
public class SpringProperties {

    private Application application = new Application();
    private Datasource datasource = new Datasource();
    private Flyway flyway = new Flyway();

    @Data
    public static class Application {
        private String name = "brad";
    }

    @Data
    public static class Datasource {
        private String url = "**********************************************";
    }

    @Data
    public static class Flyway {
        private boolean enabled = false;
        private String locations = "db/migration";
        private List<String> schemas = List.of("audit", "public", "quartz", "batch");
    }
}
