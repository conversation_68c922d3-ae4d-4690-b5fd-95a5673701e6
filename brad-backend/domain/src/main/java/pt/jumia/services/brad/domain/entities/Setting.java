package pt.jumia.services.brad.domain.entities;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import javax.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class Setting {

    @Nullable
    private Long id;

    private String property;

    private Type type;

    @Nullable
    private String overrideKey;

    @Nullable
    private String description;

    private String value;

    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime updatedAt;

    private String updatedBy;

    public enum Type {
        DEFAULT, OVERRIDE
    }

    public enum SortingFields {
        ID, PROPERTY, TYPE, OVERRIDE_KEY, DESCRIPTION, VALUE, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    }

    public boolean isTypeOverride() {
        return Type.OVERRIDE.equals(type);
    }

    public boolean isOverrideKeyEmpty() {
        return StringUtils.isBlank(overrideKey);
    }

    public Setting withoutDbFields() {
        return this.toBuilder()
            .id(null)
            .updatedAt(null)
            .updatedBy(null)
            .createdAt(null)
            .createdBy(null)
            .build();
    }

    public Setting withTimeTrucSec() {
        return toBuilder()
            .createdAt(createdAt == null ? null : createdAt.truncatedTo(ChronoUnit.SECONDS))
            .updatedAt(updatedAt == null ? null : updatedAt.truncatedTo(ChronoUnit.SECONDS))
            .build();
    }

}
