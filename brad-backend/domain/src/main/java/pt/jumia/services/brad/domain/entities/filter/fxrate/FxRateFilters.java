package pt.jumia.services.brad.domain.entities.filter.fxrate;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.shared.BaseFilters;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FxRateFilters extends BaseFilters {

    private List<String> baseCurrency;
    private List<String> quoteCurrency;
    private LocalDate rateDate;
    private BigDecimal bid;
    private LocalDateTime bisLoadedAt;
    private Integer skAudInsert;
    private Integer skAudUpdate;
    private LocalDateTime timestampLastUpdate;


    public Map<String, ?> getAsMap(){
        HashMap<String, Object> map = new HashMap<>();

        if (Objects.nonNull(baseCurrency)){
            map.put("baseCurrency", baseCurrency);
        }
        if (Objects.nonNull(quoteCurrency)){
            map.put("quoteCurrency", quoteCurrency);
        }
        if (Objects.nonNull(rateDate)){
            map.put("rateDate", rateDate);
        }
        if (Objects.nonNull(bid)){
            map.put("bid", bid);
        }
        if (Objects.nonNull(bisLoadedAt)){
            map.put("bisLoadedAt", bisLoadedAt);
        }
        if (Objects.nonNull(skAudInsert)){
            map.put("skAudInsert", skAudInsert);
        }
        if (Objects.nonNull(skAudUpdate)){
            map.put("skAudUpdate", skAudUpdate);
        }
        if (Objects.nonNull(timestampLastUpdate)){
            map.put("timestampLastUpdate", timestampLastUpdate);
        }

        return map;
    }
}
