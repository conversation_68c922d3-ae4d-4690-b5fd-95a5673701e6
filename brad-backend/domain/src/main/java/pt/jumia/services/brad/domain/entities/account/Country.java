package pt.jumia.services.brad.domain.entities.account;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

import java.time.LocalDateTime;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor
public class Country {

    Long id;

    String name;

    String code;

    Currency currency;

    LocalDateTime createdAt;

    String createdBy;

    LocalDateTime updatedAt;

    String updatedBy;

    public Country withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .currency(currency.withoutDbFields())
                .updatedAt(null)
                .updatedBy(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

    @Getter
    public enum SelectFields implements BaseSelectFields {
        ID("id", "id"),
        NAME("name", "name"),
        CODE("code", "code"),
        CURRENCY("currency", "currency"),
        CREATED_AT("createdAt", "createdAt"),
        CREATED_BY("createdBy", "createdBy"),
        UPDATED_AT("updatedAt", "updatedAt"),
        UPDATED_BY("updatedBy", "updatedBy");

        public final String queryField;
        public final String selectCode;

        SelectFields(String queryField, String selectCode) {
            this.queryField = queryField;
            this.selectCode = selectCode;
        }

    }

        public enum SortingFields {
        ID,
        NAME,
        CODE,
        CREATED_AT,
        CREATED_BY,
        UPDATED_AT,
        UPDATED_BY
    }

}
