package pt.jumia.services.brad.domain.entities.filter.transaction;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.filter.shared.GroupFilters;

import java.util.List;
import java.util.Map;

@SuperBuilder(toBuilder = true)
@Getter
public class TransactionGroupFilters extends GroupFilters<Transaction.GroupingFields> {

    public TransactionGroupFilters(List<Transaction.GroupingFields> fields) {
        super(fields);
    }

    @Override
    public Map<String, ?> getAsMap() {
        return Map.ofEntries(
                Map.entry("fields", fields)
        );
    }
}
