package pt.jumia.services.brad.domain.entities.filter.reconciliation;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import pt.jumia.services.brad.domain.enumerations.ReconciliationStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Data
@Builder(toBuilder = true)
@EqualsAndHashCode
@ToString(callSuper = true)
public class ReconciliationFilters {

    private Integer id;
    private ReconciliationStatus status;
    private String creator;
    private LocalDate creationDateStart;
    private LocalDate creationDateEnd;
    private String reviewer;
    private LocalDate reviewDateStart;
    private LocalDate reviewDateEnd;
    private BigDecimal amountTransaction;
    private BigDecimal amountBale;
    private BigDecimal amountThreshold;
    private List<Long> baleIdsList;
    private List<Long> transactionIds;

    public Map<String, ?> getAsMap() {
        HashMap<String, Object> map = new HashMap<>();

        if (!Objects.isNull(this.id)) {
            map.put("id", this.id);
        }
        if (!Objects.isNull(this.status)) {
            map.put("status", this.status);
        }
        if (!Objects.isNull(this.creator)) {
            map.put("creator", this.creator);
        }
        if (!Objects.isNull(this.creationDateStart)) {
            map.put("creationDateStart", this.creationDateStart);
        }
        if (!Objects.isNull(this.creationDateEnd)) {
            map.put("creationDateEnd", this.creationDateEnd);
        }
        if (!Objects.isNull(this.reviewer)) {
            map.put("reviewer", this.reviewer);
        }
        if (!Objects.isNull(this.reviewDateStart)) {
            map.put("reviewDateStart", this.reviewDateStart);
        }
        if (!Objects.isNull(this.reviewDateEnd)) {
            map.put("reviewDateEnd", this.reviewDateEnd);
        }
        if (!Objects.isNull(this.amountTransaction)) {
            map.put("amountTransaction", this.amountTransaction);
        }
        if (!Objects.isNull(this.amountBale)) {
            map.put("amountBale", this.amountBale);
        }
        if (!Objects.isNull(this.amountThreshold)) {
            map.put("amountThreshold", this.amountThreshold);
        }
        if (!Objects.isNull(this.baleIdsList)) {
            map.put("baleIdsList", this.baleIdsList);
        }
        if (!Objects.isNull(this.transactionIds)) {
            map.put("transactionIds", this.transactionIds);
        }

        return map;

    }




}
