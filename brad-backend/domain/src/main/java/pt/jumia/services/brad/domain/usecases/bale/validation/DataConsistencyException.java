package pt.jumia.services.brad.domain.usecases.bale.validation;

/**
 * Exception thrown when data consistency validation fails.
 * Part of the comprehensive exception hierarchy recommended in the audit.
 */
public class DataConsistencyException extends Exception {

    public DataConsistencyException(String message) {
        super(message);
    }

    public DataConsistencyException(String message, Throwable cause) {
        super(message, cause);
    }
}
