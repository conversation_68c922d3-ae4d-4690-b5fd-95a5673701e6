package pt.jumia.services.brad.domain.entities.filter.bale;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.filter.shared.GroupFilters;

import java.util.List;
import java.util.Map;

@SuperBuilder(toBuilder = true)
@Getter
public class BaleGroupFilters extends GroupFilters<Bale.GroupingFields> {

    public BaleGroupFilters(List<Bale.GroupingFields> fields) {
        super(fields);
    }

    @Override
    public Map<String, ?> getAsMap() {
        return Map.ofEntries(
                Map.entry("fields", getGroupFieldsAsString())
        );
    }
}
