package pt.jumia.services.brad.domain.entities.filter.document;


import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class DocumentSortFilters extends SortFilters<Document.SortingFields> {

    public DocumentSortFilters(Document.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
