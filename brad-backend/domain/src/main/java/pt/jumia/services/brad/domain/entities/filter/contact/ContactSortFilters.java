package pt.jumia.services.brad.domain.entities.filter.contact;

import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.account.Contact;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class ContactSortFilters extends SortFilters<Contact.SortingFields> {

    public ContactSortFilters(Contact.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
