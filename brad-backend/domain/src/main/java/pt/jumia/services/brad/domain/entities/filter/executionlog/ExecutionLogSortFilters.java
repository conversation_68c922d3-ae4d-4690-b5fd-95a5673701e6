package pt.jumia.services.brad.domain.entities.filter.executionlog;


import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class ExecutionLogSortFilters extends SortFilters<ExecutionLog.SortingFields> {

    public ExecutionLogSortFilters(ExecutionLog.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
