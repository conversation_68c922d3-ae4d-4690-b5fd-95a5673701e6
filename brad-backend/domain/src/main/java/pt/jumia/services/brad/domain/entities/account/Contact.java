package pt.jumia.services.brad.domain.entities.account;

import lombok.*;
import org.jetbrains.annotations.Nullable;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

import java.time.LocalDateTime;

/**
 * Business representation of Contact
 */
@Value
@Builder(toBuilder = true)
@AllArgsConstructor
public class Contact {

    @Nullable
    Long id;

    @NonNull
    String contactType;

    @NonNull
    String name;

    @NonNull
    String email;

    @NonNull
    String mobilePhoneNumber;

    Account account;

    LocalDateTime createdAt;

    String createdBy;

    LocalDateTime updatedAt;

    String updatedBy;

    public Contact withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .account(account == null ? null : account.withoutDbFields())
                .updatedAt(null)
                .updatedBy(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

    @Getter
    public enum SelectFields implements BaseSelectFields {
        ID("id", "id"),
        CONTACT_TYPE("contactType", "contactType"),
        NAME("name", "name"),
        EMAIL("email", "email"),
        MOBILE_PHONE_NUMBER("mobilePhoneNumber", "mobilePhoneNumber"),
        CREATED_AT("createdAt", "createdAt"),
        CREATED_BY("createdBy", "createdBy"),
        UPDATED_AT("updatedAt", "updatedAt"),
        UPDATED_BY("updatedBy", "updatedBy");

        public final String queryField;
        public final String selectCode;

        SelectFields(String queryField, String selectCode) {
            this.queryField = queryField;
            this.selectCode = selectCode;
        }
    }

    public enum SortingFields {
        ID,
        CONTACT_TYPE,
        NAME,
        EMAIL,
        MOBILE_PHONE_NUMBER,
        CREATED_AT,
        CREATED_BY,
        UPDATED_AT,
        UPDATED_BY
    }

}
