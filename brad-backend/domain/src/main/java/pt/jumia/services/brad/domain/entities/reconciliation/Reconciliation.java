package pt.jumia.services.brad.domain.entities.reconciliation;

import lombok.Builder;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.enumerations.ReconciliationStatus;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Value
@Builder(toBuilder = true)
public class Reconciliation {

    public enum SortingFields {
        ID,
        STATUS,
        CREATOR,
        CREATION_DATE,
        REVIEWER,
        REVIEW_DATE,
        AMOUNT_TRANSACTION,
        AMOUNT_BALE,
        AMOUNT_THRESHOLD
    }
    @Nullable
    Integer id;

    Account account;

    ReconciliationStatus status;

    String creator;

    LocalDateTime creationDate;

    String reviewer;

    LocalDateTime reviewDate;

    BigDecimal amountTransaction;

    BigDecimal amountBale;

    BigDecimal amountThreshold;

    String idCompany;

    List<Long> baleIds;

    List<Long> transactionIds;


}
