package pt.jumia.services.brad.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

import java.time.LocalDateTime;

/**
 * Business representation of Currency
 */
@Value
@Builder(toBuilder = true)
@AllArgsConstructor
public class Currency {

    Long id;
    String name;
    String code;
    String symbol;
    String createdBy;
    LocalDateTime createdAt;
    String updatedBy;
    LocalDateTime updatedAt;

    public Currency withoutDbFields() {

        return this.toBuilder()
                .id(null)
                .updatedAt(null)
                .updatedBy(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

    @Getter
    public enum SelectFields implements BaseSelectFields {
        ID("id", "id"),
        NAME("name", "name"),
        CODE("code", "code"),
        SYMBOL("symbol", "symbol"),
        CREATED_BY("createdBy", "createdBy"),
        CREATED_AT("createdAt", "createdAt"),
        UPDATED_BY("updatedBy", "updatedBy"),
        UPDATED_AT("updatedAt", "updatedAt");

        public final String queryField;
        public final String selectCode;

        SelectFields(String queryField, String selectCode) {
            this.queryField = queryField;
            this.selectCode = selectCode;
        }
    }

}
