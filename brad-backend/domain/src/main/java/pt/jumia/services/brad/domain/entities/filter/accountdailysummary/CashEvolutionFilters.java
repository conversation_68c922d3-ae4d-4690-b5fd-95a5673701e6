package pt.jumia.services.brad.domain.entities.filter.accountdailysummary;

import java.time.LocalDate;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
public class CashEvolutionFilters extends CommonFilters {

    @NotNull
    private LocalDate evolutionFromDate;
    @NotNull
    private LocalDate evolutionToDate;
    @Builder.Default
    private AggregateBy evolutionAggregateBy = AggregateBy.DAILY;
    @NotNull
    private Boolean isAggregatedByPeriod;

    public enum AggregateBy {
        DAILY,
        MONTHLY,
        QUARTERLY,
        YEARLY,
    }

}

