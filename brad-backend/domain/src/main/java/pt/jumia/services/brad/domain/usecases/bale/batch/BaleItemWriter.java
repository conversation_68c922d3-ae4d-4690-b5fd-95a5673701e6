package pt.jumia.services.brad.domain.usecases.bale.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCase;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Enhanced BaleItemWriter with consistent error handling and structured logging.
 * Implements audit recommendations for error handling consistency and observability.
 * 
 * Error Handling Strategy:
 * - DatabaseErrorsException: Propagate for retry (transient database issues)
 * - Other exceptions: Fail the chunk (data integrity issues)
 * - Structured logging with correlation IDs and processing context
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BaleItemWriter implements ItemWriter<Bale> {

    private final SyncBradBaleUseCase syncBradBaleUseCase;

    @Override
    public void write(Chunk<? extends Bale> chunk) throws Exception {
        List<? extends Bale> bales = chunk.getItems();
        
        if (bales.isEmpty()) {
            log.debug("No bales to write in this chunk");
            return;
        }

        // Generate correlation ID for tracking this chunk
        String correlationId = UUID.randomUUID().toString();
        MDC.put("correlationId", correlationId);
        MDC.put("chunkSize", String.valueOf(bales.size()));
        
        try {
            // Extract entry numbers for better error reporting
            List<Integer> entryNumbers = bales.stream()
                .map(Bale::getEntryNo)
                .collect(Collectors.toList());
            
            MDC.put("entryNumbers", entryNumbers.toString());
            
            log.info("Writing chunk of {} bales to Brad database - entries: {}", 
                bales.size(), entryNumbers);
            
            long startTime = System.currentTimeMillis();
            
            List<Bale> syncedBales = syncBradBaleUseCase.execute((List<Bale>) bales);
            
            long processingTime = System.currentTimeMillis() - startTime;
            MDC.put("processingTimeMs", String.valueOf(processingTime));

            log.info("Successfully synced {} bales to Brad database in {}ms", 
                syncedBales.size(), processingTime);

            // Validate sync results
            if (syncedBales.size() != bales.size()) {
                log.warn("Partial sync detected - expected: {}, actual: {}, missing: {}",
                    bales.size(), syncedBales.size(), (bales.size() - syncedBales.size()));
                
                // This is a data integrity issue, should fail the chunk
                throw new RuntimeException(String.format(
                    "Partial sync detected: expected %d bales but only %d were synced",
                    bales.size(), syncedBales.size()));
            }

        } catch (DatabaseErrorsException e) {
            // Database errors should be retried
            log.error("Database error writing chunk of {} bales - will retry: {}", 
                bales.size(), e.getMessage());
            throw e;
            
        } catch (Exception e) {
            // Other exceptions indicate data integrity issues - fail the chunk
            log.error("Failed to write chunk of {} bales to Brad database - failing chunk: {}", 
                bales.size(), e.getMessage(), e);
            throw e;
            
        } finally {
            // Clean up MDC
            MDC.remove("correlationId");
            MDC.remove("chunkSize");
            MDC.remove("entryNumbers");
            MDC.remove("processingTimeMs");
        }
    }
}
