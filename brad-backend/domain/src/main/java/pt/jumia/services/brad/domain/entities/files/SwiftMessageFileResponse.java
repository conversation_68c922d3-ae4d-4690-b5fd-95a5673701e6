package pt.jumia.services.brad.domain.entities.files;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.ToString;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;
import pt.jumia.services.brad.domain.entities.Transaction;

import java.util.List;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor
@ToString
public class SwiftMessageFileResponse {

    private final AccountStatement statement;
    private final List<Transaction> transactions;
    private final String accountNumber;
    private final ProcessingStatus processingStatus;
    private final String statusDescription;

}
