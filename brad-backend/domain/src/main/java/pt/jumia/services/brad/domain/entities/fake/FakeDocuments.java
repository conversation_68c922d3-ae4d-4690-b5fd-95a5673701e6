package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.enumerations.DocumentType;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public interface FakeDocuments {

    Document FAKE_DOCUMENT = Document.builder()
            .documentType(DocumentType.OTHERS)
            .name("fakeName")
            .description("fakeDescription")
            .url("fakeUrl")
            .fileKey("fakeFileKey")
            .file("fakeFile")
            .account(FakeAccounts.getFakeAccounts(1, null).get(0))
            .createdAt(LocalDateTime.now())
            .createdBy("fakeUser")
            .updatedAt(LocalDateTime.now())
            .updatedBy("fakeUser")
            .build();



    static List<Document> getFakeDocuments(int amount) {
        List<Account> fakeAccounts = FakeAccounts.getFakeAccounts(amount, null);
        List<Document> fakeDocuments = new ArrayList<>();
        for (int i = 1; i <= amount; i++) {
            fakeDocuments.add(FAKE_DOCUMENT.toBuilder()
                    .documentType(DocumentType.OTHERS)
                    .name(FAKE_DOCUMENT.getName() + i)
                    .description(FAKE_DOCUMENT.getDescription() + i)
                    .url(FAKE_DOCUMENT.getUrl() + i)
                    .fileKey(FAKE_DOCUMENT.getFileKey() + i)
                    .file(FAKE_DOCUMENT.getFile() + i)
                    .account(fakeAccounts.get(i - 1))
                    .createdAt(LocalDateTime.now())
                    .createdBy(FAKE_DOCUMENT.getCreatedBy() + i)
                    .updatedAt(LocalDateTime.now())
                    .updatedBy(FAKE_DOCUMENT.getUpdatedBy() + i)
                    .build());
        }

        return fakeDocuments;
    }
}
