package pt.jumia.services.brad.domain.entities.monitoring;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Ana Correia at 30/07/2019.
 */
public class GroupCounter {

    private static final Logger LOGGER = LoggerFactory.getLogger(GroupCounter.class);
    private static final String INVALID_KEY_MESSAGE_FORMAT =
            "Incrementing the group counter '%s' failed: there is no counter with the key '%s'.";

    private final String name;
    private final Map<String, Counter> counters = new HashMap<>();

    public GroupCounter(
            String name, String description, Map<String, List<Tag>> countersKeysTags, MeterRegistry registry) {

        this.name = name;

        for (Map.Entry<String, List<Tag>> counterKeyTags : countersKeysTags.entrySet()) {
            Counter counter = Counter
                    .builder(name)
                    .description(description)
                    .tags(counterKeyTags.getValue())
                    .register(registry);

            counters.put(counterKeyTags.getKey(), counter);
        }
    }

    public void increment(String key) {
        if (counters.containsKey(key)) {
            counters.get(key).increment();
        } else {
            String message = String.format(INVALID_KEY_MESSAGE_FORMAT, name, key);
            LOGGER.warn(message);
        }
    }

    public void increment(String key, double amount) {
        if (counters.containsKey(key)) {
            counters.get(key).increment(amount);
        } else {
            String message = String.format(INVALID_KEY_MESSAGE_FORMAT, name, key);
            LOGGER.warn(message);
        }
    }

    public double getCounterValue(String key) {
        if (!counters.containsKey(key)) {
            throw new RuntimeException("Counter with key '" + key + "' not found");
        }

        return counters.get(key).count();
    }
}
