package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.enumerations.ReconciliationStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public interface FakeReconciliation {

    Account FAKE_BANK_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0);

    Reconciliation FAKE_RECONCILIATION = Reconciliation.builder()
            .id(1)
            .account(FAKE_BANK_ACCOUNT)
            .status(ReconciliationStatus.APPROVED)
            .creator("creator")
            .creationDate(LocalDateTime.now())
            .amountTransaction(BigDecimal.TEN)
            .amountBale(BigDecimal.TEN)
            .amountThreshold(BigDecimal.TEN)
            .idCompany(FAKE_BANK_ACCOUNT.getCompanyID())
            .baleIds(List.of(1L))
            .transactionIds(List.of(1L))
            .build();

    static List<Reconciliation> getFakeReconciliation(int amount) {

        List<Reconciliation> fakeReconciliation = new ArrayList<>();
        for (int i = 1; i <= amount; i++) {
            fakeReconciliation.add(FAKE_RECONCILIATION.toBuilder()
                    .id(i)
                    .build());
        }
        return fakeReconciliation;

    }


}
