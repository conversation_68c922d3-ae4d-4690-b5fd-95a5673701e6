package pt.jumia.services.brad.domain.entities.group;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class GroupInfo {
    private BigDecimal totalBalance;
    private Long totalQuantity;
    private String status;
    private Boolean isStatusShared;
    private Boolean isReconciliationShared;
    private List<Long> allIds;

    private Long statusAmount;
}
