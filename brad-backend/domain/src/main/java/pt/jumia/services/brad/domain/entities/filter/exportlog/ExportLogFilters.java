package pt.jumia.services.brad.domain.entities.filter.exportlog;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.CollectionUtils;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.ExportLog;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class ExportLogFilters {

    @Nullable
    private String filterText;

    @Nullable
    private List<ExportLog.Type> types;

    @Nullable
    private List<ExportLog.Status> status;

    @Nullable
    private LocalDateTime createdAtFrom;

    @Nullable
    private LocalDateTime createdAtTo;

    @Nullable
    private List<String> countryCodes;

    /**
     * @return all the fields inside a map, which can be useful for testing
     */
    public Map<String, ?> getAsMap() {
        HashMap<String, Object> map = new HashMap<>();

        if (this.filterText != null) {
            map.put("filterText", String.valueOf(this.filterText));
        }
        if (this.types != null) {
            map.put("types", this.types);
        }
        if (this.status != null) {
            map.put("status", this.status);
        }
        if (this.createdAtFrom != null) {
            map.put("createdAtFrom", this.createdAtFrom.format(DateTimeFormatter.ofPattern(DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)));
        }
        if (this.createdAtTo != null) {
            map.put("createdAtTo", this.createdAtTo.format(DateTimeFormatter.ofPattern(DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)));
        }
        if (!CollectionUtils.isEmpty(countryCodes)) {
            countryCodes.forEach(countryId -> map.put("countryCodes", countryCodes));
        }
        return map;
    }
}
