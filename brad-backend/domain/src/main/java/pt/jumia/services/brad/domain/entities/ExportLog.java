package pt.jumia.services.brad.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;
import java.util.List;


@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ExportLog {

    public enum SortingFields {
        ID, TYPE, FILE_URL, FILE_NAME, ROW_COUNT, STATUS, EXECUTION_TIME, CREATED_AT, UPDATED_AT
    }

    @Nullable
    private Long id;

    private Type type;

    @Nullable
    private String fileUrl;

    @Nullable
    private String fileName;

    @Nullable
    private Integer rowCount;

    @Builder.Default
    private Status status = Status.PENDING;

    @Nullable
    private Long executionTime;

    @Nullable
    private String filters;

    @Nullable
    private LocalDateTime createdAt;

    @Nullable
    private String createdBy;

    private List<String> countries;

    @Nullable
    private LocalDateTime updatedAt;

    public enum Status {
        PENDING, RUNNING, COMPLETED, FAILED
    }

    public enum Type {
        BANK_ACCOUNTS
    }

    public ExportLog withoutDbFields() {
        return toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .updatedAt(null)
                .build();
    }
}
