package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public interface FakeAccountStatements {

    AccountStatement FAKE_ACCOUNT_STATEMENT = AccountStatement.builder()
        .id(1L)
        .currency(FakeCurrencies.KES)
        .initialDate(LocalDate.now())
        .finalDate(LocalDate.now())
        .statementId("1")
        .initialAmount(BigDecimal.ZERO)
        .finalAmount(BigDecimal.TEN)
        .initialDirection(Direction.CREDIT)
        .finalDirection(Direction.CREDIT)
        .account(FakeAccounts.FAKE_ACCOUNT)
        .status(AccountStatementStatus.OPEN)
        .statusDescription(AccountStatementStatus.Description.IMPORTED)
        .fxRates(Set.of())
        .description("description")
        .createdAt(LocalDateTime.now())
        .createdBy("fakeUser")
        .updatedAt(LocalDateTime.now())
        .updatedBy("fakeUser")
        .build();

    static List<AccountStatement> getFakeAccountStatements(int amount, Account account) {
        List<AccountStatement> fakeAccountStatements = new ArrayList<>();
        for (int i = 1; i <= amount; i++) {
            fakeAccountStatements.add(FAKE_ACCOUNT_STATEMENT.toBuilder()
                    .id(FAKE_ACCOUNT_STATEMENT.getId() + i)
                    .currency(account.getCurrency())
                    .initialDate(LocalDate.now())
                    .finalDate(LocalDate.now())
                    .statementId(FAKE_ACCOUNT_STATEMENT.getStatementId() + i)
                    .initialAmount(FAKE_ACCOUNT_STATEMENT.getInitialAmount())
                    .finalAmount(FAKE_ACCOUNT_STATEMENT.getFinalAmount())
                    .account(account)
                    .status(AccountStatementStatus.OPEN)
                    .statusDescription(FAKE_ACCOUNT_STATEMENT.getStatusDescription())
                    .description(FAKE_ACCOUNT_STATEMENT.getDescription())
                    .createdAt(LocalDateTime.now())
                    .createdBy(FAKE_ACCOUNT_STATEMENT.getCreatedBy())
                    .updatedAt(LocalDateTime.now())
                    .updatedBy(FAKE_ACCOUNT_STATEMENT.getUpdatedBy())
                    .build());
        }

        return fakeAccountStatements;
    }

}
