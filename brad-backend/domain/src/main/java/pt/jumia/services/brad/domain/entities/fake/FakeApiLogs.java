package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.ApiLog;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public interface FakeApiLogs {

    ApiLog FAKE_API_LOG = ApiLog.builder()
            .id(1L)
            .logType(ApiLog.ApiLogType.BANK_STATEMENT_CREATION)
            .request("fakeRequest")
            .response("fakeResponse")
            .logStatus(ApiLog.ApiLogStatus.SUCCESS)
            .createdAt(LocalDateTime.now())
            .createdBy("fakeUser")
            .updatedAt(LocalDateTime.now())
            .updatedBy("fakeUser")
            .build();

    static List<ApiLog> getFakeApiLogs(int amount) {
        List<ApiLog> fakeApiLogs = new ArrayList<>();
        for (int i = 1; i <= amount; i++) {
            fakeApiLogs.add(FAKE_API_LOG.toBuilder()
                    .id((long) i)
                    .logType(ApiLog.ApiLogType.BANK_STATEMENT_CREATION)
                    .request(FAKE_API_LOG.getRequest() + i)
                    .response(FAKE_API_LOG.getResponse() + i)
                    .logStatus(ApiLog.ApiLogStatus.SUCCESS)
                    .createdAt(LocalDateTime.now())
                    .createdBy(FAKE_API_LOG.getCreatedBy() + i)
                    .updatedAt(LocalDateTime.now())
                    .updatedBy(FAKE_API_LOG.getUpdatedBy() + i)
                    .build());
        }

        return fakeApiLogs;
    }
}
