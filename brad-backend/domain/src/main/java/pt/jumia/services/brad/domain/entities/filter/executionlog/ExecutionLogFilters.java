package pt.jumia.services.brad.domain.entities.filter.executionlog;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.shared.BaseFilters;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ExecutionLogFilters extends BaseFilters {

    private Long id;
    private List<String> logType;
    private List<String> logStatus;
    private Integer recordsAmount;
    private LocalDateTime executionStartTime;
    private LocalDateTime executionEndTime;
    private String relatedEntity;


    public Map<String, ?> getAsMap(){
        HashMap<String, Object> map = new HashMap<>();

        if (Objects.nonNull(id)) {
            map.put("id", id);
        }
        if (Objects.nonNull(logType)) {
            map.put("logType", logType);
        }
        if (Objects.nonNull(logStatus)) {
            map.put("logStatus", logStatus);
        }
        if (Objects.nonNull(recordsAmount)) {
            map.put("recordsAmount", recordsAmount);
        }
        if (Objects.nonNull(executionStartTime)) {
            map.put("executionStartTime", executionStartTime);
        }
        if (Objects.nonNull(executionEndTime)) {
            map.put("executionEndTime", executionEndTime);
        }
        if (Objects.nonNull(relatedEntity)) {
            map.put("relatedEntity", relatedEntity);
        }

        return map;
    }
}
