package pt.jumia.services.brad.domain.entities.filter.account;


import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class AccountSortFilters extends SortFilters<Account.SortingFields> {

    public AccountSortFilters(Account.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
