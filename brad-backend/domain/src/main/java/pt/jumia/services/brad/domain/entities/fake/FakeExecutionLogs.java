package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.ExecutionLog;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public interface FakeExecutionLogs {

    ExecutionLog FAKE_API_LOG = ExecutionLog.builder()
            .id(1L)
            .logType(ExecutionLog.ExecutionLogType.BALE)
            .recordsAmount(10)
            .executionStartTime(LocalDateTime.now())
            .executionEndTime(LocalDateTime.now())
            .appliedFilters("appliedFilters")
            .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
            .query("query")
            .build();

    static List<ExecutionLog> getFakeExecutionLogs(int amount) {
        List<ExecutionLog> fakeExecutionLogs = new ArrayList<>();
        for (int i = 1; i <= amount; i++) {
            fakeExecutionLogs.add(FAKE_API_LOG.toBuilder()
                    .id((long) i)
                    .recordsAmount(i)
                    .executionStartTime(LocalDateTime.now().minusDays(i))
                    .executionEndTime(LocalDateTime.now().minusDays(i))
                    .appliedFilters("appliedFilters" + i)
                    .query("query" + i)
                    .build());
        }

        return fakeExecutionLogs;
    }
}
