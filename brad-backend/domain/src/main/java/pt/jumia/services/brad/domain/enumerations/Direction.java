package pt.jumia.services.brad.domain.enumerations;

import com.prowidesoftware.swift.model.mx.dic.CreditDebitCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.text.MessageFormat;

@Getter
@RequiredArgsConstructor
public enum Direction {
    DEBIT(1), CREDIT(2);

    private final int value;


    public static Direction getDirection(final String dir) {

        if (dir != null) {
            for (Direction direction : Direction.values()) {
                if (direction.name().equalsIgnoreCase(dir)) {
                    return direction;
                }
                try {
                    if (direction.getValue() == Integer.parseInt(dir)) {
                        return direction;
                    }
                } catch (Exception ignored) {
                }
            }
        }
        if (dir == null || dir.isEmpty()) {
            return null;
        }
        throw new IllegalArgumentException(MessageFormat.format("Invalid direction value: {0}", dir));
    }

    public static Direction fromString(String dcMark) {
        if ("D".equals(dcMark)) {
            return Direction.DEBIT;
        } else if ("C".equals(dcMark)) {
            return Direction.CREDIT;
        } else {
            throw new IllegalArgumentException("Invalid direction mark: " + dcMark);
        }
    }

    public static Direction getTranslatedDirectionForMXFile(CreditDebitCode creditDebitCode) {
        switch (creditDebitCode) {
            case CRDT -> {
                return Direction.CREDIT;
            }
            case DBIT -> {
                return Direction.DEBIT;
            }
        }
        return null;
    }
}
