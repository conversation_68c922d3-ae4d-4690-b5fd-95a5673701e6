package pt.jumia.services.brad.domain.entities.reconciliation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Country;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Business representation of Threshold
 */
@Value
@Builder(toBuilder = true)
@AllArgsConstructor
public class Threshold {

    public enum SortingFields {
        ID,
        CURRENCY,
        COUNTRY,
        AMOUNT
    }

    Long id;
    Currency currency;
    Country country;
    BigDecimal amount;
    String createdBy;
    LocalDateTime createdAt;
    String updatedBy;
    LocalDateTime updatedAt;

    public Threshold withoutDbFields() {

        return this.toBuilder()
                .id(null)
                .updatedAt(null)
                .updatedBy(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

}
