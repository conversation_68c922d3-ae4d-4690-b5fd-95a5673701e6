package pt.jumia.services.brad.domain.entities.filter.shared;

import java.util.HashMap;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.Filter;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

import java.util.Map;

@Data
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
public abstract class SortFilters<E extends Enum<E>> implements Filter {


    protected E field;

    protected E secondaryField;

    @Builder.Default
    private OrderDirection direction = OrderDirection.DESC;

    @Override
    public Map<String, ?> getAsMap() {

        final Map<String, String> filtersMap = new HashMap<>();
        filtersMap.put("orderField", field.name());
        filtersMap.put("orderDirection", direction.name());

        if (Objects.nonNull(secondaryField)) {
            filtersMap.put("secondaryField", secondaryField.name());
        }
        return filtersMap;
    }
}
