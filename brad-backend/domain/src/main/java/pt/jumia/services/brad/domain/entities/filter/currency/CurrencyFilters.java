package pt.jumia.services.brad.domain.entities.filter.currency;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.shared.BaseFilters;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CurrencyFilters extends BaseFilters {

    private String filterText;

    public Map<String, ?> getAsMap() {
        HashMap<String, Object> map = new HashMap<>();
        if (!Objects.isNull(this.filterText)) {
            map.put("filterText", this.filterText);
        }

        return map;
    }
}
