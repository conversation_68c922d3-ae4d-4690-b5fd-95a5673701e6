package pt.jumia.services.brad.domain.entities.filter;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageFilters implements Filter {

    @Builder.Default
    private Integer page = 1;

    @Builder.Default
    private Integer size = 10;
    

    public Map<String, String> getAsMap() {
        HashMap<String, String> map = new HashMap<>();
        map.put("page", String.valueOf(page));
        map.put("size", String.valueOf(size));
        return map;
    }
}
