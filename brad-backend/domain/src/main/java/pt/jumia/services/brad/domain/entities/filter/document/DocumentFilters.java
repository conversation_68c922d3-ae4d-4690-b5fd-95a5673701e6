package pt.jumia.services.brad.domain.entities.filter.document;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.filter.shared.BaseFilters;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DocumentFilters extends BaseFilters {

    private List<String> types;
    private String name;
    private String description;
    private Long accountId;
    private String file;

    public Map<String, ?> getAsMap(){
        HashMap<String, Object> map = new HashMap<>();

        if (!Objects.isNull(this.types)){
            map.put("documentTypes", this.types);
        }
        if (!Objects.isNull(this.name)){
            map.put("name", this.name);
        }
        if (!Objects.isNull(this.description)){
            map.put("description", this.description);
        }
        if (!Objects.isNull(this.accountId)){
            map.put("accountID", this.accountId);
        }
        if (!Objects.isNull(this.file)){
            map.put("accountID", this.file);
        }

        return map;
    }


}
