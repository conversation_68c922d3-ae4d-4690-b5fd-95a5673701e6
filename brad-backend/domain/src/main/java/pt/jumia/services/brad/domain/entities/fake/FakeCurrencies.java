package pt.jumia.services.brad.domain.entities.fake;
import pt.jumia.services.brad.domain.entities.Currency;

import java.time.LocalDateTime;
import java.util.List;

public interface FakeCurrencies {

    String FAKE_USER = "fakeUser";

    Currency NGN = Currency.builder()
            .id(1L)
            .name("Nigerian Naira")
            .code("NGN")
            .symbol("₦")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency EGP = Currency.builder()
            .id(2L)
            .name("Egyptian Pound")
            .code("EGP")
            .symbol("E£")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency KES = Currency.builder()
            .id(3L)
            .name("Kenyan Shilling")
            .code("KES")
            .symbol("KES")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency MAD = Currency.builder()
            .id(4L)
            .name("Moroccan Dirham")
            .code("MAD")
            .symbol("MAD")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency CFA = Currency.builder()
            .id(5L)
            .name("West African CFA franc")
            .code("CFA")
            .symbol("CFA")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency DZD = Currency.builder()
            .id(6L)
            .name("Algerian Dinar")
            .code("DZD")
            .symbol("DZD")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency GHS = Currency.builder()
            .id(7L)
            .name("Ghanaian Cedi")
            .code("GHS")
            .symbol("GHS")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency XOF = Currency.builder()
            .id(8L)
            .name("Senegalese CFA franc")
            .code("XOF")
            .symbol("XOF")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency TND = Currency.builder()
            .id(9L)
            .name("Tunisian Dinar")
            .code("TND")
            .symbol("TND")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency UGX = Currency.builder()
            .id(10L)
            .name("Ugandan Shilling")
            .code("UGX")
            .symbol("UGX")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();
    
    Currency ZAR = Currency.builder()
            .id(11L)
            .name("South African Rand")
            .code("ZAR")
            .symbol("ZAR")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency USD = Currency.builder()
            .id(12L)
            .name("US Dollar")
            .code("USD")
            .symbol("$")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency EUR = Currency.builder()
            .id(13L)
            .name("Euro")
            .code("EUR")
            .symbol("€")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency CNY = Currency.builder()
            .id(14L)
            .name("Chinese Yuan")
            .code("CNY")
            .symbol("¥")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();

    Currency AED = Currency.builder()
            .id(15L)
            .name("United Arab Emirates Dirham")
            .code("AED")
            .symbol("AED")
            .createdAt(LocalDateTime.now())
            .createdBy(FAKE_USER)
            .updatedAt(LocalDateTime.now())
            .updatedBy(FAKE_USER)
            .build();



    List<Currency> ALL_CURRENCIES = List.of(NGN, EGP, KES, MAD, CFA, DZD, GHS, XOF, TND, UGX, ZAR, USD, EUR);

}
