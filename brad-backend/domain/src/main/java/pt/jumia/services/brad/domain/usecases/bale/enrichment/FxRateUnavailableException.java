package pt.jumia.services.brad.domain.usecases.bale.enrichment;

/**
 * Exception thrown when FX rate retrieval fails.
 * Part of the specific exception hierarchy recommended in the audit.
 */
public class FxRateUnavailableException extends Exception {

    public FxRateUnavailableException(String message) {
        super(message);
    }

    public FxRateUnavailableException(String message, Throwable cause) {
        super(message, cause);
    }
}
