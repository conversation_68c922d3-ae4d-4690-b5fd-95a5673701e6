package pt.jumia.services.brad.domain.entities.filter.audit;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import pt.jumia.services.brad.domain.entities.AuditedEntity;
import pt.jumia.services.brad.domain.entities.filter.Filter;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

import java.util.Map;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class AuditedEntitySortFilters implements Filter {

    @Builder.Default
    private AuditedEntity.SortingFields field = AuditedEntity.SortingFields.REV;
    @Builder.Default
    private OrderDirection direction = OrderDirection.DESC;

    @Override
    public Map<String, ?> getAsMap() {
        return Map.ofEntries(
                Map.entry("orderField", field.name()),
                Map.entry("orderDirection", direction.name())
        );
    }
}
