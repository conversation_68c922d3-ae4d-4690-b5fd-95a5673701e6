package pt.jumia.services.brad.domain.entities.reconciliation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;

import java.math.BigDecimal;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor
public class DifferenceBetweenThreshold {

    BigDecimal transactionAmount;
    BigDecimal baleAmount;
    BigDecimal amountDifference;
    BigDecimal thresholdAmount;
    Boolean isBetweenThreshold;


    public DifferenceBetweenThreshold(BigDecimal transactionAmount, BigDecimal baleAmount, BigDecimal thresholdAmount) {
        this.transactionAmount = transactionAmount;
        this.baleAmount = baleAmount;
        this.amountDifference = transactionAmount.subtract(baleAmount);
        this.thresholdAmount = thresholdAmount;
        this.isBetweenThreshold = amountDifference.abs().compareTo(thresholdAmount) <= 0;
    }
}
