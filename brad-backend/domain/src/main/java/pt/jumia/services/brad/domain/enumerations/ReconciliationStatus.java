package pt.jumia.services.brad.domain.enumerations;

public enum ReconciliationStatus {

    PENDING, APPROVED, REJECTED;

    public static ReconciliationStatus fromString(String value) {
        if (value != null) {
            for (ReconciliationStatus status : ReconciliationStatus.values()) {
                if (value.equalsIgnoreCase(status.toString())) {
                    return status;
                }
            }
        }
        throw new IllegalArgumentException("Invalid ReconciliationStatus value: " + value);
    }

}
