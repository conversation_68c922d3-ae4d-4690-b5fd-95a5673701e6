package pt.jumia.services.brad.domain.entities.filter.accountstatementfile;

import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class AccountStatementFileSortFilters extends SortFilters<AccountStatementFile.SortingFields> {

    public AccountStatementFileSortFilters(AccountStatementFile.SortingFields field, OrderDirection direction) {

        super(field, field, direction);
    }

}
