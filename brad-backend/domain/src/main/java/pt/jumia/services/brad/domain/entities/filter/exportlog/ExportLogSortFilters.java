package pt.jumia.services.brad.domain.entities.filter.exportlog;

import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.ExportLog;
import pt.jumia.services.brad.domain.entities.filter.shared.SortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@SuperBuilder(toBuilder = true)
public class ExportLogSortFilters extends SortFilters<ExportLog.SortingFields> {

    public ExportLogSortFilters(ExportLog.SortingFields field, OrderDirection direction) {
        super(field, field, direction);
    }
}
