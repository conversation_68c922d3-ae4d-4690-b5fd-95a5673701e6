package pt.jumia.services.brad.domain.usecases.bale;

/**
 * Base exception class for all bale processing related exceptions.
 * Part of the comprehensive exception hierarchy recommended in the audit.
 */
public class BaleProcessingException extends Exception {

    public BaleProcessingException(String message) {
        super(message);
    }

    public BaleProcessingException(String message, Throwable cause) {
        super(message, cause);
    }
}
