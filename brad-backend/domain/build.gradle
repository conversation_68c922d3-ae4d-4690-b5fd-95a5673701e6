apply plugin: 'idea'
apply from: '../config/quality/quality.gradle'

dependencies {

    api "org.jetbrains:annotations-java5:23.0.0"

    // Acl lib
    api group: 'com.jumia.services', name: 'acl-lib', version: "${aclLibVersion}"

    api group: 'com.github.spotbugs', name: 'spotbugs-annotations', version: "${spotbugsVersion}"

    //so we can use spring DI
    implementation ("org.springframework:spring-context:${springVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    testImplementation "org.springframework:spring-test:${springVersion}"
    //and load properties
    implementation "org.springframework.boot:spring-boot:${springBootVersion}"
    
    // Spring Batch for batch processing
    implementation "org.springframework.boot:spring-boot-starter-batch:${springBootVersion}"

    // needed to initialize defaultReader (for the tests to not fail in intellij)
    implementation group: 'com.jayway.jsonpath', name: 'json-path', version: '2.7.0'

    // needed to support for MT940 format
    // https://mvnrepository.com/artifact/com.prowidesoftware/pw-iso20022
    implementation group: 'com.prowidesoftware', name: 'pw-iso20022', version: 'SRU2021-9.2.6'

    //The files in this package implement JSON encoders/decoders in Java
    // https://mvnrepository.com/artifact/org.json/json
    implementation group: 'org.json', name: 'json', version: '20090211'

    implementation group: 'com.opencsv', name: 'opencsv', version: '4.6'

    // Prometheus client
    // https://mvnrepository.com/artifact/io.micrometer/micrometer-registry-prometheus
    api group: 'io.micrometer', name: 'micrometer-registry-prometheus', version: '1.10.3'

    //logs
    api group: 'org.slf4j', name: 'slf4j-api', version: '1.7.30'

    // json converter
    implementation group: 'com.google.code.gson', name: 'gson', version: '2.10.1'

    // commons
    api "pt.aig.aigx.commons:common-utils:0.0.6-RELEASE"

    //new relic, for monitoring
    implementation group: 'com.newrelic.agent.java', name: 'newrelic-api', version: '7.11.0'

    testImplementation "org.mockito:mockito-junit-jupiter:${mockitoVersion}"
    testImplementation "org.assertj:assertj-core:${assertJVersion}"
    //enable a logging framework in the tests
    testImplementation group: 'org.slf4j', name: 'slf4j-simple', version: '2.0.6'
    
    // Spring Batch test dependencies
    testImplementation "org.springframework.batch:spring-batch-test:5.0.0"

    // https://mvnrepository.com/artifact/org.quartz-scheduler/quartz
    implementation 'org.quartz-scheduler:quartz:2.3.0'

    implementation 'javax.xml.bind:jaxb-api:2.3.1'
    implementation 'com.sun.xml.bind:jaxb-core:2.3.0.1'
    implementation 'com.sun.xml.bind:jaxb-impl:2.3.3'

    // Country codes
    api "com.neovisionaries:nv-i18n:1.28"

    // Spring ORM
    implementation "org.springframework:spring-orm:${springVersion}"

    // Jackson datatypes
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.0'

    // Spring Batch core for job launcher and job interfaces
    implementation "org.springframework.batch:spring-batch-core:5.0.0"
}


idea {
    module {
        sourceDirs += file("$buildDir/classes/java")
    }
}
