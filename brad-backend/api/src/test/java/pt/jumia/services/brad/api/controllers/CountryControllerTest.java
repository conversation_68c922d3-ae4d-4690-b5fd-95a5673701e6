package pt.jumia.services.brad.api.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.api.csvs.CsvBuilder;
import pt.jumia.services.brad.api.payloads.request.country.CountryApiRequestPayload;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.countries.CreateCountriesUseCase;
import pt.jumia.services.brad.domain.usecases.countries.DeleteCountriesUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;
import pt.jumia.services.brad.domain.usecases.countries.UpdateCountriesUseCase;

import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
@WebMvcTest(CountryController.class)
public class CountryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private CreateCountriesUseCase createCountriesUseCase;

    @MockBean
    private ReadCountriesUseCase readCountriesUseCase;

    @MockBean
    private UpdateCountriesUseCase updateCountriesUseCase;

    @MockBean
    private DeleteCountriesUseCase deleteCountriesUseCase;

    @MockBean
    private CsvBuilder csvBuilder;


    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);
    }


    //Create Account Tests

    @Test
    public void testSimpleMapping_success() throws Exception {

        when(createCountriesUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        CountryApiRequestPayload payload = new CountryApiRequestPayload(FakeCountries.NIGERIA);
        mockMvc
                .perform(post("/api/countries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvalidContent() throws Exception {
        when(createCountriesUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        mockMvc
                .perform(post("/api/countries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("Invalid Payload"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.globalError",
                        containsString("JSON parse error: Unrecognized token 'Invalid'")))
                .andReturn();
    }

    @Test
    public void testConflict() throws Exception {

        when(createCountriesUseCase.execute(any()))
                .thenThrow(new ConstraintViolationException("Test error", Collections.emptySet()));

        CountryApiRequestPayload payload = new CountryApiRequestPayload(FakeCountries.NIGERIA);
        mockMvc
                .perform(post("/api/countries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andReturn();
    }

    @Test
    public void createCountry_failure_AlreadyExists() throws Exception {

        when(createCountriesUseCase.execute(any()))
                .thenThrow(AlreadyExistsException.createAlreadyExists(Country.class, "1"));
        CountryApiRequestPayload payload = new CountryApiRequestPayload(FakeCountries.NIGERIA);
        mockMvc
                .perform(post("/api/countries")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isConflict());
    }

    //Fetch Countries Tests

    @Test
    public void testFetch_success() throws Exception {


        List<Country> countries = FakeCountries.ALL_COUNTRIES;

        when(readCountriesUseCase.executeCountries())
                .thenReturn(countries);



        mockMvc
                .perform(get("/api/countries"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetch_emptyList_success() throws Exception {


        when(readCountriesUseCase.executeCountries())
                .thenReturn(Collections.emptyList());

        mockMvc
                .perform(get("/api/countries"))
                .andExpect(status().isOk())
                .andReturn();
    }



    //Fetch Country by ID Tests

    @Test
    public void testFetchById_validId_success() throws Exception {


        Country mockCountry = FakeCountries.NIGERIA;


        when(readCountriesUseCase.execute(1L)).thenReturn(mockCountry);

        mockMvc.perform(get("/api/countries/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(mockCountry.getId()))
                .andExpect(jsonPath("$.name").value(mockCountry.getName()))
                .andExpect(jsonPath("$.code").value(mockCountry.getCode()));

    }

    @Test
    public void testFetchById_invalidId_notFoundException() throws Exception {


        when(readCountriesUseCase.execute(2L)).thenThrow(new NotFoundException("Country not found"));

        mockMvc.perform(get("/api/countries/2"))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testFetchById_nonIntegerId_methodArgumentTypeMismatchException() throws Exception {


        mockMvc.perform(get("/api/countries/abc"))
                .andExpect(status().isBadRequest())
                .andExpect(result -> assertTrue(result.getResolvedException() instanceof MethodArgumentTypeMismatchException));
    }




    //Delete Account Tests

    @Test
    public void testDeleteSuccess() throws Exception {

        int id = 1;
        when(readCountriesUseCase.execute((long) id)).thenReturn(FakeCountries.NIGERIA);
        doNothing().when(deleteCountriesUseCase).execute(id);
        mockMvc.perform(delete("/api/countries/{id}", id))
                .andExpect(status().isNoContent());
    }

    @Test
    public void testDeleteNonExisting() throws Exception {

        int id = 1;
        when(readCountriesUseCase.execute((long) id)).thenReturn(FakeCountries.NIGERIA);
        doThrow(new NotFoundException("Country not found")).when(deleteCountriesUseCase).execute(id);
        mockMvc.perform(delete("/api/countries/{id}", id))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testDeleteInvalidId() throws Exception {
        String id = "invalidId";
        mockMvc.perform(delete("/api/countries/{id}", id))
                .andExpect(status().isBadRequest());

    }

    //Update Country Tests

    @Test
    public void testUpdateCountry() throws Exception {

        when(readCountriesUseCase.execute(1L))
                .thenReturn(FakeCountries.NIGERIA);
        CountryApiRequestPayload payload = new CountryApiRequestPayload(FakeCountries.NIGERIA);
        payload.setName("newName");


        mockMvc.perform(put("/api/countries/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isNoContent());

        verify(updateCountriesUseCase).execute(any(Country.class),eq(1L));

    }
}
