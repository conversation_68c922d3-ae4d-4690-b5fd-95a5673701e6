package pt.jumia.services.brad.api.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.api.csvs.CsvBuilder;
import pt.jumia.services.brad.api.payloads.request.currency.CurrencyApiRequestPayload;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.CreateCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.DeleteCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.UpdateCurrenciesUseCase;

import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
@WebMvcTest(CurrencyController.class)
public class CurrencyControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private CreateCurrenciesUseCase createCurrenciesUseCase;

    @MockBean
    private ReadCurrenciesUseCase readCurrenciesUseCase;

    @MockBean
    private UpdateCurrenciesUseCase updateCurrenciesUseCase;

    @MockBean
    private DeleteCurrenciesUseCase deleteCurrenciesUseCase;

    @MockBean
    private CsvBuilder csvBuilder;

    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);
    }


    //Create Account Tests

    @Test
    public void testSimpleMapping_success() throws Exception {

        when(createCurrenciesUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        CurrencyApiRequestPayload payload = new CurrencyApiRequestPayload(FakeCurrencies.NGN);
        mockMvc
                .perform(post("/api/currencies")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvalidContent() throws Exception {
        when(createCurrenciesUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        mockMvc
                .perform(post("/api/currencies")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("Invalid Payload"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.globalError",
                        containsString("JSON parse error: Unrecognized token 'Invalid'")))
                .andReturn();
    }

    @Test
    public void testConflict() throws Exception {

        when(createCurrenciesUseCase.execute(any()))
                .thenThrow(new ConstraintViolationException("Test error", Collections.emptySet()));

        CurrencyApiRequestPayload payload = new CurrencyApiRequestPayload(FakeCurrencies.NGN);
        mockMvc
                .perform(post("/api/currencies")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andReturn();
    }

    @Test
    public void createCurrency_failure_AlreadyExists() throws Exception {

        when(createCurrenciesUseCase.execute(any()))
                .thenThrow(AlreadyExistsException.createAlreadyExists(Currency.class, "1"));
        CurrencyApiRequestPayload payload = new CurrencyApiRequestPayload(FakeCurrencies.NGN);
        mockMvc
                .perform(post("/api/currencies")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isConflict());
    }

    //Fetch Currencies Tests

    @Test
    public void testFetch_success() throws Exception {


        List<Currency> currencies = FakeCurrencies.ALL_CURRENCIES;

        when(readCurrenciesUseCase.executeCurrencies(null))
                .thenReturn(currencies);

        mockMvc
                .perform(get("/api/currencies"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetch_emptyList_success() throws Exception {


        when(readCurrenciesUseCase.executeCurrencies(null))
                .thenReturn(Collections.emptyList());

        mockMvc
                .perform(get("/api/currencies"))
                .andExpect(status().isOk())
                .andReturn();
    }



    //Fetch Currency by ID Tests

    @Test
    public void testFetchById_validId_success() throws Exception {


        Currency mockCurrency = FakeCurrencies.NGN;


        when(readCurrenciesUseCase.execute(1L)).thenReturn(mockCurrency);

        mockMvc.perform(get("/api/currencies/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(mockCurrency.getId()))
                .andExpect(jsonPath("$.name").value(mockCurrency.getName()))
                .andExpect(jsonPath("$.code").value(mockCurrency.getCode()))
                .andExpect(jsonPath("$.symbol").value(mockCurrency.getSymbol()));

    }

    @Test
    public void testFetchById_invalidId_notFoundException() throws Exception {


        when(readCurrenciesUseCase.execute(2L)).thenThrow(new NotFoundException("Currency not found"));

        mockMvc.perform(get("/api/currencies/2"))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testFetchById_nonIntegerId_methodArgumentTypeMismatchException() throws Exception {


        mockMvc.perform(get("/api/currencies/abc"))
                .andExpect(status().isBadRequest())
                .andExpect(result -> assertTrue(result.getResolvedException() instanceof MethodArgumentTypeMismatchException));
    }

    //Delete Account Tests

    @Test
    public void testDeleteSuccess() throws Exception {

        int id = 1;
        doNothing().when(deleteCurrenciesUseCase).execute(id);
        mockMvc.perform(delete("/api/currencies/{id}", id))
                .andExpect(status().isNoContent());
    }

    @Test
    public void testDeleteNonExisting() throws Exception {

        int id = 1;
        doThrow(new NotFoundException("Currency not found")).when(deleteCurrenciesUseCase).execute(id);
        mockMvc.perform(delete("/api/currencies/{id}", id))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testDeleteInvalidId() throws Exception {
        String id = "invalidId";
        mockMvc.perform(delete("/api/currencies/{id}", id))
                .andExpect(status().isBadRequest());

    }

    //Update Currency Tests

    @Test
    public void testUpdateCurrency() throws Exception {

        when(readCurrenciesUseCase.execute(1L))
                .thenReturn(FakeCurrencies.NGN);
        CurrencyApiRequestPayload payload = new CurrencyApiRequestPayload(FakeCurrencies.NGN);
        payload.setName("newName");


        mockMvc.perform(put("/api/currencies/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isNoContent());

        verify(updateCurrenciesUseCase).execute(any(Currency.class));
    }
}
