package pt.jumia.services.brad.api.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.api.payloads.request.viewentity.ViewEntityApiRequestPayload;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.CreateViewEntityUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.DeleteViewEntityUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.UpdateViewEntityUseCase;

import java.util.List;

import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
@WebMvcTest(BaleViewEntityController.class)
public class BaleViewEntityControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private CreateViewEntityUseCase createBaleViewEntityUseCase;

    @MockBean
    private ReadViewEntityUseCase readBaleViewEntityUseCase;

    @MockBean
    private UpdateViewEntityUseCase updateBaleViewEntityUseCase;

    @MockBean
    private DeleteViewEntityUseCase deleteBaleViewEntityUseCase;

    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);
    }

    @Test
    public void testCreate() throws Exception {

        when(createBaleViewEntityUseCase.execute(any(), eq(ViewEntity.EntityType.BALE)))
                .thenAnswer(returnsFirstArg());

        ViewEntityApiRequestPayload payload = new ViewEntityApiRequestPayload(FakeViewEntity.getFakeViewEntity(1).get(0));

        mockMvc
                .perform(get("/api/bale-view-entities")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload.toString()))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetchById() throws Exception {
        ViewEntity baleViewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);

        when(readBaleViewEntityUseCase.execute(1L, ViewEntity.EntityType.BALE)).thenReturn(baleViewEntity);

        mockMvc
                .perform(get("/api/bale-view-entities/1"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetchAll() throws Exception {
        List<ViewEntity> baleViewEntityList = FakeViewEntity.getFakeViewEntity(5);

        when(readBaleViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(baleViewEntityList);

        mockMvc
                .perform(get("/api/bale-view-entities"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testUpdate() throws Exception {
        ViewEntity baleViewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);

        when(updateBaleViewEntityUseCase.execute(baleViewEntity, ViewEntity.EntityType.BALE)).thenReturn(baleViewEntity);

        ViewEntityApiRequestPayload payload = new ViewEntityApiRequestPayload(baleViewEntity);

        mockMvc
                .perform(MockMvcRequestBuilders.put("/api/bale-view-entities/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isNoContent())
                .andReturn();
    }

    @Test
    public void testDelete() throws Exception {
        doNothing().when(deleteBaleViewEntityUseCase).execute(1L, ViewEntity.EntityType.BALE);

        mockMvc
                .perform(delete("/api/bale-view-entities/1"))
                .andExpect(status().isNoContent())
                .andReturn();
    }


}
