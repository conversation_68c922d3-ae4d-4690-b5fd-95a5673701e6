package pt.jumia.services.brad.api.controllers.audit;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.domain.entities.AuditedEntity;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.audit.account.ReadAccountAuditUseCase;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
@WebMvcTest(AccountAuditController.class)
public class AccountAuditControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private ReadAccountAuditUseCase readAccountAuditUseCase;

    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);
    }


    @Test
    public void testFetchByID() throws Exception {
        List<AuditedEntity<Account>> auditedAccountList = new ArrayList<>();
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AuditedEntitySortFilters auditedEntitySortFilters = new AuditedEntitySortFilters(AuditedEntity.SortingFields.REV, OrderDirection.DESC);

        when(readAccountAuditUseCase.executeById(1,auditedEntitySortFilters, pageFilters))
                .thenReturn(auditedAccountList);

        when(readAccountAuditUseCase.executeCountById(1))
                .thenReturn(0L);


        mockMvc
                .perform(get("/api/accounts/audit/1"))
                .andExpect(status().isOk())
                .andReturn();
    }
}
