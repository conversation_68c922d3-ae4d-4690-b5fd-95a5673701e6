package pt.jumia.services.brad.api.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.api.payloads.request.viewentity.ViewEntityApiRequestPayload;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.CreateViewEntityUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.DeleteViewEntityUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.UpdateViewEntityUseCase;

import java.util.List;

import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
@WebMvcTest(FxRateViewEntityController.class)
public class FxRateViewEntityControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private CreateViewEntityUseCase createFxRateViewEntityUseCase;

    @MockBean
    private ReadViewEntityUseCase readFxRateViewEntityUseCase;

    @MockBean
    private UpdateViewEntityUseCase updateFxRateViewEntityUseCase;

    @MockBean
    private DeleteViewEntityUseCase deleteFxRateViewEntityUseCase;

    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);
    }

    @Test
    public void testCreate() throws Exception {

        when(createFxRateViewEntityUseCase.execute(any(), eq(ViewEntity.EntityType.FX_RATE)))
                .thenAnswer(returnsFirstArg());

        ViewEntityApiRequestPayload payload = new ViewEntityApiRequestPayload(FakeViewEntity.getFakeViewEntity(1).get(0));

        mockMvc
                .perform(get("/api/fx-rate-view-entities")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload.toString()))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetchById() throws Exception {
        ViewEntity fxRateViewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);

        when(readFxRateViewEntityUseCase.execute(1L, ViewEntity.EntityType.FX_RATE)).thenReturn(fxRateViewEntity);

        mockMvc
                .perform(get("/api/fx-rate-view-entities/1"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetchAll() throws Exception {
        List<ViewEntity> fxRateViewEntityList = FakeViewEntity.getFakeViewEntity(5);

        when(readFxRateViewEntityUseCase.execute(ViewEntity.EntityType.FX_RATE)).thenReturn(fxRateViewEntityList);

        mockMvc
                .perform(get("/api/fx-rate-view-entities"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testUpdate() throws Exception {
        ViewEntity fxRateViewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);

        when(updateFxRateViewEntityUseCase.execute(fxRateViewEntity, ViewEntity.EntityType.FX_RATE)).thenReturn(fxRateViewEntity);

        ViewEntityApiRequestPayload payload = new ViewEntityApiRequestPayload(fxRateViewEntity);

        mockMvc
                .perform(MockMvcRequestBuilders.put("/api/fx-rate-view-entities/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isNoContent())
                .andReturn();
    }

    @Test
    public void testDelete() throws Exception {
        doNothing().when(deleteFxRateViewEntityUseCase).execute(1L, ViewEntity.EntityType.FX_RATE);

        mockMvc
                .perform(delete("/api/fx-rate-view-entities/1"))
                .andExpect(status().isNoContent())
                .andReturn();
    }


}
