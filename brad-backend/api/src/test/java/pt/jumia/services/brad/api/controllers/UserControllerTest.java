package pt.jumia.services.brad.api.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.api.payloads.request.user.UserApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.user.UserFiltersApiRequestPayload;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.User;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeUsers;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.user.UserFilters;
import pt.jumia.services.brad.domain.entities.filter.user.UserSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;
import pt.jumia.services.brad.domain.usecases.user.CreateUserUseCase;
import pt.jumia.services.brad.domain.usecases.user.DeleteUserUseCase;
import pt.jumia.services.brad.domain.usecases.user.ReadUserUseCase;
import pt.jumia.services.brad.domain.usecases.user.UpdateUserUseCase;

import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Tests the controller at API level.
 * In here you should test the API validations and return content
 *
 * Note that you need to mock all the dependencies of your controller
 */
@ExtendWith(SpringExtension.class)
@WebMvcTest(UserController.class)
public class UserControllerTest {

    private final User USER = FakeUsers.getFakeUsers(1).get(0);
    private final Account BANK_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0);

    private final String URL = "/api/users";

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private ReadAccountsUseCase readAccountsUseCase;

    @MockBean
    private CreateUserUseCase createUserUseCase;

    @MockBean
    private ReadUserUseCase readUserUseCase;

    @MockBean
    private UpdateUserUseCase updateUserUseCase;

    @MockBean
    private DeleteUserUseCase deleteUserUseCase;

    @MockBean
    private ReadCountriesUseCase readCountriesUseCase;



    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);
    }


    //Create Signatory Tests

    @Test
    public void testSimpleMapping_success() throws Exception {

        when(createUserUseCase.execute(any(), any()))
                .thenReturn(USER.toBuilder().account(BANK_ACCOUNT).build());

        User user = FakeUsers.getFakeUsers(1).get(0);
        UserApiRequestPayload payload = new UserApiRequestPayload(user);
        payload.setAccountID(1L);
        when(readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID())))
                .thenReturn(FakeUsers.getFakeUsers(1).get(0).getAccount());
        mockMvc
                .perform(post(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvalidContent() throws Exception {
        when(createUserUseCase.execute(any(), any()))
                .thenReturn(USER.toBuilder().account(BANK_ACCOUNT).build());

        mockMvc
                .perform(post(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("Invalid Payload"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.globalError",
                        containsString("JSON parse error: Unrecognized token 'Invalid'")))
                .andReturn();
    }

    @Test
    public void testConflict() throws Exception {

        when(createUserUseCase.execute(any(), any()))
                .thenThrow(new ConstraintViolationException("Test error", Collections.emptySet()));
        User user = FakeUsers.getFakeUsers(1).get(0);
        UserApiRequestPayload payload = new UserApiRequestPayload(user);
        payload.setAccountID(1L);
        when(readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID())))
                .thenReturn(FakeUsers.getFakeUsers(1).get(0).getAccount());
        mockMvc
                .perform(post(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andReturn();
    }

    //Fetch Signatory Tests

    @Test
    public void testFetch_success() throws Exception {


        List<User> users = FakeUsers.getFakeUsers(20);
        Account account = FakeAccounts.FAKE_ACCOUNT;
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        UserSortFilters userSortFilters = new UserSortFilters(User.SortingFields.ID, OrderDirection.DESC);

        UserFilters userFilters = UserFilters.builder().accountID(1l).build();
        when(readAccountsUseCase.execute(Math.toIntExact(userFilters.getAccountID()))).thenReturn(account);
        when(readUserUseCase.execute(userFilters, userSortFilters, pageFilters))
                .thenReturn(users);



        mockMvc
                .perform(get(URL + "?accountID=1"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetch_emptyList_success() throws Exception {


        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);
        Account account = FakeAccounts.FAKE_ACCOUNT;

        UserSortFilters userSortFilters = new UserSortFilters(User.SortingFields.ID, OrderDirection.DESC);

        UserFilters userFilters = UserFilters.builder().accountID(1l).build();
        when(readAccountsUseCase.execute(Math.toIntExact(userFilters.getAccountID()))).thenReturn(account);
        when(readUserUseCase.execute(userFilters, userSortFilters, pageFilters))
                .thenReturn(Collections.emptyList());

        mockMvc
                .perform(get(URL + "?accountID=1"))
                .andExpect(status().isOk())
                .andReturn();
    }



    //Fetch Signatory by ID Tests

    @Test
    public void testFetchById_validId_success() throws Exception {


        User mockUser = FakeUsers.getFakeUsers(1).get(0);


        when(readUserUseCase.execute(1)).thenReturn(mockUser);

        mockMvc.perform(get(URL +"/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(mockUser.getId()))
                .andExpect(jsonPath("$.userName").value(mockUser.getName()))
                .andExpect(jsonPath("$.email").value(mockUser.getEmail()))
                .andExpect(jsonPath("$.status").value(mockUser.getStatus().name()))
                .andExpect(jsonPath("$.accountID").value(mockUser.getAccount().getId()));

    }

    @Test
    public void testFetchById_invalidId_notFoundException() throws Exception {


        when(readUserUseCase.execute(2)).thenThrow(new NotFoundException("User not found"));

        mockMvc.perform(get(URL + "/2"))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testFetchById_nonIntegerId_methodArgumentTypeMismatchException() throws Exception {


        mockMvc.perform(get(URL + "/abc"))
                .andExpect(status().isBadRequest())
                .andExpect(result -> assertTrue(result.getResolvedException() instanceof MethodArgumentTypeMismatchException));
    }




    //Delete Signatory Tests

    @Test
    public void testDeleteSuccess() throws Exception {

        int id = 1;
        when(readUserUseCase.execute(id)).thenReturn(FakeUsers.FAKE_USER);
        doNothing().when(deleteUserUseCase).execute(id);
        mockMvc.perform(delete(URL + "/{id}", id))
                .andExpect(status().isNoContent());
    }

    @Test
    public void testDeleteNonExisting() throws Exception {

        int id = 1;
        when(readUserUseCase.execute(id)).thenReturn(FakeUsers.FAKE_USER);
        doThrow(new NotFoundException("User not found")).when(deleteUserUseCase).execute(id);
        mockMvc.perform(delete(URL + "/{id}", id))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testDeleteInvalidId() throws Exception {
        String id = "invalidId";
        mockMvc.perform(delete(URL + "/{id}", id))
                .andExpect(status().isBadRequest());

    }

    //Update Signatory Tests

    @Test
    public void testUpdateUser() throws Exception {

        when(readUserUseCase.execute(1))
                .thenReturn(FakeUsers.getFakeUsers(1).get(0));
        User user = FakeUsers.getFakeUsers(1).get(0);
        UserApiRequestPayload payload = new UserApiRequestPayload(user);
        payload.setAccountID(1L);
        when(readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID())))
                .thenReturn(FakeUsers.getFakeUsers(1).get(0).getAccount());
        payload.setUserName("new name");


        mockMvc.perform(put(URL + "/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isOk());

        verify(updateUserUseCase).execute(any(User.class));

    }

    @Test
    public void testDownloadUsers() throws Exception {

        UserFiltersApiRequestPayload payload = new UserFiltersApiRequestPayload();
        List<User> users = FakeUsers.getFakeUsers(20);
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(users.size());

        UserSortFilters userSortFilters = new UserSortFilters(User.SortingFields.ID, OrderDirection.DESC);

        UserFilters userFilters = UserFilters.builder().build();

        when(readUserUseCase.executeCount(userFilters))
                .thenReturn(users.size());

        when(readUserUseCase.execute(userFilters, userSortFilters, pageFilters))
                .thenReturn(users);


        mockMvc.perform(get("/api/users/download")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isOk());
    }
}
