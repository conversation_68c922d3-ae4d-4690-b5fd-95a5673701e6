package pt.jumia.services.brad.api.controllers;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.fake.FakeTransaction;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.ReadAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.transactions.ReadTransactionUseCase;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
@WebMvcTest(TransactionController.class)
public class TransationControllerTest {


    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private ReadTransactionUseCase readTransactionUseCase;

    @MockBean
    private ReadAccountsUseCase readAccountsUseCase;

    @MockBean
    private ReadAccountStatementUseCase readAccountStatementUseCase;



    @Test
    public void testFetch_success() throws Exception {

        Account account = FakeAccounts.FAKE_ACCOUNT_A;
        AccountStatement accountStatement = FakeAccountStatements.getFakeAccountStatements(1, account).get(0);
        List<Transaction> transaction = FakeTransaction.getFakeCreditTransactions(10, accountStatement);
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        TransactionSortFilters transactionSortFilters = new TransactionSortFilters(Transaction.SortingFields.ID, OrderDirection.DESC);

        TransactionFilters transactionFilters = TransactionFilters.builder().partitionKey(String.valueOf(account.getId())).build();
        when(readAccountsUseCase.execute(Math.toIntExact(Long.parseLong(transactionFilters.getPartitionKey())))).thenReturn(account);
        when(readTransactionUseCase.execute(transactionFilters, transactionSortFilters, pageFilters))
                .thenReturn(transaction);



        mockMvc
                .perform(get("/api/transactions?partitionKey=2"))
                .andExpect(status().isOk())
                .andReturn();
    }



    @Test
    public void testFetch_emptyList_success() throws Exception {

        Account account = FakeAccounts.FAKE_ACCOUNT_A;
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        TransactionSortFilters transactionSortFilters = new TransactionSortFilters(Transaction.SortingFields.ID, OrderDirection.DESC);

        TransactionFilters transactionFilters = TransactionFilters.builder().partitionKey(String.valueOf(account.getId())).build();
        when(readAccountsUseCase.execute(Math.toIntExact(Long.parseLong(transactionFilters.getPartitionKey())))).thenReturn(account);
        when(readTransactionUseCase.execute(transactionFilters, transactionSortFilters, pageFilters))
                .thenReturn(Collections.emptyList());

        mockMvc
                .perform(get("/api/transactions?partitionKey=2"))
                .andExpect(status().isOk())
                .andReturn();
    }
}
