package pt.jumia.services.brad.api.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.api.payloads.request.document.DocumentApiRequestPayload;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeDocuments;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.document.DocumentFilters;
import pt.jumia.services.brad.domain.entities.filter.document.DocumentSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.documents.CreateDocumentsUseCase;
import pt.jumia.services.brad.domain.usecases.documents.DeleteDocumentsUseCase;
import pt.jumia.services.brad.domain.usecases.documents.ReadDocumentsUseCase;
import pt.jumia.services.brad.domain.usecases.documents.UpdateDocumentsUseCase;

import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Tests the controller at API level.
 * In here you should test the API validations and return content
 *
 * Note that you need to mock all the dependencies of your controller
 */
@ExtendWith(SpringExtension.class)
@WebMvcTest(DocumentController.class)
public class DocumentControllerTest {

    private final Document DOCUMENT = FakeDocuments.getFakeDocuments(1).get(0);
    private final Account BANK_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0);

    private final String URL = "/api/documents";

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private ReadAccountsUseCase readAccountsUseCase;

    @MockBean
    private CreateDocumentsUseCase createDocumentsUseCase;

    @MockBean
    private ReadDocumentsUseCase readDocumentsUseCase;

    @MockBean
    private UpdateDocumentsUseCase updateDocumentsUseCase;

    @MockBean
    private DeleteDocumentsUseCase deleteDocumentsUseCase;



    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);
    }


    //Create Document Tests

    @Test
    public void testSimpleMapping_success() throws Exception {

        when(createDocumentsUseCase.execute(any(),any()))
                .thenReturn(DOCUMENT.toBuilder().account(BANK_ACCOUNT).build());

        Document document = FakeDocuments.getFakeDocuments(1).get(0);
        DocumentApiRequestPayload payload = new DocumentApiRequestPayload(document);
        payload.setAccountID(1L);
        when(readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID())))
                .thenReturn(FakeDocuments.getFakeDocuments(1).get(0).getAccount());
        mockMvc
                .perform(post(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvalidContent() throws Exception {
        when(createDocumentsUseCase.execute(any(),any()))
                .thenReturn(DOCUMENT.toBuilder().account(BANK_ACCOUNT).build());

        mockMvc
                .perform(post(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("Invalid Payload"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.globalError",
                        containsString("JSON parse error: Unrecognized token 'Invalid'")))
                .andReturn();
    }

    @Test
    public void testConflict() throws Exception {

        when(createDocumentsUseCase.execute(any(),any()))
                .thenThrow(new ConstraintViolationException("Test error", Collections.emptySet()));
        Document document = FakeDocuments.getFakeDocuments(1).get(0);
        DocumentApiRequestPayload payload = new DocumentApiRequestPayload(document);
        payload.setAccountID(1L);
        when(readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID())))
                .thenReturn(FakeDocuments.getFakeDocuments(1).get(0).getAccount());
        mockMvc
                .perform(post(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andReturn();
    }

    @Test
    public void createDocument_failure_AlreadyExists() throws Exception {

        when(createDocumentsUseCase.execute(any(),any()))
                .thenThrow(AlreadyExistsException.createAlreadyExists(Document.class, "1"));

        Document document = FakeDocuments.getFakeDocuments(1).get(0);
        DocumentApiRequestPayload payload = new DocumentApiRequestPayload(document);
        payload.setAccountID(1L);
        when(readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID())))
                .thenReturn(FakeDocuments.getFakeDocuments(1).get(0).getAccount());
        mockMvc
                .perform(post(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isConflict());
    }

    //Fetch Document Tests

    @Test
    public void testFetch_success() throws Exception {


        List<Document> documents = FakeDocuments.getFakeDocuments(20);
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        DocumentSortFilters documentSortFilters = new DocumentSortFilters(Document.SortingFields.ID, OrderDirection.DESC);
        Account account = FakeAccounts.FAKE_ACCOUNT;
        DocumentFilters documentFilters = DocumentFilters.builder().accountId(1l).build();
        when(readAccountsUseCase.execute(Math.toIntExact(documentFilters.getAccountId()))).thenReturn(account);
        when(readDocumentsUseCase.execute(documentFilters, documentSortFilters, pageFilters))
                .thenReturn(documents);



        mockMvc
                .perform(get(URL + "?accountID=1"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetch_emptyList_success() throws Exception {


        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        DocumentSortFilters documentSortFilters = new DocumentSortFilters(Document.SortingFields.ID, OrderDirection.DESC);

        Account account = FakeAccounts.FAKE_ACCOUNT;
        DocumentFilters documentFilters = DocumentFilters.builder().accountId(1l).build();
        when(readAccountsUseCase.execute(Math.toIntExact(documentFilters.getAccountId()))).thenReturn(account);
        when(readDocumentsUseCase.execute(documentFilters, documentSortFilters, pageFilters))
                .thenReturn(Collections.emptyList());

        mockMvc
                .perform(get(URL + "?accountID=1"))
                .andExpect(status().isOk())
                .andReturn();
    }



    //Fetch Document by ID Tests

    @Test
    public void testFetchById_validId_success() throws Exception {


        Document mockDocument = FakeDocuments.getFakeDocuments(1).get(0);


        when(readDocumentsUseCase.execute(1)).thenReturn(mockDocument);

        mockMvc.perform(get(URL +"/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(mockDocument.getId()))
                .andExpect(jsonPath("$.documentType").value(mockDocument.getDocumentType().name()))
                .andExpect(jsonPath("$.name").value(mockDocument.getName()))
                .andExpect(jsonPath("$.description").value(mockDocument.getDescription()))
                .andExpect(jsonPath("$.url").value(mockDocument.getUrl()))
                .andExpect(jsonPath("$.accountID").value(mockDocument.getAccount().getId()));

    }

    @Test
    public void testFetchById_invalidId_notFoundException() throws Exception {


        when(readDocumentsUseCase.execute(2)).thenThrow(new NotFoundException("Document not found"));

        mockMvc.perform(get(URL + "/2"))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testFetchById_nonIntegerId_methodArgumentTypeMismatchException() throws Exception {


        mockMvc.perform(get(URL + "/abc"))
                .andExpect(status().isBadRequest())
                .andExpect(result -> assertTrue(result.getResolvedException() instanceof MethodArgumentTypeMismatchException));
    }




    //Delete Document Tests

    @Test
    public void testDeleteSuccess() throws Exception {

        int id = 1;
        when(readDocumentsUseCase.execute(Math.toIntExact(id))).thenReturn(FakeDocuments.FAKE_DOCUMENT);
        doNothing().when(deleteDocumentsUseCase).execute(id);
        mockMvc.perform(delete(URL + "/{id}", id))
                .andExpect(status().isNoContent());
    }

    @Test
    public void testDeleteNonExisting() throws Exception {

        int id = 1;
        when(readDocumentsUseCase.execute(Math.toIntExact(id))).thenReturn(FakeDocuments.FAKE_DOCUMENT);
        doThrow(new NotFoundException("Document not found")).when(deleteDocumentsUseCase).execute(id);
        mockMvc.perform(delete(URL + "/{id}", id))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testDeleteInvalidId() throws Exception {
        String id = "invalidId";
        mockMvc.perform(delete(URL + "/{id}", id))
                .andExpect(status().isBadRequest());

    }

    //Update Document Tests

    @Test
    public void testUpdateDocument() throws Exception {

        when(readDocumentsUseCase.execute(1))
                .thenReturn(FakeDocuments.getFakeDocuments(1).get(0));
        Document document = FakeDocuments.getFakeDocuments(1).get(0);
        DocumentApiRequestPayload payload = new DocumentApiRequestPayload(document);
        payload.setAccountID(1L);
        when(readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID())))
                .thenReturn(FakeDocuments.getFakeDocuments(1).get(0).getAccount());
        payload.setName("new name");


        mockMvc.perform(put(URL + "/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isOk());

        verify(updateDocumentsUseCase).execute(any(Document.class));

    }

    @Test
    public void testFetchContactTypes() throws Exception {
        when(readDocumentsUseCase.executeDocumentTypes()).thenReturn(Collections.emptyList());

        mockMvc.perform(get(URL + "/types"))
                .andExpect(status().isOk());

        verify(readDocumentsUseCase).executeDocumentTypes();

    }
}
