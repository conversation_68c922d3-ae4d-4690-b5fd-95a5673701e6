package pt.jumia.services.brad.api.controllers;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.fake.FakeExecutionLogs;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.executionlog.ExecutionLogFilters;
import pt.jumia.services.brad.domain.entities.filter.executionlog.ExecutionLogSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.ReadExecutionLogsUseCase;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@ExtendWith(SpringExtension.class)
@WebMvcTest(ExecutionLogController.class)
public class ExecutionLogControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private ReadExecutionLogsUseCase readExecutionLogsUseCase;

    private final String BASE_URL = "/api/execution-logs";
    
    @Test
    public void testFetch_success() throws Exception {

        List<ExecutionLog> executionLogs = FakeExecutionLogs.getFakeExecutionLogs(20);
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        ExecutionLogSortFilters executionLogSortFilters = new ExecutionLogSortFilters(ExecutionLog.SortingFields.ID, OrderDirection.DESC);

        ExecutionLogFilters executionLogFilters = ExecutionLogFilters.builder().build();
        when(readExecutionLogsUseCase.execute(executionLogFilters, executionLogSortFilters, pageFilters))
                .thenReturn(executionLogs);



        mockMvc
                .perform(get(BASE_URL))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetch_emptyList_success() throws Exception {


        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        ExecutionLogSortFilters executionLogSortFilters = new ExecutionLogSortFilters(ExecutionLog.SortingFields.ID, OrderDirection.DESC);

        ExecutionLogFilters executionLogFilters = ExecutionLogFilters.builder().build();
        when(readExecutionLogsUseCase.execute(executionLogFilters, executionLogSortFilters, pageFilters))
                .thenReturn(Collections.emptyList());

        mockMvc
                .perform(get(BASE_URL))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetchById_success() throws Exception {

        ExecutionLog executionLog = FakeExecutionLogs.getFakeExecutionLogs(1).get(0);
        when(readExecutionLogsUseCase.execute(1))
                .thenReturn(executionLog);

        mockMvc
                .perform(get(BASE_URL + "/1"))
                .andExpect(status().isOk())
                .andReturn();
    }

}
