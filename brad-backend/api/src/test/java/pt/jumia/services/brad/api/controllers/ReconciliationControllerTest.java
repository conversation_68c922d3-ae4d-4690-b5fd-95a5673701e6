package pt.jumia.services.brad.api.controllers;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.reconciliation.CreateReconciliationUseCase;
import pt.jumia.services.brad.domain.usecases.reconciliation.UpdateReconciliationUseCase;
import pt.jumia.services.brad.domain.usecases.reconciliation.ReadReconciliationUseCase;

import static org.mockito.Mockito.doNothing;

@ExtendWith(SpringExtension.class)
@WebMvcTest(ReconciliationController.class)
public class ReconciliationControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private ReadReconciliationUseCase readReconciliationUseCase;

    @MockBean
    private CreateReconciliationUseCase createReconciliationUseCase;

    @MockBean
    private UpdateReconciliationUseCase deleteReconciliationUseCase;

    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);
    }
//
//    @Test
//    public void testGetSelectedAmountDifference() throws Exception {
//
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//        String payload = ResourceLoader.getStringFromFile("reconciliation/valid_reconciliation.json");
//
//        mockMvc
//                .perform(post("/api/reconciliation/selectedAmountDifference")
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(payload))
//                .andExpect(status().isOk())
//                .andReturn();
//    }
// Deactivating reconciliation until finrec substitution is complete
//    @Test
//    public void testReconcile_success() throws Exception {
//
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//        when(createReconciliationUseCase.execute(any())).thenReturn(reconciliationList.get(0));
//
//        ReconciliationApiRequestPayload payload = new ReconciliationApiRequestPayload(reconciliationList.get(0));
//
//        mockMvc
//                .perform(post("/api/reconciliation")
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(new ObjectMapper().writeValueAsString(payload)))
//                .andExpect(status().isCreated())
//                .andReturn();
//    }

//    @Test
//    public void testReconcile_failure() throws Exception {
//
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//        ReconciliationApiRequestPayload payload = new ReconciliationApiRequestPayload(reconciliationList.get(0));
//
//        when(createReconciliationUseCase.execute(any())).thenThrow(new IllegalArgumentException(""));
//
//        mockMvc
//                .perform(post("/api/reconciliation")
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(new ObjectMapper().writeValueAsString(payload)))
//                .andExpect(status().isBadRequest())
//                .andReturn();
//    }

//    @Test
//    public void testReconcile_failure_Alreadyreconciled() throws Exception {
//
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//        ReconciliationApiRequestPayload payload = new ReconciliationApiRequestPayload(reconciliationList.get(0));
//
//        when(createReconciliationUseCase.execute(any())).thenThrow(new EntityAlreadyReconciledException(""));
//
//        mockMvc
//                .perform(post("/api/reconciliation")
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(new ObjectMapper().writeValueAsString(payload)))
//                .andExpect(status().isConflict())
//                .andReturn();
//    }
//
//    @Test
//    public void testReconcile_failure_DifferentAmounts() throws Exception {
//
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//
//        when(createReconciliationUseCase.execute(reconciliationList.get(0)))
//                .thenThrow(new IllegalArgumentException("Cannot reconcile different amounts"));
//
//
//        mockMvc
//                .perform(post("/api/reconciliation"))
//                .andExpect(status().isBadRequest())
//                .andReturn();
//    }
//
//    @Test
//    public void testGetReconciliationById() throws Exception {
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//        when(readReconciliationUseCase.execute(reconciliationList.get(0).getId())).thenReturn(reconciliationList.get(0));
//
//        mockMvc
//                .perform(get("/api/reconciliation/" + reconciliationList.get(0).getId())
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andReturn();
//    }
//
//    @Test
//    public void testGetReconciliationById_failure_NotFound() throws Exception {
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//        when(readReconciliationUseCase.execute(reconciliationList.get(0).getId())).thenThrow(new NotFoundException(""));
//
//        mockMvc
//                .perform(get("/api/reconciliation/" + reconciliationList.get(0).getId())
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isNotFound())
//                .andReturn();
//    }
//
//
//    @Test
//    public void testGetReconciliations() throws Exception {
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//        PageFilters pageFilters = new PageFilters();
//        pageFilters.setPage(1);
//        pageFilters.setSize(5);
//
//        ReconciliationSortFilters reconciliationSortFilters = new ReconciliationSortFilters(
//                Reconciliation.SortingFields.ID, OrderDirection.DESC
//        );
//
//        ReconciliationFilters reconciliationFilters = ReconciliationFilters.builder().build();
//
//        when(readReconciliationUseCase.execute(reconciliationFilters, reconciliationSortFilters, pageFilters))
//                .thenReturn(reconciliationList);
//        when(readReconciliationUseCase.executeCount(reconciliationFilters))
//                .thenReturn(5L);
//
//        mockMvc
//                .perform(get("/api/reconciliation"))
//                .andExpect(status().isOk())
//                .andReturn();
//
//    }
//
//    @Test
//    public void testApprove_success() throws Exception {
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//        doNothing().when(createReconciliationUseCase).approve(reconciliationList.get(0).getId(),"");
//
//
//        mockMvc
//                .perform(post("/api/reconciliation/approve/"+reconciliationList.get(0).getId())
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andReturn();
//    }
//
//    @Test
//    public void testApprove_failure_NotFound() throws Exception {
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//        doThrow(new NotFoundException("")).when(createReconciliationUseCase).approve(reconciliationList.get(0).getId(),
//                RequestContext.getUsername());
//
//        mockMvc
//                .perform(post("/api/reconciliation/approve/"+reconciliationList.get(0).getId())
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isNotFound())
//                .andReturn();
//    }
//
//    @Test
//    public void testApprove_failure_NotPending() throws Exception {
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//        doThrow(new IllegalArgumentException("Reconciliation is not pending")).when(createReconciliationUseCase)
//                .approve(reconciliationList.get(0).getId(), RequestContext.getUsername());
//
//        mockMvc
//                .perform(post("/api/reconciliation/approve/"+reconciliationList.get(0).getId())
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isBadRequest())
//                .andReturn();
//    }
//    @Test
//    public void testApprove_failure_NotAuthorized() throws Exception {
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//        doThrow(new ConflictOfInterestException(""))
//                .when(createReconciliationUseCase)
//                .approve(reconciliationList.get(0).getId(), RequestContext.getUsername());
//
//        mockMvc
//                .perform(post("/api/reconciliation/approve/"+reconciliationList.get(0).getId())
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isForbidden())
//                .andReturn();
//    }
//
//    @Test
//    public void testReject() throws Exception {
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//
//        doNothing().when(deleteReconciliationUseCase).reject(reconciliationList.get(0).getId());
//
//
//        mockMvc
//                .perform(delete("/api/reconciliation/reject/"+reconciliationList.get(0).getId()))
//                .andExpect(status().isOk())
//                .andReturn();
//    }
//
//    @Test
//    public void testReject_failure_NotFound() throws Exception {
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//
//        doThrow(new NotFoundException("")).when(deleteReconciliationUseCase).reject(reconciliationList.get(0).getId());
//
//
//        mockMvc
//                .perform(delete("/api/reconciliation/reject/"+reconciliationList.get(0).getId()))
//                .andExpect(status().isNotFound())
//                .andReturn();
//    }
//
//    @Test
//    public void testReject_failure_NotPending() throws Exception {
//        List<Reconciliation> reconciliationList = FakeReconciliation.ALL;
//
//
//        doThrow(new IllegalArgumentException("Reconciliation is not pending")).when(deleteReconciliationUseCase).reject(reconciliationList.get(0).getId());
//
//
//        mockMvc
//                .perform(delete("/api/reconciliation/reject/"+reconciliationList.get(0).getId()))
//                .andExpect(status().isBadRequest())
//                .andReturn();
//    }
//
//    @Test
//    public void testGetIsReconciledStatuses() throws Exception {
//        List<String> isReconciledStatus = Arrays.stream(IsReconciledStatus.values())
//                .map(IsReconciledStatus::name)
//                .collect(Collectors.toList());
//
//        when(readReconciliationUseCase.executeIsReconciledStatus()).thenReturn(isReconciledStatus);
//
//        mockMvc
//                .perform(get("/api/reconciliation/isReconciledStatuses"))
//                .andExpect(status().isOk())
//                .andReturn();
//
//
//    }
//    @Test
//    public void testGetReconciliationStatuses() throws Exception {
//        List<String> reconciliationStatuses = Arrays.stream(ReconciliationStatus.values())
//                .map(ReconciliationStatus::name)
//                .collect(Collectors.toList());
//
//        when(readReconciliationUseCase.executeReconciliationStatus()).thenReturn(reconciliationStatuses);
//
//        mockMvc
//                .perform(get("/api/reconciliation/reconciliationStatuses"))
//                .andExpect(status().isOk())
//                .andReturn();
//
//
//    }
}
