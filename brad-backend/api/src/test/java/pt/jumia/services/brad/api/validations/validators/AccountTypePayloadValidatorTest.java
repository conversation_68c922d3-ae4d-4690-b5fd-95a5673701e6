package pt.jumia.services.brad.api.validations.validators;

import jakarta.validation.ConstraintValidatorContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.api.payloads.request.account.AccountApiRequestPayload;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AccountTypePayloadValidatorTest {

    private AccountTypePayloadValidator validator;

    @Mock
    private ConstraintValidatorContext context;

    @Mock
    private ConstraintValidatorContext.ConstraintViolationBuilder violationBuilder;

    @Mock
    private ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext nodeBuilder;

    @BeforeEach
    void setUp() {
        validator = new AccountTypePayloadValidator();
    }

    @Test
    void whenValidBankAccount_thenReturnTrue() {
        var validAccount = FakeAccounts.getFakeAccounts(1, Account.Type.BANK_ACCOUNT).get(0);
        var payload = new AccountApiRequestPayload(validAccount);
        assertTrue(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidBankAccount_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.BANK_ACCOUNT).get(0)
                .toBuilder()
                .partner("partner1")
                .phoneNumber("phoneNumber1")
                .build();
        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }

    @Test
    void whenValidPSPAccount_thenReturnTrue() {
        var validAccount = FakeAccounts.getFakeAccounts(1, Account.Type.PSP).get(0);
        var payload = new AccountApiRequestPayload(validAccount);
        assertTrue(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidPSPAccount_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.PSP).get(0)
                .toBuilder()
                .partner(null)
                .iban("fakeIban1")
                .build();
        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }

    @Test
    void whenValidMobileMoneyAccount_thenReturnTrue() {
        var validAccount = FakeAccounts.getFakeAccounts(1, Account.Type.MOBILE_MONEY).get(0);
        var payload = new AccountApiRequestPayload(validAccount);
        assertTrue(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidMobileMoneyAccount_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.MOBILE_MONEY).get(0)
                .toBuilder()
                .partner(null)
                .phoneNumber(null)
                .iban("iban1")
                .build();
        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }

    @Test
    void whenValidWalletAccount_thenReturnTrue() {
        var validAccount = FakeAccounts.getFakeAccounts(1, Account.Type.WALLET).get(0);
        var payload = new AccountApiRequestPayload(validAccount);
        assertTrue(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidWalletAccount_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.WALLET).get(0)
                .toBuilder()
                .partner(null)
                .iban("iban1")
                .build();
        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }

    @Test
    void whenValidInvestmentAccountTermDeposits_thenReturnTrue() {
        var validAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(Account.SubType.TERM_DEPOSITS)
                .contractId("TD-123")
                .amountDeposited(BigDecimal.valueOf(10000))
                .maturityDate(LocalDate.now().plusMonths(12))
                .interest(BigDecimal.valueOf(3.5))
                .build();

        var payload = new AccountApiRequestPayload(validAccount);
        assertTrue(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidInvestmentAccountTermDeposits_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(Account.SubType.TERM_DEPOSITS)
                .contractId(null)
                .amountDeposited(BigDecimal.valueOf(10000))
                .maturityDate(LocalDate.now().plusMonths(12))
                .interest(BigDecimal.valueOf(3.5))
                .build();

        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }

    @Test
    void whenValidInvestmentAccountTLF_thenReturnTrue() {
        var validAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(Account.SubType.TLF)
                .contractId("TLF-456")
                .amountDeposited(BigDecimal.valueOf(5000))
                .maturityDate(LocalDate.now().plusMonths(6))
                .interest(BigDecimal.valueOf(2.5))
                .build();

        var payload = new AccountApiRequestPayload(validAccount);
        assertTrue(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidInvestmentAccountTLF_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(Account.SubType.TLF)
                .contractId("TLF-456")
                .amountDeposited(null)
                .maturityDate(LocalDate.now().plusMonths(6))
                .interest(null)
                .build();

        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }

    @Test
    void whenValidInvestmentAccountBankGuarantees_thenReturnTrue() {
        var validAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(Account.SubType.BANK_GUARANTEES)
                .contractId("BG-789")
                .amountDeposited(BigDecimal.valueOf(25000))
                .maturityDate(LocalDate.now().plusYears(1))
                .interest(BigDecimal.valueOf(1.8))
                .build();

        var payload = new AccountApiRequestPayload(validAccount);
        assertTrue(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidInvestmentAccountBankGuarantees_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(Account.SubType.BANK_GUARANTEES)
                .contractId("BG-789")
                .amountDeposited(BigDecimal.valueOf(25000))
                .maturityDate(null)
                .interest(BigDecimal.valueOf(1.8))
                .build();

        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }

    @Test
    void whenValidInvestmentAccountBonds_thenReturnTrue() {
        var validAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(Account.SubType.BONDS)
                .isin("US123456AB12")
                .amountDeposited(BigDecimal.valueOf(50000))
                .maturityDate(LocalDate.now().plusYears(5))
                .nominalAmount(BigDecimal.valueOf(50000))
                .couponPaymentPeriodicity(Account.CouponPaymentPeriodicity.SEMI_ANNUAL)
                .couponRate(BigDecimal.valueOf(4.2))
                .build();

        var payload = new AccountApiRequestPayload(validAccount);
        assertTrue(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidInvestmentAccountBonds_isinMissing_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(Account.SubType.BONDS)
                .isin(null)
                .amountDeposited(BigDecimal.valueOf(50000))
                .maturityDate(LocalDate.now().plusYears(5))
                .nominalAmount(BigDecimal.valueOf(50000))
                .couponPaymentPeriodicity(Account.CouponPaymentPeriodicity.SEMI_ANNUAL)
                .couponRate(BigDecimal.valueOf(4.2))
                .build();

        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidInvestmentAccountBonds_couponRateMissing_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(Account.SubType.BONDS)
                .isin("US123456AB12")
                .amountDeposited(BigDecimal.valueOf(50000))
                .maturityDate(LocalDate.now().plusYears(5))
                .nominalAmount(BigDecimal.valueOf(50000))
                .couponPaymentPeriodicity(Account.CouponPaymentPeriodicity.SEMI_ANNUAL)
                .couponRate(null)
                .build();

        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidInvestmentAccount_missingSubType_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(null)
                .build();

        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidInvestmentAccountWithInvalidFields_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(Account.SubType.BONDS)
                .isin("US123456AB12")
                .amountDeposited(BigDecimal.valueOf(50000))
                .maturityDate(LocalDate.now().plusYears(5))
                .nominalAmount(BigDecimal.valueOf(50000))
                .couponPaymentPeriodicity(Account.CouponPaymentPeriodicity.SEMI_ANNUAL)
                .couponRate(BigDecimal.valueOf(4.2))
                .phoneNumber("**********")
                .iban("**********************")
                .build();

        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidInvestmentTermDeposit_withBondSpecificField_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(Account.SubType.TERM_DEPOSITS)
                .contractId("TD-123")
                .amountDeposited(BigDecimal.valueOf(10000))
                .maturityDate(LocalDate.now().plusMonths(12))
                .interest(BigDecimal.valueOf(3.5))
                .isin("US123456AB12")
                .build();

        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }

    @Test
    void whenInvalidInvestmentBond_withTermDepositSpecificField_thenReturnFalse() {
        when(context.buildConstraintViolationWithTemplate(any()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addPropertyNode(any()))
                .thenReturn(nodeBuilder);

        var invalidAccount = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0)
                .toBuilder()
                .subType(Account.SubType.BONDS)
                .isin("US123456AB12")
                .amountDeposited(BigDecimal.valueOf(50000))
                .maturityDate(LocalDate.now().plusYears(5))
                .nominalAmount(BigDecimal.valueOf(50000))
                .couponPaymentPeriodicity(Account.CouponPaymentPeriodicity.SEMI_ANNUAL)
                .couponRate(BigDecimal.valueOf(4.2))
                .contractId("TD-123")
                .build();

        var payload = new AccountApiRequestPayload(invalidAccount);
        assertFalse(validator.isValid(payload, context));
    }
}
