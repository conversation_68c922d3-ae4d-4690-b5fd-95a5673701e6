package pt.jumia.services.brad.api.controllers;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.brad.domain.entities.ApiLog;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeApiLogs;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.apilog.ApiLogFilters;
import pt.jumia.services.brad.domain.entities.filter.apilog.ApiLogSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.apilog.ReadApiLogUseCase;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import pt.jumia.services.brad.domain.usecases.apilog.UpdateApiLogUseCase;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@ExtendWith(SpringExtension.class)
@WebMvcTest(ApiLogController.class)
public class ApiLogControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private ReadApiLogUseCase readApiLogUseCase;

    @MockBean
    private UpdateApiLogUseCase updateApiLogUseCase;

    @MockBean
    private ReadAccountsUseCase readAccountsUseCase;

    private final String BASE_URL = "/api/api-logs";
    
    @Test
    public void testFetch_success() throws Exception {

        List<ApiLog> apiLogs = FakeApiLogs.getFakeApiLogs(20);
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        ApiLogSortFilters apiLogSortFilters = new ApiLogSortFilters(ApiLog.SortingFields.ID, OrderDirection.DESC);

        ApiLogFilters apiLogFilters = ApiLogFilters.builder().build();
        when(readApiLogUseCase.execute(apiLogFilters, apiLogSortFilters, pageFilters))
                .thenReturn(apiLogs);



        mockMvc
                .perform(get(BASE_URL))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetch_emptyList_success() throws Exception {


        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        ApiLogSortFilters apiLogSortFilters = new ApiLogSortFilters(ApiLog.SortingFields.ID, OrderDirection.DESC);

        ApiLogFilters apiLogFilters = ApiLogFilters.builder().build();
        when(readApiLogUseCase.execute(apiLogFilters, apiLogSortFilters, pageFilters))
                .thenReturn(Collections.emptyList());

        mockMvc
                .perform(get(BASE_URL))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetchById_success() throws Exception {

        ApiLog apiLogWithPartitionKey = ApiLog.builder().partitionKey("partitionKey").build();
        Account account = FakeAccounts.FAKE_ACCOUNT;

        when(readApiLogUseCase.execute(1)).thenReturn(apiLogWithPartitionKey);
        when(readAccountsUseCase.execute(apiLogWithPartitionKey.getPartitionKey())).thenReturn(account);


        mockMvc
                .perform(get(BASE_URL+"/1"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetchLogTypes_success() throws Exception {

        List<String> logTypes = new ArrayList<>();

        when(readApiLogUseCase.executeLogTypes()).thenReturn(logTypes);

        mockMvc
                .perform(get(BASE_URL+"/api-log-types"))
                .andExpect(status().isOk())
                .andReturn();
    }
    @Test
    public void testFetchStatus_success() throws Exception {

        List<String> statusTypes = new ArrayList<>();

        when(readApiLogUseCase.executeLogStatus()).thenReturn(statusTypes);

        mockMvc
                .perform(get(BASE_URL+"/api-log-status"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testAcknowledgeFailure_success() throws Exception {

        when(updateApiLogUseCase.acknowledgeFailure(1)).thenReturn(FakeApiLogs.FAKE_API_LOG);

        mockMvc
            .perform(put(BASE_URL+"/1/acknowledge-failure"))
            .andExpect(status().isOk())
            .andReturn();
    }
}
