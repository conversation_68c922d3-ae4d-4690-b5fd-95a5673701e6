package pt.jumia.services.brad.api.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.api.payloads.request.contact.ContactApiRequestPayload;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Contact;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.api.payloads.request.contact.ContactFiltersApiRequestPayload;
import pt.jumia.services.brad.domain.entities.fake.FakeContacts;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.contact.ContactFilters;
import pt.jumia.services.brad.domain.entities.filter.contact.ContactSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.contact.CreateContactUseCase;
import pt.jumia.services.brad.domain.usecases.contact.DeleteContactUseCase;
import pt.jumia.services.brad.domain.usecases.contact.ReadContactUseCase;
import pt.jumia.services.brad.domain.usecases.contact.UpdateContactUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;

import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Tests the controller at API level.
 * In here you should test the API validations and return content
 *
 * Note that you need to mock all the dependencies of your controller
 */
@ExtendWith(SpringExtension.class)
@WebMvcTest(ContactController.class)
public class ContactControllerTest {

    private final Contact CONTACT = FakeContacts.getFakeContacts(1).get(0);
    private final Account BANK_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0);

    private final String URL = "/api/contacts";

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private CreateContactUseCase createContactUseCase;

    @MockBean
    private ReadContactUseCase readContactUseCase;

    @MockBean
    private UpdateContactUseCase updateContactUseCase;

    @MockBean
    private DeleteContactUseCase deleteContactUseCase;

    @MockBean
    private ReadAccountsUseCase readAccountsUseCase;

    @MockBean
    private ReadCountriesUseCase readCountriesUseCase;



    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);
    }


    //Create Contact Tests

    @Test
    public void testSimpleMapping_success() throws Exception {

        when(readAccountsUseCase.execute(1)).thenReturn(BANK_ACCOUNT);
        when(createContactUseCase.execute(any(), any()))
            .thenReturn(CONTACT.toBuilder().account(BANK_ACCOUNT).build());

        Contact contact = FakeContacts.getFakeContacts(1).get(0);
        ContactApiRequestPayload payload = new ContactApiRequestPayload(contact);
        payload.setAccountID(1L);

        mockMvc
            .perform(post(URL)
                .contentType(MediaType.APPLICATION_JSON)
                    .content(new ObjectMapper().writeValueAsString(payload)))
            .andExpect(status().isCreated())
            .andReturn();
    }

    @Test
    public void testInvalidContent() throws Exception {
        when(createContactUseCase.execute(any(), any()))
                .thenReturn(CONTACT.toBuilder().account(BANK_ACCOUNT).build());

        mockMvc
            .perform(post(URL)
                .contentType(MediaType.APPLICATION_JSON)
                .content("Invalid Payload"))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.globalError",
                containsString("JSON parse error: Unrecognized token 'Invalid'")))
            .andReturn();
    }

    @Test
    public void testConflict() throws Exception {

        when(readAccountsUseCase.execute(Math.toIntExact(FakeAccounts.FAKE_ACCOUNT.getId()))).thenReturn(FakeAccounts.FAKE_ACCOUNT);
        when(createContactUseCase.execute(any(), any()))
            .thenThrow(new ConstraintViolationException("Test error", Collections.emptySet()));
        Contact contact = FakeContacts.getFakeContacts(1).get(0);
        ContactApiRequestPayload payload = new ContactApiRequestPayload(contact);
        payload.setAccountID(1L);

        mockMvc
            .perform(post(URL)
                .contentType(MediaType.APPLICATION_JSON)
                    .content(new ObjectMapper().writeValueAsString(payload)))
            .andExpect(status().isBadRequest())
            .andReturn();
    }


    //Fetch Contact Tests

    @Test
    public void testFetch_success() throws Exception {


        List<Contact> contacts = FakeContacts.getFakeContacts(20);
        Account account = FakeAccounts.FAKE_ACCOUNT;
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        ContactSortFilters contactSortFilters = new ContactSortFilters(Contact.SortingFields.ID, OrderDirection.DESC);

        ContactFilters contactFilters = ContactFilters.builder().accountID(1L).build();
        when(readAccountsUseCase.execute(Math.toIntExact(contactFilters.getAccountID()))).thenReturn(account);
        when(readContactUseCase.execute(contactFilters, contactSortFilters, pageFilters))
                .thenReturn(contacts);



        mockMvc
                .perform(get(URL + "?accountID=1"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetch_emptyList_success() throws Exception {


        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);
        Account account = FakeAccounts.FAKE_ACCOUNT;

        ContactSortFilters contactSortFilters = new ContactSortFilters(Contact.SortingFields.ID, OrderDirection.DESC);

        ContactFilters contactFilters = ContactFilters.builder().accountID(1L).build();
        when(readAccountsUseCase.execute(Math.toIntExact(contactFilters.getAccountID()))).thenReturn(account);
        when(readContactUseCase.execute(contactFilters, contactSortFilters, pageFilters))
                .thenReturn(Collections.emptyList());

        mockMvc
                .perform(get(URL + "?accountID=1"))
                .andExpect(status().isOk())
                .andReturn();
    }



    //Fetch Contact by ID Tests

    @Test
    public void testFetchById_validId_success() throws Exception {


        Contact mockContact = FakeContacts.getFakeContacts(1).get(0);


        when(readContactUseCase.execute(1)).thenReturn(mockContact);

        mockMvc.perform(get(URL +"/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(mockContact.getId()))
                .andExpect(jsonPath("$.contactType").value(mockContact.getContactType()))
                .andExpect(jsonPath("$.name").value(mockContact.getName()))
                .andExpect(jsonPath("$.email").value(mockContact.getEmail()))
                .andExpect(jsonPath("$.mobilePhoneNumber").value(mockContact.getMobilePhoneNumber()))
                .andExpect(jsonPath("$.accountID").value(mockContact.getAccount().getId()));

    }

    @Test
    public void testFetchById_invalidId_notFoundException() throws Exception {


        when(readContactUseCase.execute(2)).thenThrow(new NotFoundException("Contact not found"));

        mockMvc.perform(get(URL + "/2"))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testFetchById_nonIntegerId_methodArgumentTypeMismatchException() throws Exception {


        mockMvc.perform(get(URL + "/abc"))
                .andExpect(status().isBadRequest())
                .andExpect(result -> assertTrue(result.getResolvedException() instanceof MethodArgumentTypeMismatchException));
    }




    //Delete Contact Tests

    @Test
    public void testDeleteSuccess() throws Exception {

        int id = 1;
        when(readContactUseCase.execute(id)).thenReturn(FakeContacts.FAKE_CONTACT);
        doNothing().when(deleteContactUseCase).execute(id);
        mockMvc.perform(delete(URL + "/{id}", id))
                .andExpect(status().isNoContent());
    }

    @Test
    public void testDeleteNonExisting() throws Exception {

        int id = 1;
        when(readContactUseCase.execute(id)).thenReturn(FakeContacts.FAKE_CONTACT);
        doThrow(new NotFoundException("Contact not found")).when(deleteContactUseCase).execute(id);
        mockMvc.perform(delete(URL + "/{id}", id))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testDeleteInvalidId() throws Exception {
        String id = "invalidId";
        mockMvc.perform(delete(URL + "/{id}", id))
                .andExpect(status().isBadRequest());

    }

    //Update Contact Tests

    @Test
    public void testUpdateContact() throws Exception {

        when(readContactUseCase.execute(1))
                .thenReturn(FakeContacts.getFakeContacts(1).get(0));

        Contact contact = FakeContacts.getFakeContacts(1).get(0);
        Account account = FakeAccounts.FAKE_ACCOUNT;
        Contact contactToGetAccountNumber = contact.toBuilder().account(account).build();
        when(readAccountsUseCase.execute(Math.toIntExact(contactToGetAccountNumber.getAccount().getId()))).thenReturn(account);
        ContactApiRequestPayload payload = new ContactApiRequestPayload(contact);
        payload.setAccountID(1L);
        payload.setName("new name");


        mockMvc.perform(put(URL + "/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                        .andExpect(status().isOk());

        verify(updateContactUseCase).execute(any(Contact.class));

    }

    @Test
    public void testFetchContactTypes() throws Exception {
        when(readContactUseCase.executeContactTypes()).thenReturn(Collections.emptyList());

        mockMvc.perform(get(URL + "/types"))
                .andExpect(status().isOk());

        verify(readContactUseCase).executeContactTypes();

    }

    @Test
    public void testDownloadContacts() throws Exception {

        ContactFiltersApiRequestPayload payload = new ContactFiltersApiRequestPayload();
        List<Contact> contacts = FakeContacts.getFakeContacts(20);
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(contacts.size());

        ContactSortFilters contactSortFilters = new ContactSortFilters(Contact.SortingFields.ID, OrderDirection.DESC);

        ContactFilters contactFilters = ContactFilters.builder().build();

        when(readContactUseCase.executeCount(contactFilters))
                .thenReturn(contacts.size());

        when(readContactUseCase.execute(contactFilters, contactSortFilters, pageFilters))
                .thenReturn(contacts);


        mockMvc.perform(get("/api/contacts/download")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isOk());
    }
}
