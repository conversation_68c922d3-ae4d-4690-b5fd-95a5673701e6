package pt.jumia.services.brad.api.controllers;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatementFiles;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.ReprocessFilesUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.DownloadAccountStatementFileUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.ReadAccountStatementFileUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.ScanS3BucketForStatementFileUseCase;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static pt.jumia.services.brad.api.controllers.BaseControllerTest.REQUEST_USER;

@ExtendWith(SpringExtension.class)
@WebMvcTest(AccountStatementFileController.class)
public class AccountStatementFileControllerTest {

    private final String URL = "/api/account-statement-files";
    private final String TEMP_URL = "temp.url";
    private final Long ID = 1L;
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private ReadAccountStatementFileUseCase readAccountStatementFileUseCase;

    @MockBean
    private ScanS3BucketForStatementFileUseCase scanS3BucketForStatementFileUseCase;

    @MockBean
    private ReprocessFilesUseCase reprocessFilesUseCase;

    @MockBean
    private DownloadAccountStatementFileUseCase downloadAccountStatementFileUseCase;

    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccessStatementFiles(requestUser);
        doNothing().when(validateUserAccessUseCase).checkCanScanSftpFolder(requestUser);
    }

    //Fetch AccountStatementFile Tests

    @Test
    public void testFetch_success() throws Exception {

        List<AccountStatementFile> accountStatementFiles = FakeAccountStatementFiles.getFakeAccountStatementFiles(5);
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountStatementFileSortFilters accountStatementFileSortFilters = new AccountStatementFileSortFilters(AccountStatementFile.SortingFields.ID,
                OrderDirection.DESC);

        AccountStatementFileFilters accountStatementFileFilters = AccountStatementFileFilters.builder().build();
        when(readAccountStatementFileUseCase.execute(accountStatementFileFilters, accountStatementFileSortFilters, pageFilters))
                .thenReturn(accountStatementFiles);

        mockMvc
                .perform(get(URL))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetch_emptyList_success() throws Exception {

        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountStatementFileSortFilters accountStatementFileSortFilters = new AccountStatementFileSortFilters(AccountStatementFile.SortingFields.ID,
                OrderDirection.DESC);

        AccountStatementFileFilters accountStatementFileFilters = AccountStatementFileFilters.builder().build();
        when(readAccountStatementFileUseCase.execute(accountStatementFileFilters, accountStatementFileSortFilters, pageFilters))
                .thenReturn(Collections.emptyList());

        mockMvc
                .perform(get(URL))
                .andExpect(status().isOk())
                .andReturn();
    }

    //Fetch AccountStatementFile by ID Tests

    @Test
    public void testFetchById_validId_success() throws Exception {

        AccountStatementFile mockAccountStatementFile = FakeAccountStatementFiles.getFakeAccountStatementFiles(1).get(0);

        when(readAccountStatementFileUseCase.execute(1)).thenReturn(mockAccountStatementFile);

        mockMvc.perform(get(URL + "/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(mockAccountStatementFile.getId()))
                .andExpect(jsonPath("$.name").value(mockAccountStatementFile.getName()))
                .andExpect(jsonPath("$.processingStatus").value(mockAccountStatementFile.getProcessingStatus().name()))
                .andExpect(jsonPath("$.statusDescription").value(mockAccountStatementFile.getStatusDescription()))
                .andExpect(jsonPath("$.executionLog").value(mockAccountStatementFile.getExecutionLog().getId()));

    }

    @Test
    public void testFetchById_invalidId_notFoundException() throws Exception {

        when(readAccountStatementFileUseCase.execute(2)).thenThrow(new NotFoundException("AccountStatementFile not found"));

        mockMvc.perform(get(URL + "/2"))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testFetchById_nonIntegerId_methodArgumentTypeMismatchException() throws Exception {

        mockMvc.perform(get(URL + "/abc"))
                .andExpect(status().isBadRequest())
                .andExpect(result -> assertInstanceOf(MethodArgumentTypeMismatchException.class, result.getResolvedException()));
    }

    // Scan SFTP folder tests
    @Test
    public void testScanSftpFolder_whenSchedulerExceptionIsThrown_throwsCorrectException() throws Exception {

        doThrow(SchedulerException.class).when(scanS3BucketForStatementFileUseCase).start();

        mockMvc.perform(patch(URL + "/scan"))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void testScanSftpFolder_whenOk_returns20Ok() throws Exception {

        doNothing().when(scanS3BucketForStatementFileUseCase).start();

        mockMvc.perform(patch(URL + "/scan"))
                .andExpect(status().isOk());
    }

    @Test
    public void testDownload() throws Exception {

        when(downloadAccountStatementFileUseCase.executeGetTempDownloadUrl(ID)).thenReturn(TEMP_URL);

        doNothing().when(validateUserAccessUseCase).checkCanDownloadStatementFiles(REQUEST_USER);

        mockMvc
                .perform(get(URL + "/" + ID + "/download"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testReprocessStatementFiles_whenOk_returns20Ok() throws Exception {

        doNothing().when(reprocessFilesUseCase).execute(any());

        mockMvc.perform(patch(URL + "/reprocess"))
            .andExpect(status().isOk());
    }

}
