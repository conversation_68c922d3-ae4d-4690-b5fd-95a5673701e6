package pt.jumia.services.brad.api.controllers;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.fake.FakeFxRates;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.fxrate.FxRateFilters;
import pt.jumia.services.brad.domain.entities.filter.fxrate.FxRateSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.fxrates.ReadFxRateUseCase;
import pt.jumia.services.brad.domain.usecases.fxrates.brad.ReadBradFxRateUseCase;

import java.time.LocalDate;
import java.util.List;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
@WebMvcTest(FxRateController.class)
public class FxRateControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private ReadBradFxRateUseCase readBradFxRateUseCase;

    @MockBean
    private ReadFxRateUseCase readFxRateUseCase;

    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);
    }


    @Test
    public void testFetch() throws Exception {
        List<FxRate> fxRates = FakeFxRates.FAKE_FX_RATE_LIST;
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        FxRateSortFilters fxRateSortFilters = FxRateSortFilters.builder()
                .field(FxRate.SortingFields.TIMESTAMP_LAST_UPDATE)
                .direction(OrderDirection.ASC)
                .build();

        FxRateFilters fxRateFilters = FxRateFilters.builder().build();

        when(readBradFxRateUseCase.execute(fxRateFilters, fxRateSortFilters, pageFilters)).thenReturn(fxRates);
        when(readBradFxRateUseCase.executeCount(fxRateFilters)).thenReturn(3L);

        mockMvc
                .perform(get("/api/fx-rates"))
                .andExpect(status().isOk())
                .andReturn();

    }

    @Test
    public void testFetchAllFxRateInRateDateOfCurrency() throws Exception {
        List<FxRate> fxRates = FakeFxRates.FAKE_FX_RATE_LIST;
        LocalDate rateDate = fxRates.get(0).getRateDate();
        String currency = fxRates.get(0).getBaseCurrency().getCode();
        when(readBradFxRateUseCase.executeAllFxRateInRateDateOfCurrency(
                rateDate,
                currency
        )).thenReturn(fxRates);

        mockMvc
                .perform(get("/api/fx-rates/" + rateDate + "/" + currency))
                .andExpect(status().isOk())
                .andReturn();

    }

    @Test
    public void testFetchById() throws Exception {
        FxRate fxRate = FakeFxRates.FAKE_FX_RATE_EUR_USD;
        Integer id = 1;
        when(readBradFxRateUseCase.execute(id)).thenReturn(fxRate);

        mockMvc
                .perform(get("/api/fx-rates/" + id))
                .andExpect(status().isOk())
                .andReturn();

    }
}
