package pt.jumia.services.brad.api.controllers;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import pt.jumia.services.brad.api.payloads.response.JwtResponsePayload;
import pt.jumia.services.brad.api.payloads.response.error.ErrorResponsePayload;
import pt.jumia.services.brad.domain.usecases.ReadUserAccessUseCase;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@WebMvcTest(AuthController.class)
public class AuthControllerTest extends BaseControllerTest {

    private static final String BASE_PUBLIC_ENDPOINT = "/auth";
    private static final String BASE_ENDPOINT = "/api/auth";

    @MockBean
    private ReadUserAccessUseCase readUserAccessUseCase;

    @Test
    public void swapTempToken() throws Exception {
        String tempToken = "temp-token-to-swap";
        String finalToken = "final-token";
        when(readUserAccessUseCase.findRealToken(tempToken)).thenReturn(finalToken);

        JwtResponsePayload swappedJwtResponsePayload = request(
                post(BASE_PUBLIC_ENDPOINT + "/swap-token"),
                tempToken,
                HttpStatus.OK,
                JwtResponsePayload.class);

        verify(readUserAccessUseCase).findRealToken(tempToken);
        assertThat(swappedJwtResponsePayload.getJwt()).isEqualTo(finalToken);
    }

    @Test
    public void swapTempTokenNull() throws Exception {
        ErrorResponsePayload errorResponsePayload = request(
                post(BASE_PUBLIC_ENDPOINT + "/swap-token"),
                HttpStatus.BAD_REQUEST,
                ErrorResponsePayload.class);

        verifyNoInteractions(readUserAccessUseCase);
        assertThat(errorResponsePayload.getGlobalError()).contains("Required request body is missing");
    }

    @Test
    public void swapTempTokenEmpty() throws Exception {
        ErrorResponsePayload errorResponsePayload = request(
                post(BASE_PUBLIC_ENDPOINT + "/swap-token"),
                "",
                HttpStatus.BAD_REQUEST,
                ErrorResponsePayload.class);

        verifyNoInteractions(readUserAccessUseCase);
        assertThat(errorResponsePayload.getGlobalError()).contains("Required request body is missing");
    }

    @Test
    public void fetchAllPermissions() throws Exception {
        Map<String, Map<String, List<String>>> userPermissions = Map.of(
                "APPLICATION", Map.of(
                        "BRAD", List.of("can_access", "download_csv", "admin_delete_bank_account", "manage_bank_account", "close_bank_account")
                )
        );
        when(readUserAccessUseCase.execute(REQUEST_USER)).thenReturn(userPermissions);

        Map<String, Map<String, List<String>>> permissionsResponsePayload = request(
                get(BASE_ENDPOINT + "/user/permissions"),
                HttpStatus.OK,
                Map.class);


        verify(readUserAccessUseCase).execute(REQUEST_USER);
        assertThat(permissionsResponsePayload).isEqualTo(userPermissions);
    }

    @Test
    public void logout() throws Exception {
        request(get(BASE_ENDPOINT + "/logout"), HttpStatus.OK);

        verify(readUserAccessUseCase).logout(REQUEST_USER);
    }
}
