package pt.jumia.services.brad.api.payloads.response.views;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.enumerations.Direction;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
public class BaleRawResponsePayload {

    @Getter(value = AccessLevel.NONE)
    @Setter(value = AccessLevel.NONE)
    private static final String dateFormat = "yyyy-MM-dd";

    Long id;
    String idCompany;
    String accountNumber;
    Integer entryNo;
    String documentNo;
    String documentType;
    @JsonFormat(pattern = dateFormat)
    LocalDate postingDate;
    String accountPostingGroup;
    String description;
    String sourceCode;
    String reasonCode;
    String busLine;
    String department;
    Direction direction;
    BigDecimal amount;
    BigDecimal remainingAmount;
    String transactionCurrencyCode;
    BigDecimal amountLcy;
    String balanceAccountNumber;
    String balanceAccountType;
    Boolean isOpen;
    Boolean isReversed;
    String postedBy;
    String externalDocumentNo;
    String baleTimestamp;
    String accountTimestamp;

    public BaleRawResponsePayload(Bale bale) {
        this.id = bale.getId();
        this.idCompany = bale.getIdCompany();
        this.accountNumber = bale.getAccount().getAccountNumber();
        this.entryNo = bale.getEntryNo();
        this.documentNo = bale.getDocumentNo();
        this.documentType = bale.getDocumentType();
        this.postingDate = bale.getPostingDate();
        this.accountPostingGroup = bale.getAccountPostingGroup();
        this.description = bale.getDescription();
        this.sourceCode = bale.getSourceCode();
        this.reasonCode = bale.getReasonCode();
        this.busLine = bale.getBusLine();
        this.department = bale.getDepartment();
        this.direction = bale.getDirection();
        this.amount = bale.getAmount();
        this.remainingAmount = bale.getRemainingAmount();
        this.transactionCurrencyCode = bale.getTransactionCurrency().getCode();
        this.amountLcy = bale.getAmountLcy();
        this.balanceAccountNumber = bale.getBalanceAccountNumber();
        this.balanceAccountType = bale.getBalanceAccountType();
        this.isOpen = bale.getIsOpen();
        this.isReversed = bale.getIsReversed();
        this.postedBy = bale.getPostedBy();
        this.externalDocumentNo = bale.getExternalDocumentNo();
        this.baleTimestamp = bale.getBaleTimestamp();
        this.accountTimestamp = bale.getAccountTimestamp();
    }



}
