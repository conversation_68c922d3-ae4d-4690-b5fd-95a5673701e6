package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.ExportLog;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class ExportLogResponsePayload {

    private Long id;
    private ExportLog.Type type;
    private String fileUrl;
    private String fileName;
    private Integer rowCount;
    private String status;
    private Long executionTime;
    private List<String> countries;
    private String filters;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    private String createdBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;

    public ExportLogResponsePayload(ExportLog exportLog) {
        this.id = exportLog.getId();
        this.type = exportLog.getType();
        this.fileUrl = exportLog.getFileUrl();
        this.fileName = exportLog.getFileName();
        this.rowCount = exportLog.getRowCount();
        this.status = exportLog.getStatus().name();
        this.filters = exportLog.getFilters();
        this.countries = exportLog.getCountries();
        this.executionTime = exportLog.getExecutionTime();
        this.createdAt = exportLog.getCreatedAt();
        this.createdBy = exportLog.getCreatedBy();
        this.updatedAt = exportLog.getUpdatedAt();
    }

    public ExportLog toEntity() {
        return ExportLog.builder()
                .id(id)
                .type(type)
                .fileUrl(fileUrl)
                .fileName(fileName)
                .countries(countries)
                .rowCount(rowCount)
                .status(ExportLog.Status.valueOf(status))
                .executionTime(executionTime)
                .filters(filters)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .build();
    }
}
