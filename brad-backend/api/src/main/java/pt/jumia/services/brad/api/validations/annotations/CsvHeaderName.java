package pt.jumia.services.brad.api.validations.annotations;

import jakarta.validation.Constraint;
import pt.jumia.services.brad.api.validations.validators.CsvHeaderValidator;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;

@Documented
@Target({FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CsvHeaderValidator.class)
public @interface CsvHeaderName {
    String value();

}
