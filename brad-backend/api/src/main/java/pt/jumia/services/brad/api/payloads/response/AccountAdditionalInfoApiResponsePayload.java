package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.account.Account;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
public class AccountAdditionalInfoApiResponsePayload {
    private static final String dateFormat = "yyyy-MM-dd";
    private Long id;
    private BigDecimal balance;
    private BigDecimal balanceUSD;
    private CurrencyApiResponsePayload localCurrency;
    private BigDecimal balanceLocalCurrency;
    @JsonFormat(pattern = dateFormat)
    private LocalDate lastStatementDate;
    @JsonFormat(pattern = dateFormat)
    private LocalDate lastTransactionDate;
    private Boolean hasError;
    private String statementSource;

    public AccountAdditionalInfoApiResponsePayload(Account account) {
        this.id = account.getId();
        this.balance = account.getBalance();
        this.balanceUSD = account.getBalanceUSD();
        this.localCurrency = account.getLocalCurrency() != null ?
                new CurrencyApiResponsePayload(account.getLocalCurrency()) : null;
        this.balanceLocalCurrency = account.getBalanceLocalCurrency();
        this.lastStatementDate = account.getLastStatementDate();
        this.lastTransactionDate = account.getLastTransactionDate();
        this.hasError = account.getHasError();
        this.statementSource = account.getStatementSource().name();
    }

}
