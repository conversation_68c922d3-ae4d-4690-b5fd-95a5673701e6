package pt.jumia.services.brad.api.payloads.request.exportlog;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.ExportLog;
import pt.jumia.services.brad.domain.entities.filter.exportlog.ExportLogSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@Data
@NoArgsConstructor
public class ExportLogSortRequestPayload {

    @ValidEnumValue(required = true, enumClass = ExportLog.SortingFields.class)
    private String orderField = ExportLog.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public ExportLogSortRequestPayload(ExportLogSortFilters exportLogSortFilters) {
        this.orderField = exportLogSortFilters.getField().name();
        this.orderDirection = exportLogSortFilters.getDirection().name();
    }

    public ExportLogSortFilters toEntity() {
        return ExportLogSortFilters.builder()
                .field(ExportLog.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }
}
