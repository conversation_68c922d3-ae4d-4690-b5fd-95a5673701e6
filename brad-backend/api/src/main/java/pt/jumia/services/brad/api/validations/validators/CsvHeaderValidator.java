package pt.jumia.services.brad.api.validations.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import pt.jumia.services.brad.api.validations.annotations.CsvHeaderName;

public class CsvHeaderValidator implements ConstraintValidator<CsvHeaderName, String> {

    private CsvHeaderName params;

    @Override
    public void initialize(CsvHeaderName csvHeaderName) {
        this.params = csvHeaderName;
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        return value != null && value.equalsIgnoreCase(params.value());
    }


}
