package pt.jumia.services.brad.api.payloads.request.user;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.account.User;
import pt.jumia.services.brad.domain.entities.account.User.Status;

@Data
@NoArgsConstructor
public class UserApiRequestPayload {
    private Long id;

    private String userName;

    private String email;

    private Long accountID;

    private String createdBy;

    private String updatedBy;

    private String createdAt;

    private String updatedAt;

    private String hrRole;

    private String permissionType;

    private String mobilePhoneNumber;

    @ValidEnumValue(required = true, enumClass = Status.class)
    private String status;


    public UserApiRequestPayload(User user) {
        this.id = user.getId();
        this.userName = user.getName();
        this.email = user.getEmail();
        this.accountID = user.getAccount().getId();
        this.hrRole = user.getHrRole();
        this.permissionType = user.getPermissionType();
        this.mobilePhoneNumber = user.getMobilePhoneNumber();
        this.status = user.getStatus().name();
    }

    public User toEntity() {
        return User.builder()
                .id(id)
                .name(userName)
                .email(email)
                .hrRole(hrRole)
                .permissionType(permissionType)
                .mobilePhoneNumber(mobilePhoneNumber)
                .status(Status.valueOf(status))
                .build();
    }
}
