package pt.jumia.services.brad.api.csvs.exports;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import org.springframework.http.ResponseEntity;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.api.validations.annotations.CsvHeaderName;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static pt.jumia.services.brad.api.csvs.CsvBuilder.buildCsv;

@Builder(toBuilder = true)
public class StatementExportCSV {

    @CsvHeaderName("ID")
    public Long id;
    @CsvHeaderName("Currency")
    public String currencyCode;
    @CsvHeaderName("Account Number")
    public String accountNumber;
    @CsvHeaderName("Statement Number")
    public String statementId;
    @CsvHeaderName("Previous Statement Number")
    public String previousStatementID;
    @CsvHeaderName("Initial Date")
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    public LocalDate initialDate;
    @CsvHeaderName("Final Date")
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    public LocalDate finalDate;
    @CsvHeaderName("Initial Direction")
    public Direction initialDirection;
    @CsvHeaderName("Final Direction")
    public Direction finalDirection;
    @CsvHeaderName("Initial Amount")
    public BigDecimal initialAmount;
    @CsvHeaderName("Final Amount")
    public BigDecimal finalAmount;
    @CsvHeaderName("Initial Amount (USD)")
    public BigDecimal initialAmountUsd;
    @CsvHeaderName("Final Amount (USD)")
    public BigDecimal finalAmountUsd;
    @CsvHeaderName("Initial Amount (LCY)")
    public BigDecimal initialAmountLocalCurrency;
    @CsvHeaderName("Final Amount (LCY)")
    public BigDecimal finalAmountLocalCurrency;
    @CsvHeaderName("Rate")
    public BigDecimal rate;
    @CsvHeaderName("Status")
    public AccountStatementStatus status;
    @CsvHeaderName("Status Description")
    public String statusDescription;
    @CsvHeaderName("Created By")
    public String createdBy;
    @CsvHeaderName("Created At")
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    public LocalDateTime createdAt;
    @CsvHeaderName("Updated By")
    public String updatedBy;
    @CsvHeaderName("Updated At")
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    public LocalDateTime updatedAt;

    public static ResponseEntity<byte[]> buildStatementCSV(List<AccountStatement> statements) {
        List<StatementExportCSV> statementCSVs = statements.stream()
                .map((statement) -> StatementExportCSV.builder()
                        .id(statement.getId())
                        .currencyCode(statement.getCurrency().getCode())
                        .accountNumber(statement.getAccount().getAccountNumber())
                        .statementId(statement.getStatementId())
                        .previousStatementID(statement.getPreviousStatement() != null ? statement.getPreviousStatement().getStatementId() : null)
                        .initialDate(statement.getInitialDate())
                        .finalDate(statement.getFinalDate())
                        .initialDirection(statement.getInitialDirection())
                        .finalDirection(statement.getFinalDirection())
                        .initialAmount(statement.getInitialAmount())
                        .finalAmount(statement.getFinalAmount())
                        .initialAmountUsd(statement.getAmountInUsd(statement.getInitialAmount()))
                        .finalAmountUsd(statement.getAmountInUsd(statement.getFinalAmount()))
                        .initialAmountLocalCurrency(statement.getAmountInLocalCurrency(statement.getInitialAmount()))
                        .finalAmountLocalCurrency(statement.getAmountInLocalCurrency(statement.getFinalAmount()))
                        .status(statement.getStatus())
                        .statusDescription(statement.getStatusDescription().getName())
                        .createdBy(statement.getCreatedBy())
                        .createdAt(statement.getCreatedAt())
                        .updatedBy(statement.getUpdatedBy())
                        .updatedAt(statement.getUpdatedAt())
                        .build())
                .toList();
        return buildCsv(statementCSVs);
    }
}
