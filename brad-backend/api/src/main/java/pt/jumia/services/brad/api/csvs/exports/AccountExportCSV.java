package pt.jumia.services.brad.api.csvs.exports;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import org.springframework.http.ResponseEntity;
import pt.jumia.services.brad.api.utils.ExportDateUtil;
import pt.jumia.services.brad.api.validations.annotations.CsvHeaderName;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.dtos.AccountNetChangeResultDto;
import pt.jumia.services.brad.domain.entities.filter.account.AccountNetChangeFilters;
import pt.jumia.services.brad.domain.enumerations.StatementSource;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static pt.jumia.services.brad.api.csvs.CsvBuilder.buildCsv;
import static pt.jumia.services.brad.api.csvs.CsvBuilder.getFieldNames;

@Builder(toBuilder = true)
public class AccountExportCSV {

    @CsvHeaderName("ID")
    public Long id;
    @CsvHeaderName("Company ID")
    public String companyID;
    @CsvHeaderName("Country")
    public String countryName;
    @CsvHeaderName("NAV Reference")
    public String navReference;
    @CsvHeaderName("Balance")
    public BigDecimal balance;
    @CsvHeaderName("Balance (USD)")
    public BigDecimal balanceUSD;
    @CsvHeaderName("Net Change")
    public BigDecimal netChange;
    @CsvHeaderName("Beneficiary Name")
    public String beneficiaryName;
    @CsvHeaderName("Beneficiary Address")
    public String beneficiaryAddress;
    @CsvHeaderName("IBAN")
    public String iban;
    @CsvHeaderName("Account Number")
    public String accountNumber;
    @CsvHeaderName("Account Name")
    public String accountName;
    @CsvHeaderName("SWIFT Code")
    public String swiftCode;
    @CsvHeaderName("Account Routing Code")
    public String bankRoutingCode;
    @CsvHeaderName("Sort Code")
    public String sortCode;
    @CsvHeaderName("Branch Code")
    public String branchCode;
    @CsvHeaderName("RIB")
    public String rib;
    @CsvHeaderName("Type")
    public Account.Type type;
    @CsvHeaderName("Status")
    public Account.Status status;
    @CsvHeaderName("Statement Source")
    public StatementSource statementSource;
    @CsvHeaderName("Statement Periodicity")
    public Account.StatementPeriodicity statementPeriodicity;
    @CsvHeaderName("Currency")
    public String currencyCode;
    @CsvHeaderName("ISIN")
    public String isin;
    @CsvHeaderName("Contract ID")
    public String contractId;
    @CsvHeaderName("Amount Deposited")
    public BigDecimal amountDeposited;
    @CsvHeaderName("Maturity Date")
    @JsonFormat(pattern = ExportDateUtil.SIMPLE_DATE_FORMAT)
    public LocalDate maturityDate;
    @CsvHeaderName("Nominal Amount")
    public BigDecimal nominalAmount;
    @CsvHeaderName("Coupon Payment Periodicity")
    public String couponPaymentPeriodicity;
    @CsvHeaderName("Coupon Rate")
    public BigDecimal couponRate;
    @CsvHeaderName("Interest")
    public BigDecimal interest;
    @CsvHeaderName("Last Statement Date")
    @JsonFormat(pattern = ExportDateUtil.SIMPLE_DATE_FORMAT)
    public LocalDate lastStatementDate;
    @CsvHeaderName("Last Processed Statement Date")
    @JsonFormat(pattern = ExportDateUtil.SIMPLE_DATE_FORMAT)
    public LocalDate lastProcessedStatementDate;
    @CsvHeaderName("Created By")
    public String createdBy;
    @CsvHeaderName("Created At")
    public String createdAt;
    @CsvHeaderName("Updated By")
    public String updatedBy;
    @CsvHeaderName("Updated At")
    public String updatedAt;

    public static ResponseEntity<byte[]> buildAccountCSV(List<Account> accounts, AccountNetChangeFilters netChangeFilters,
        List<AccountNetChangeResultDto> netChangeResultDtos) {

        List<AccountExportCSV> accountCSVs = accounts.stream()
            .map((account) -> {
                final AccountNetChangeResultDto accountNetChangeResultDto = netChangeResultDtos.stream()
                    .filter(v -> v.partitionKey().equalsIgnoreCase(
                        String.valueOf(account.getId()))).findFirst().orElse(null);

                return AccountExportCSV.builder()
                    .id(account.getId())
                    .companyID(account.getCompanyID())
                    .countryName(account.getCountry().getName())
                    .navReference(account.getNavReference())
                    .balance(account.getBalance())
                    .balanceUSD(account.getBalanceUSD())
                    .netChange(Objects.isNull(accountNetChangeResultDto) ? BigDecimal.ZERO : accountNetChangeResultDto.netChange())
                    .beneficiaryName(account.getBeneficiaryName())
                    .beneficiaryAddress(account.getBeneficiaryAddress())
                    .iban(account.getIban())
                    .accountNumber(account.getAccountNumber())
                    .accountName(account.getAccountName())
                    .swiftCode(account.getSwiftCode())
                    .bankRoutingCode(account.getBankRoutingCode())
                    .sortCode(account.getSortCode())
                    .branchCode(account.getBranchCode())
                    .rib(account.getRib())
                    .type(account.getType())
                    .status(account.getStatus())
                    .statementSource(account.getStatementSource() == null
                        ? StatementSource.MANUAL_UPLOAD
                        : account.getStatementSource())
                    .statementPeriodicity(account.getStatementPeriodicity())
                    .currencyCode(account.getCurrency().getCode())
                    .isin(account.getIsin())
                    .contractId(account.getContractId())
                    .amountDeposited(account.getAmountDeposited())
                    .maturityDate(account.getMaturityDate())
                    .nominalAmount(account.getNominalAmount())
                    .couponPaymentPeriodicity(account.getCouponPaymentPeriodicity() != null ? 
                        account.getCouponPaymentPeriodicity().name() : null)
                    .couponRate(account.getCouponRate())
                    .interest(account.getInterest())
                    .lastStatementDate(account.getLastStatementDate())
                    .lastProcessedStatementDate(account.getLastProcessedStatementDate())
                    .createdAt(account.getCreatedAt() == null ? null :
                        (account.getCreatedAt().format(ExportDateUtil.USUAL_DATE_TIME_FORMAT)))
                    .createdBy(account.getCreatedBy())
                    .updatedAt(account.getUpdatedAt() == null ? null :
                        (account.getUpdatedAt().format(ExportDateUtil.USUAL_DATE_TIME_FORMAT)))
                    .updatedBy(account.getUpdatedBy())
                    .build();
            })
            .toList();

        if (accountCSVs.isEmpty()) {
            throw new RuntimeException("Error downloading CSV. No entity found for the provided filters");
        }
        final List<String> headers = overrideHeaders(netChangeFilters);

        return buildCsv(accountCSVs, headers);
    }

    private static List<String> overrideHeaders(final AccountNetChangeFilters netChangeFilters) {

        final String neChange = "Net Change";

        final List<String> fieldNames = getFieldNames(AccountExportCSV.class);
        return fieldNames.stream().map(fieldName -> {
            if (Objects.nonNull(netChangeFilters.endDate()) && fieldName.equalsIgnoreCase(neChange)) {
                if (Boolean.TRUE.equals(netChangeFilters.fromBeginning())) {
                    return neChange + " ( Since beginning - " + netChangeFilters.endDate() + ")";
                } else {
                    return neChange + " (" + netChangeFilters.startDate() + " - " + netChangeFilters.endDate() + ")";
                }
            } else {
                return fieldName;
            }
        }).collect(Collectors.toList());
    }

}
