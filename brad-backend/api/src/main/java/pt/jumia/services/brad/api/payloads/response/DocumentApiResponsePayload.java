package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.enumerations.DocumentType;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class DocumentApiResponsePayload {

    private Long id;
    private String documentType;
    private String name;
    private String description;
    private String url;
    private Long accountID;
    private String file;
    private String createdBy;
    private String updatedBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;



    public DocumentApiResponsePayload(Document document) {
        this.id = document.getId();
        this.documentType = document.getDocumentType().toString();
        this.name = document.getName();
        this.description = document.getDescription();
        this.url = document.getUrl();
        this.accountID = document.getAccount().getId();
        this.file = document.getFile();
        this.createdAt = document.getCreatedAt();
        this.updatedAt = document.getUpdatedAt();
        this.createdBy = document.getCreatedBy();
        this.updatedBy = document.getUpdatedBy();

    }

    public Document toEntity() {
        return Document
                .builder()
                .id(id)
                .documentType(DocumentType.fromString(documentType))
                .name(name)
                .description(description)
                .url(url)
                .file(file)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }

    public Document toEntityWithoutAccount() {
        return Document
                .builder()
                .id(id)
                .documentType(DocumentType.fromString(documentType))
                .name(name)
                .description(description)
                .url(url)
                .file(file)
                .account(null)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}
