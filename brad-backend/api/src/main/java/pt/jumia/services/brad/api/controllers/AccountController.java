package pt.jumia.services.brad.api.controllers;

import com.neovisionaries.i18n.CountryCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.ArrayList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.api.csvs.exports.AccountExportCSV;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.account.AccountApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.account.AccountFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.account.AccountNetChangeRequestPayload;
import pt.jumia.services.brad.api.payloads.request.account.AccountSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.*;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.ExportLog;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Account.Status;
import pt.jumia.services.brad.domain.entities.dtos.AccountNetChangeResultDto;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountNetChangeFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountSortFilters;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.accounts.*;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;
import pt.jumia.services.brad.domain.utils.JsonUtils;

import java.text.ParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * Controller responsible for handling CRUD operations for accounts.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/accounts")
public class AccountController {

    private final CreateAccountsUseCase createAccountsUseCase;
    private final ReadAccountsUseCase readAccountsUseCase;
    private final UpdateAccountsUseCase updateAccountsUseCase;
    private final DeleteAccountsUseCase deleteAccountsUseCase;
    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final ReadCountriesUseCase readCountriesUseCase;
    private final ExportAccountsUseCase exportAccountsUseCase;

    @GetMapping
    public PageApiResponsePayload<AccountApiResponsePayload> fetch(HttpServletRequest httpServletRequest,
                       @Valid AccountFiltersApiRequestPayload accountFiltersApiRequestPayload,
                       @Valid AccountSortFiltersApiRequestPayload accountSortFiltersApiRequestPayload,
                       @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload)
            throws UserForbiddenException, EntityErrorsException, ParseException, NotFoundException {

        log.info("Fetching all accounts for user with identifier {}", RequestContext.getUsername());

        AccountFilters accountFilters = accountFiltersApiRequestPayload.toEntity();
        accountFilters = getAccountFiltersWithUpdatedCountryCodesBasedOnUserAccess(accountFilters);

        AccountSortFilters accountSortFilters = accountSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();

        List<AccountApiResponsePayload> accounts =
                readAccountsUseCase.execute(accountFilters, accountSortFilters, pageFilters)
                        .stream()
                        .map(AccountApiResponsePayload::new)
                        .collect(Collectors.toList());

        long total = readAccountsUseCase.executeCount(accountFilters);

        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                accounts,
                total
            );
    }

    @GetMapping(value = "/{id}")
    public AccountApiResponsePayload fetchById(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        Account account = readAccountsUseCase.execute(id);
        String country = getCountry(account);

        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching account {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new AccountApiResponsePayload(account);

    }


    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public AccountApiResponsePayload create(@RequestBody @Valid AccountApiRequestPayload payload)
            throws UserForbiddenException, AlreadyExistsException, NotFoundException, EntityErrorsException, DatabaseErrorsException {

        validateUserAccessUseCase.checkCanManageAccounts(RequestContext.getUser(), CountryCode.valueOf(payload.getCountryCode()));

        log.info("Creating new account: {} for user with identifier {}",
                JsonUtils.toJsonWithDateFormated(payload), RequestContext.getUsername());

        Account account = createAccountsUseCase.execute(payload.toEntity());

        return new AccountApiResponsePayload(account);
    }

    @GetMapping(value = "/download")
    @ResponseStatus(HttpStatus.OK)
    public ExportLogResponsePayload download(@Valid AccountFiltersApiRequestPayload accountFiltersApiRequestPayload,
                                             @Valid AccountNetChangeRequestPayload accountNetChangeRequestPayload,
                                             @Valid AccountSortFiltersApiRequestPayload accountSortFiltersApiRequestPayload)
        throws UserForbiddenException, ParseException, EntityErrorsException {

        String username = RequestContext.getUsername();
        log.info("Downloading accounts for user with identifier {}", username);

        ExportLog exportLog = exportAccountsUseCase.execute(accountFiltersApiRequestPayload.toEntity(),
                accountNetChangeRequestPayload.toEntity(),
                accountSortFiltersApiRequestPayload.toEntity(),
                username);

        return new ExportLogResponsePayload(exportLog);
    }

    @PutMapping(value = "/{id}")
    public void update(@PathVariable(value = "id") Long id, @RequestBody @Valid AccountApiRequestPayload payload)
            throws NotFoundException, UserForbiddenException, EntityErrorsException, DatabaseErrorsException, AlreadyExistsException {

        validateUserAccessUseCase.checkCanManageAccounts(RequestContext.getUser(), CountryCode.valueOf(payload.getCountryCode()));

        log.info("Updating account with id {} : {} for user with identifier {}",
                id, JsonUtils.toJson(payload), RequestContext.getUsername());

        payload.setId(id);
        updateAccountsUseCase.execute(payload.toEntity());

    }

    @PatchMapping(value = "/sync-last-processed-statement-date")
    public void updateLastProcessedStatementDate(@RequestBody List<Integer> ids)
        throws NotFoundException, UserForbiddenException, DatabaseErrorsException {

        for (Integer accountId : ids) {
            Account account = readAccountsUseCase.execute(accountId);
            validateUserAccessUseCase.checkCanManageAccounts(RequestContext.getUser(), CountryCode.valueOf(account.getCountry().getCode()));

            log.info("Updating account last processed statement date with id {} for user with identifier {}",
                accountId, RequestContext.getUsername());

            updateAccountsUseCase.executeLastProcessedStatementDate(List.of(Long.valueOf(accountId)));
        }

    }

    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        Account account = readAccountsUseCase.execute(id);
        String country = getCountry(account);

        validateUserAccessUseCase.checkCanDeleteAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Deleting account with id {} for user with identifier {}",
                id, RequestContext.getUsername());

        deleteAccountsUseCase.execute(id);

    }

    @GetMapping(value = "/additional-info/{id}")
    public AccountAdditionalInfoApiResponsePayload fetchAdditionalInfoById(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException, EntityErrorsException {

        Account account = readAccountsUseCase.executeAdditionalInfo(id);
        String country = getCountry(account);

        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching additional account info {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new AccountAdditionalInfoApiResponsePayload(account);

    }

    @GetMapping(value = "/statuses")
    public List<AccountStatusResponsePayload> fetchAccountStatuses() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());

        log.info("Fetching additional account statuses for user with identifier {}", RequestContext.getUsername());

        return Arrays.stream(Account.Status.values())
                .map(status -> new AccountStatusResponsePayload(status.name(), status.getValue()))
                .toList();
    }

    @GetMapping(value = "/nav-references")
    public List<Account> fetchAccountNavReferences(HttpServletRequest httpServletRequest,
                                                @Valid AccountFiltersApiRequestPayload accountFiltersApiRequestPayload)
            throws UserForbiddenException, ParseException, NotFoundException {

        String country = getCountry(readAccountsUseCase.execute(accountFiltersApiRequestPayload.getAccountNumber()));
        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching all accounts for user with identifier {}", RequestContext.getUsername());

        AccountFilters accountFilters = accountFiltersApiRequestPayload.toEntity();

        return readAccountsUseCase.executeAccountNavReferences(accountFilters);
    }

    @GetMapping(value = "/account-last-updated-statement")
    public AccountApiResponsePayload fetchLastUpdatedStatementAccount(HttpServletRequest httpServletRequest)
            throws UserForbiddenException, NotFoundException {

        Optional<Account> account = readAccountsUseCase.executeByUserLastUpdatedStatement(RequestContext.getUsername());
        String country = getCountry(account.orElseThrow());

        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching last updated account by user with identifier {}", RequestContext.getUsername());

        return account.map(AccountApiResponsePayload::new)
                .orElseThrow(() -> NotFoundException.createNotFound(Account.class, RequestContext.getUsername()));
    }

    @GetMapping(value = "/troubleshooting")
    public PageApiResponsePayload<AccountApiTroubleShootingResponsePayload> getTroubleshootingAccounts(
        HttpServletRequest httpServletRequest,
        @Valid AccountFiltersApiRequestPayload accountFiltersApiRequestPayload,
        @Valid AccountSortFiltersApiRequestPayload accountSortFiltersApiRequestPayload,
        @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload)
        throws UserForbiddenException, ParseException, EntityErrorsException {

        log.info("{} Fetching Accounts that require troubleshooting ", RequestContext.getUsername());

        AccountFilters accountFilters = accountFiltersApiRequestPayload.toEntity();
        accountFilters = getAccountFiltersWithUpdatedCountryCodesBasedOnUserAccessTroubleshooting(accountFilters);

        AccountSortFilters accountSortFilters = accountSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();

        AccountFilters filters = accountFilters.toBuilder().status(List.of(Status.OPEN)).build();

        List<AccountApiTroubleShootingResponsePayload> accounts =
            readAccountsUseCase.executeAllTroubleshootingAccounts(filters, accountSortFilters, pageFilters)
                .stream()
                .map(AccountApiTroubleShootingResponsePayload::new)
                .collect(Collectors.toList());

        long total = readAccountsUseCase.countTroubleShooting(filters);

        return PageApiResponsePayload.buildPageResponsePayload(
            httpServletRequest,
            pageFilters,
            accounts,
            total
        );
    }

    @GetMapping(value = "/net-change")
    public List<AccountNetChangeResponsePayload> computeNetChange(
            @Valid AccountNetChangeRequestPayload accountNetChangeRequestPayload) throws ParseException, EntityErrorsException {

        AccountNetChangeFilters netChangeFilters = accountNetChangeRequestPayload.toEntity();

        List<AccountNetChangeResultDto> netChangeResultDtos = readAccountsUseCase
                .executeAccountNetChange(accountNetChangeRequestPayload.getPartitionKeys(), netChangeFilters);

        return netChangeResultDtos.stream()
                .map(dto -> new AccountNetChangeResponsePayload(dto.partitionKey(), dto.netChange()))
                .toList();
    }

    private static String getCountry(Account account) {
        String country = account.getCountry().getCode();
        return country;
    }

    private AccountFilters getAccountFiltersWithUpdatedCountryCodesBasedOnUserAccess
            (AccountFilters accountFilters)
            throws UserForbiddenException {
        List<CountryCode> countriesWithCanViewPermission =
                validateUserAccessUseCase.getCountriesCanViewAccountsOrThrow(RequestContext.getUser());
        return getAccountFiltersAccordingToCountryPermissions(accountFilters, countriesWithCanViewPermission);
    }

    private AccountFilters getAccountFiltersWithUpdatedCountryCodesBasedOnUserAccessToDownload
            (AccountFilters accountFilters)
            throws UserForbiddenException {
        List<CountryCode> countriesWithCanViewPermission =
                validateUserAccessUseCase.getCountriesCanDownloadAccountsOrThrow(RequestContext.getUser());
        return getAccountFiltersAccordingToCountryPermissions(accountFilters, countriesWithCanViewPermission);
    }

    private AccountFilters getAccountFiltersWithUpdatedCountryCodesBasedOnUserAccessTroubleshooting
            (AccountFilters accountFilters)
            throws UserForbiddenException {
        List<CountryCode> countriesWithCanViewPermission =
                validateUserAccessUseCase.getCountriesCanAccessTroubleshootOrThrow(RequestContext.getUser());
        return getAccountFiltersAccordingToCountryPermissions(accountFilters, countriesWithCanViewPermission);
    }

    private AccountFilters getAccountFiltersAccordingToCountryPermissions
            (AccountFilters accountFilters, List<CountryCode> countriesWithCanViewPermission) {
        List<Long> countryCodesFromFilters = accountFilters.getCountryCodes();
        List<Long> filteredCountryCodes;
        if (countryCodesFromFilters != null && !countryCodesFromFilters.isEmpty()) {
            filteredCountryCodes = countryCodesFromFilters.stream()
                    .filter(countryCode -> {
                        try {
                            Country country = readCountriesUseCase.execute(countryCode);
                            CountryCode countryCodeFromCountry = CountryCode.valueOf(country.getCode());
                            log.info("Country found for : {}", countryCode);
                            return countriesWithCanViewPermission.contains(countryCodeFromCountry);
                        } catch (NotFoundException e) {
                            return false;
                        }
                    }).collect(Collectors.toList());

        } else {
            filteredCountryCodes = countriesWithCanViewPermission.stream().map(countryCode -> {
                try {
                    Country country = readCountriesUseCase.execute(String.valueOf(countryCode));
                    log.info("Country found for : {}", countryCode);
                    return country.getId();
                } catch (NotFoundException e) {
                    log.debug("Country not found for ID: {}", countryCode, e);
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());

        }
        accountFilters.setCountryCodes(filteredCountryCodes);
        return accountFilters;
    }

}
