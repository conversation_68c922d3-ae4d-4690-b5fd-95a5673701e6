package pt.jumia.services.brad.api.payloads.request.contact;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.account.Contact;
import pt.jumia.services.brad.domain.entities.filter.contact.ContactSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContactSortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = Contact.SortingFields.class)
    private String orderField = Contact.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public ContactSortFilters toEntity() {
        return ContactSortFilters.builder()
                .field(Contact.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }
}
