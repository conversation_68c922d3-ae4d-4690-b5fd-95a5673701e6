package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.Jobs;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class JobApiResponsePayload {
    private String jobName;
    private String cronExpression;
    private String state;
    private String timezone;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime lastFiredTime;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime nextFireTime;

    public JobApiResponsePayload(Jobs job) {
        this.jobName = job.getJobName();
        this.cronExpression = job.getCronExpression();
        this.state = job.getState();
        this.timezone = job.getTimezone();
        this.lastFiredTime = job.getLastFiredTime();
        this.nextFireTime = job.getNextFireTime();
    }

    public Jobs toEntity() {
        return Jobs
                .builder()
                .jobName(jobName)
                .cronExpression(cronExpression)
                .state(state)
                .timezone(timezone)
                .lastFiredTime(lastFiredTime)
                .nextFireTime(nextFireTime)
                .build();
    }
}
