package pt.jumia.services.brad.api.payloads.response.group;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.group.BaleGroup;

import java.util.List;

@Data
@NoArgsConstructor
public class BaleGroupingsApiResponsePayload {

    private List<String> groupings;
    private List<List<String>> result;

    public BaleGroupingsApiResponsePayload(BaleGroup group) {
        this.groupings = group.getGroupings().stream().map(Enum::name).toList();
        this.result = group.getResult();
    }
}
