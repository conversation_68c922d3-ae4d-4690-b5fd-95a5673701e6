package pt.jumia.services.brad.api.csvs;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.api.csvs.imports.CsvParser;
import pt.jumia.services.brad.api.validations.annotations.CsvHeaderName;
import pt.jumia.services.brad.domain.exceptions.InvalidFileException;

import java.io.*;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Component
@Slf4j
public class CsvBuilder {


    public static List<String> getFieldNames(Class<?> clazz) {
        List<String> fieldNames = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(CsvHeaderName.class)) {
                fieldNames.add(field.getAnnotation(CsvHeaderName.class).value());
                continue;
            }
            fieldNames.add(field.getName());
        }
        return fieldNames;
    }

    private static List<Object> getFieldValues(Object obj) throws IllegalAccessException {
        List<Object> values = new ArrayList<>();
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            values.add(field.get(obj));
        }
        return values;
    }

    public static <T> ResponseEntity<byte[]> buildCsv(List<T> entities) {

        if (entities.isEmpty()){
            log.debug("No entity found for the provided filters");
            throw new RuntimeException("Error downloading CSV. No entity found for the provided filters");
        }

        String[] headers = getFieldNames(entities.get(0).getClass()).toArray(String[]::new);

        return writeCsv(entities, headers);
    }


    public static <T> ResponseEntity<byte[]> buildCsv(List<T> entities, List<String> headersList) {

        if (entities.isEmpty()) {
            log.debug("No entity found for the provided filters");
            throw new RuntimeException("Error downloading CSV. No entity found for the provided filters");
        }
        String[] headers = headersList.toArray(String[]::new);

        return writeCsv(entities, headers);

    }

    private static <T> ResponseEntity<byte[]> writeCsv(final List<T> entities, final String[] headers) {

        try (
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
                CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(headers))
        ) {
            for (T entity : entities) {
                csvPrinter.printRecord(getFieldValues(entity));
            }

            csvPrinter.flush();
            byte[] csvBytes = outputStream.toByteArray();
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + entities.get(0).getClass() + ".csv")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .contentLength(csvBytes.length)
                    .body(csvBytes);
        } catch (Exception e) {
            log.error("Error while generating CSV file", e);
            throw new RuntimeException("Error while generating CSV file", e);
        }
    }

    public static <T> List<T> readCsv(String file, CsvParser<T> csvParser) throws InvalidFileException {
        List<T> objectList = new ArrayList<>();

        try {
            byte[] decodedBytes = Base64.getDecoder().decode(file.substring(file.indexOf(",") + 1));
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(decodedBytes);
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(byteArrayInputStream, StandardCharsets.UTF_8));

            String line = bufferedReader.readLine();
            while (line != null) {
                line = bufferedReader.readLine();
                if (line == null) {
                    break;
                }
                String[] values = line.split(",");
                T object = csvParser.parseFromCSV(values);
                objectList.add(object);

            }

        } catch (Exception e) {
            log.error("Error while reading CSV file", e);
            throw InvalidFileException.createInvalidFile(e.getMessage());
        }

        return objectList;
    }

}
