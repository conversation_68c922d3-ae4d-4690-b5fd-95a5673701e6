package pt.jumia.services.brad.api.payloads.request.transaction;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionGroupFilters;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TransactionGroupFiltersApiRequestPayload {

    private List<String> fields;

    public TransactionGroupFilters toEntity() {
        return new TransactionGroupFilters(Transaction.GroupingFields.fromValues(this.fields));
    }

}
