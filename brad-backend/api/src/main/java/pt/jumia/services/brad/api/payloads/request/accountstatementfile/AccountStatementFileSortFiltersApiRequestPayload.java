package pt.jumia.services.brad.api.payloads.request.accountstatementfile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountStatementFileSortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = AccountStatementFile.SortingFields.class)
    private String orderField = AccountStatementFile.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public AccountStatementFileSortFilters toEntity() {

        return AccountStatementFileSortFilters.builder()
            .field(AccountStatementFile.SortingFields.valueOf(orderField))
            .direction(OrderDirection.valueOf(orderDirection))
            .build();
    }

}
