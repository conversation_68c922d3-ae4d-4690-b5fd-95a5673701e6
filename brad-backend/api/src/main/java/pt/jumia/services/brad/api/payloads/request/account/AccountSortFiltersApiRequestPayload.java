package pt.jumia.services.brad.api.payloads.request.account;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.filter.account.AccountSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountSortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = Account.SortingFields.class)
    private String orderField = Account.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public AccountSortFilters toEntity() {
        return AccountSortFilters.builder()
                .field(Account.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }
}
