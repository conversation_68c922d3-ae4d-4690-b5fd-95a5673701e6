package pt.jumia.services.brad.api.validations.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import pt.jumia.services.brad.api.validations.validators.GenericPayloadValidator;
import pt.jumia.services.brad.api.validations.validators.PayloadValidator;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


@Documented
@Constraint(validatedBy = GenericPayloadValidator.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidPayload {
    String message() default "Invalid payload";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
    Class<? extends PayloadValidator<?>> payloadValidator();
}
