package pt.jumia.services.brad.api.validations.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.SneakyThrows;
import org.springframework.util.StringUtils;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.util.Set;
import java.util.stream.Collectors;

public class EnumValueValidator implements ConstraintValidator<ValidEnumValue, String> {

    private ValidEnumValue params;

    private Set<String> availableEnumNames;

    @Override
    public void initialize(ValidEnumValue stringEnumeration) {
        this.params = stringEnumeration;

        Class<? extends Enum<?>> enumSelected = stringEnumeration.enumClass();
        Set<? extends Enum<?>> enumInstances = Set.of(enumSelected.getEnumConstants());
        availableEnumNames = enumInstances.stream().map(Enum::name).collect(Collectors.toSet());
    }

    @SneakyThrows
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (!this.params.required() && !StringUtils.hasText(value)) {
            return true;
        }
        if (this.params.required() && !StringUtils.hasText(value)) {
            return false;
        } else if (availableEnumNames.stream().anyMatch(s -> s.equalsIgnoreCase(value))) {
            return true;
        }

        try {
            throw EntityErrorsException.createSortError(value, this.params.enumClass().getEnclosingClass(),
                    availableEnumNames.toString());
        } catch (Exception ignored) {
            throw EntityErrorsException.createSortError(value, this.params.enumClass(), availableEnumNames.toString());
        }

    }
}
