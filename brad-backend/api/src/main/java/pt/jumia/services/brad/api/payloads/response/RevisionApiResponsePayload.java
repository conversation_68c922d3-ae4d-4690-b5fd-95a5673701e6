package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.AuditedEntity;

import java.time.LocalDateTime;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RevisionApiResponsePayload {

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime datetime;
    private String username;

    public RevisionApiResponsePayload(AuditedEntity.RevisionInfo revisionInfo) {
        this.datetime = revisionInfo.getDatetime();
        this.username = revisionInfo.getEmail();
    }

    public AuditedEntity.RevisionInfo toEntity() {
        return AuditedEntity.RevisionInfo.builder()
                .datetime(datetime)
                .email(username)
                .build();
    }
}
