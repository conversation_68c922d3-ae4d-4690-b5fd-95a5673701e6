package pt.jumia.services.brad.api.payloads.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.Jobs;

@Data
@NoArgsConstructor
public class JobApiRequestPayload {
    private String jobName;
    private String cronExpression;
    private String state;

    public JobApiRequestPayload(String jobName, String cronExpression, String state) {
        this.jobName = jobName;
        this.cronExpression = cronExpression;
        this.state = state;
    }

    public JobApiRequestPayload(Jobs job) {
        this.jobName = job.getJobName();
        this.cronExpression = job.getCronExpression();
        this.state = job.getState();
    }

    public Jobs toEntity() {
        return Jobs
                .builder()
                .jobName(jobName)
                .cronExpression(cronExpression)
                .state(state)
                .build();
    }
}
