package pt.jumia.services.brad.api.payloads.request.viewentity;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.ViewEntity;

@Data
@NoArgsConstructor
public class ViewEntityApiRequestPayload {

    private Long id;

    @NotBlank
    private String driver;

    @NotBlank
    private String jdbcConnectionUrl;

    @NotBlank
    private String schemaName;

    @NotBlank
    private String databaseName;

    @NotBlank
    private String viewName;

    public ViewEntityApiRequestPayload(ViewEntity viewEntity) {
        this.driver = viewEntity.getDriver();
        this.jdbcConnectionUrl = viewEntity.getJdbcConnectionUrl();
        this.databaseName = viewEntity.getDatabaseName();
        this.schemaName = viewEntity.getSchemaName();
        this.viewName = viewEntity.getViewName();
    }

    public ViewEntity toEntity() {
        return ViewEntity.builder()
            .id(this.id)
            .driver(driver)
            .jdbcConnectionUrl(this.jdbcConnectionUrl)
            .databaseName(this.databaseName)
            .schemaName(this.schemaName)
            .viewName(this.viewName)
            .build();
    }
}
