package pt.jumia.services.brad.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.dtos.AccountTroubleshootingDto;

@Data
@NoArgsConstructor
public class AccountApiTroubleShootingResponsePayload extends AccountApiResponsePayload {

    private Boolean isOutOfSync;
    private Boolean manualUploadMissing;
    private Boolean failedStatementValidation;

    public AccountApiTroubleShootingResponsePayload(AccountTroubleshootingDto dto) {

        super(dto.getAccount());
        this.isOutOfSync = dto.getIsOutOfSync();
        this.manualUploadMissing = dto.getManualUploadMissing();
        this.failedStatementValidation = dto.getFailedStatementValidation();
    }

}
