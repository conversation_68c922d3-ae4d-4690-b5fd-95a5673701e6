package pt.jumia.services.brad.api.csvs.exports;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import org.springframework.http.ResponseEntity;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.api.validations.annotations.CsvHeaderName;
import pt.jumia.services.brad.domain.entities.account.Contact;

import java.time.LocalDateTime;
import java.util.List;

import static pt.jumia.services.brad.api.csvs.CsvBuilder.buildCsv;
@Builder(toBuilder = true)
public class ContactExportCSV {

        @CsvHeaderName("ID")
        public Long id;
        @CsvHeaderName("Company ID")
        public String companyID;
        @CsvHeaderName("Country")
        public String countryName;
        @CsvHeaderName("NAV Reference")
        public String navReference;
        @CsvHeaderName("ContactType")
        public String contactType;
        @CsvHeaderName("Name")
        public String name;
        @CsvHeaderName("Email")
        public String email;
        @CsvHeaderName("Phone Number")
        public String mobilePhoneNumber;
        @CsvHeaderName("Bank Account Number")
        public String bankAccountNumber;
        @CsvHeaderName("Created By")
        public String createdBy;
        @CsvHeaderName("Created At")
        @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
        public LocalDateTime createdAt;
        @CsvHeaderName("Updated By")
        public String updatedBy;
        @CsvHeaderName("Updated At")
        @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
        public LocalDateTime updatedAt;

        public static ResponseEntity<byte[]> buildContactCSV(List<Contact> contacts) {

            List<ContactExportCSV> contactExportCSVs = contacts.stream()
                    .map((contact) -> ContactExportCSV.builder()
                            .id(contact.getId())
                            .companyID(contact.getAccount().getCompanyID())
                            .countryName(contact.getAccount().getCountry().getName())
                            .navReference(contact.getAccount().getNavReference())
                            .contactType(contact.getContactType())
                            .name(contact.getName())
                            .email(contact.getEmail())
                            .mobilePhoneNumber(contact.getMobilePhoneNumber())
                            .bankAccountNumber(contact.getAccount().getAccountNumber())
                            .createdAt(contact.getCreatedAt())
                            .createdBy(contact.getCreatedBy())
                            .updatedAt(contact.getUpdatedAt())
                            .updatedBy(contact.getUpdatedBy())
                            .build())
                    .toList();

            return buildCsv(contactExportCSVs);
        }
}
