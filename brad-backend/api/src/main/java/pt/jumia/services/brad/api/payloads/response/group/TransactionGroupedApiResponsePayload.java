package pt.jumia.services.brad.api.payloads.response.group;

import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.TransactionApiResponsePayload;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class TransactionGroupedApiResponsePayload {

    private PageApiResponsePayload<TransactionApiResponsePayload> pagedEntityList;
    private List<Long> allIds;

    public TransactionGroupedApiResponsePayload(
            HttpServletRequest httpServletRequest,
            PageFilters pageFilters,
            List<Transaction> transactions,
            List<Long> allIds,
            Integer totalQuantity
    ) {
        this.pagedEntityList = PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                transactions.stream().map(TransactionApiResponsePayload::new)
                        .collect(Collectors.toList()),
                totalQuantity);
        this.allIds = new ArrayList<>(allIds);
    }
}
