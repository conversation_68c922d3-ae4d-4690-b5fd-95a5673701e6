package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.aig.aigx.commons.exceptions.InvalidArgumentException;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Account.Type;
import pt.jumia.services.brad.domain.enumerations.StatementSource;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

@Data
@NoArgsConstructor
public class AccountApiResponsePayload {

    private static final String dateFormat = "yyyy-MM-dd";

    private Long id;
    private String companyID;
    private CountryApiResponsePayload country;
    private String navReference;
    private String beneficiaryName;
    private String beneficiaryAddress;
    private String iban;
    private String accountNumber;
    private String accountName;
    private String swiftCode;
    private String accountRoutingCode;
    private String sortCode;
    private String branchCode;
    private String rib;
    private String partner;
    private String phoneNumber;
    private CurrencyApiResponsePayload currency;
    private String type;
    private String subType;
    @JsonProperty("status")
    private String accountStatus;
    private BigDecimal balance;
    private BigDecimal balanceUSD;
    private String isin;
    private String contractId;
    private BigDecimal amountDeposited;
    @JsonFormat(pattern = dateFormat)
    private LocalDate maturityDate;
    private BigDecimal nominalAmount;
    private String couponPaymentPeriodicity;
    private BigDecimal couponRate;
    private BigDecimal interest;
    private String createdBy;
    private String updatedBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;
    @JsonFormat(pattern = dateFormat)
    private LocalDate lastStatementDate;
    @JsonFormat(pattern = dateFormat)
    private LocalDate lastProcessedStatementDate;
    private String statementSource;
    private String statementPeriodicity;
    private Boolean hasError;

    public AccountApiResponsePayload(Account account) {
        this.id = account.getId();
        this.companyID = account.getCompanyID();
        this.country = new CountryApiResponsePayload(account.getCountry());
        this.navReference = account.getNavReference();
        this.beneficiaryName = account.getBeneficiaryName();
        this.beneficiaryAddress = account.getBeneficiaryAddress();
        this.iban = account.getIban();
        this.accountNumber = account.getAccountNumber();
        this.accountName = account.getAccountName();
        this.swiftCode = account.getSwiftCode();
        this.accountRoutingCode = account.getBankRoutingCode();
        this.sortCode = account.getSortCode();
        this.branchCode = account.getBranchCode();
        this.rib = account.getRib();
        this.partner = account.getPartner();
        this.phoneNumber = account.getPhoneNumber();
        this.currency = new CurrencyApiResponsePayload(account.getCurrency());
        this.type = account.getType().name();
        this.subType = account.getSubType() != null ? account.getSubType().name() : null;
        this.accountStatus = account.getStatus().getValue();
        this.balance = account.getBalance();
        this.balanceUSD = account.getBalanceUSD();
        this.isin = account.getIsin();
        this.contractId = account.getContractId();
        this.amountDeposited = account.getAmountDeposited();
        this.maturityDate = account.getMaturityDate();
        this.nominalAmount = account.getNominalAmount();
        this.couponPaymentPeriodicity = account.getCouponPaymentPeriodicity() != null ? 
            account.getCouponPaymentPeriodicity().name() : null;
        this.couponRate = account.getCouponRate();
        this.interest = account.getInterest();
        this.createdAt = account.getCreatedAt();
        this.updatedAt = account.getUpdatedAt();
        this.createdBy = account.getCreatedBy();
        this.updatedBy = account.getUpdatedBy();
        this.lastStatementDate = account.getLastStatementDate();
        this.lastProcessedStatementDate = account.getLastProcessedStatementDate();
        this.statementSource = account.getStatementSource().getValue();
        this.statementPeriodicity = account.getStatementPeriodicity().name();
        this.hasError = account.getHasError();
    }

    public Account toEntity() {
        return Account
                .builder()
                .id(id)
                .companyID(companyID)
                .country(country.toEntity())
                .navReference(navReference)
                .beneficiaryName(beneficiaryName)
                .beneficiaryAddress(beneficiaryAddress)
                .iban(iban)
                .accountNumber(accountNumber)
                .accountName(accountName)
                .swiftCode(swiftCode)
                .bankRoutingCode(accountRoutingCode)
                .sortCode(sortCode)
                .branchCode(branchCode)
                .rib(rib)
                .partner(partner)
                .phoneNumber(phoneNumber)
                .currency(currency.toEntity())
                .type(Type.valueOf(type))
                .subType(subType != null ? Account.SubType.valueOf(subType) : null)
                .status(getStatus())
                .balance(balance)
                .balanceUSD(balanceUSD)
                .isin(isin)
                .contractId(contractId)
                .amountDeposited(amountDeposited)
                .maturityDate(maturityDate)
                .nominalAmount(nominalAmount)
                .couponPaymentPeriodicity(couponPaymentPeriodicity != null ? 
                    Account.CouponPaymentPeriodicity.valueOf(couponPaymentPeriodicity) : null)
                .couponRate(couponRate)
                .interest(interest)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .lastStatementDate(lastStatementDate)
                .lastProcessedStatementDate(lastProcessedStatementDate)
                .statementSource(StatementSource.valueOf(statementSource))
                .statementPeriodicity(Account.StatementPeriodicity.valueOf(statementPeriodicity))
                .build();
    }

    private Account.Status getStatus() {

        return Arrays.stream(Account.Status.values())
                .filter(s -> s.getValue().equalsIgnoreCase(accountStatus))
                .findFirst()
                .orElseThrow(() -> new InvalidArgumentException(String.format("Invalid bank status: %s", accountStatus)));
    }
}
