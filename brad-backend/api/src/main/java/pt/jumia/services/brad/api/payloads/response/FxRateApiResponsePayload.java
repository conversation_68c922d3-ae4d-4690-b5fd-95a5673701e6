package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.FxRate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class FxRateApiResponsePayload {

    private Integer id;
    private CurrencyApiResponsePayload baseCurrency;
    private CurrencyApiResponsePayload quoteCurrency;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate rateDate;
    private BigDecimal bid;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime bisLoadedAt;
    private Integer skAudInsert;
    private Integer skAudUpdate;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime timestampLastUpdate;

    public FxRateApiResponsePayload(FxRate fxRate){
        this.id = fxRate.getId();
        this.baseCurrency = new CurrencyApiResponsePayload(fxRate.getBaseCurrency());
        this.quoteCurrency = new CurrencyApiResponsePayload(fxRate.getQuoteCurrency());
        this.rateDate = fxRate.getRateDate();
        this.bid = fxRate.getBid();
        this.bisLoadedAt = fxRate.getBisLoadedAt();
        this.skAudInsert = fxRate.getSkAudInsert();
        this.skAudUpdate = fxRate.getSkAudUpdate();
        this.timestampLastUpdate = fxRate.getTimestampLastUpdate();
    }

    public FxRate toEntity() {
        return FxRate.builder()
                .id(this.id)
                .baseCurrency(this.baseCurrency.toEntity())
                .quoteCurrency(this.quoteCurrency.toEntity())
                .rateDate(this.rateDate)
                .bid(this.bid)
                .bisLoadedAt(this.bisLoadedAt)
                .skAudInsert(this.skAudInsert)
                .skAudUpdate(this.skAudUpdate)
                .timestampLastUpdate(this.timestampLastUpdate)
                .build();
    }
}
