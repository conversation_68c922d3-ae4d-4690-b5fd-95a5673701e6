package pt.jumia.services.brad.api.payloads.response.group;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.group.TransactionGroup;

import java.util.List;

@Data
@NoArgsConstructor
public class TransactionGroupingsApiResponsePayload {

    private List<String> groupings;
    private List<List<String>> result;

    public TransactionGroupingsApiResponsePayload(TransactionGroup group) {
        this.groupings = group.getGroupings().stream().map(Enum::name).toList();
        this.result = group.getResult();
    }
}
