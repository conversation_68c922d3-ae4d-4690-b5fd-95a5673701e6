package pt.jumia.services.brad.api.payloads.request.transaction;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class TransactionFiltersApiRequestPayload {

    private String filterText;
    private String type;
    private String accountId;
    private String partitionKey;
    private List<String> currencyCodes;
    private String valueDateStart;
    private String valueDateEnd;
    private String transactionDateStart;
    private String transactionDateEnd;
    private String statementDateStart;
    private String statementDateEnd;
    private List<String> direction;
    private BigDecimal amount;
    private BigDecimal amountLocalCurrency;
    private BigDecimal rate;
    private String reference;
    private String description;
    private String accountStatementID;
    private String remittanceInformation;
    private String orderingPartyName;
    private boolean exactFilters;
    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAtStart;
    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAtEnd;

    private boolean withReconciliationFilters;
    //reconciliation related filters
    private Integer reconciliationId;
    private String reconciliationCreator;
    private String reconciliationCreationDateStart;
    private String reconciliationCreationDateEnd;
    private String reconciliationReviewer;
    private String reconciliationReviewDateStart;
    private String reconciliationReviewDateEnd;
    private List<String> reconciliationStatus;

    private boolean importedStatementOnly;

    private List<String> selectedFields;

    public TransactionFilters toEntity() throws ParseException {
        return TransactionFilters
                .builder()
                .filterText(filterText)
                .type(type)
                .partitionKey(partitionKey)
                .accountId(accountId)
                .currency(currencyCodes)
                .valueDateStart(DateParser.parseToLocalDate(valueDateStart))
                .valueDateEnd(DateParser.parseToLocalDate(valueDateEnd))
                .transactionDateStart(DateParser.parseToLocalDate(transactionDateStart))
                .transactionDateEnd(DateParser.parseToLocalDate(transactionDateEnd))
                .statementDateStart(DateParser.parseToLocalDate(statementDateStart))
                .statementDateEnd(DateParser.parseToLocalDate(statementDateEnd))
                .direction(direction)
                .amount(amount)
                .amountLocalCurrency(amountLocalCurrency)
                .rate(rate)
                .reference(reference)
                .description(description)
                .accountStatementID(accountStatementID)
                .remittanceInformation(remittanceInformation)
                .orderingPartyName(orderingPartyName)
                .createdAtStart(createdAtStart)
                .createdAtEnd(createdAtEnd)
                .exactFilters(exactFilters)
                .reconciliationId(reconciliationId)
                .reconciliationCreator(reconciliationCreator)
                .reconciliationCreationDateStart(DateParser.parseToLocalDate(reconciliationCreationDateStart))
                .reconciliationCreationDateEnd(DateParser.parseToLocalDate(reconciliationCreationDateEnd))
                .reconciliationReviewer(reconciliationReviewer)
                .reconciliationReviewDateStart(DateParser.parseToLocalDate(reconciliationReviewDateStart))
                .reconciliationReviewDateEnd(DateParser.parseToLocalDate(reconciliationReviewDateEnd))
                .reconciliationStatus(reconciliationStatus)
                .importedStatementOnly(importedStatementOnly)
                .selectedFields(Objects.isNull(selectedFields) ? List.of() : BaseSelectFields.fromSelectCodes(
                        Transaction.SelectFields.class, selectedFields)
                        .stream().map(Transaction.SelectFields::getQueryField).toList()
                )
                .build();

    }

}
