package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.account.Country;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class CountryApiResponsePayload {

    private Long id;
    private String name;
    private String code;
    private CurrencyApiResponsePayload currency;
    private String createdBy;
    private String updatedBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;

    public CountryApiResponsePayload(Country country) {
        this.id = country.getId();
        this.name = country.getName();
        this.code = country.getCode();
        this.currency = country.getCurrency() != null ? new CurrencyApiResponsePayload(country.getCurrency()) : null;
        this.createdAt = country.getCreatedAt();
        this.updatedAt = country.getUpdatedAt();
        this.createdBy = country.getCreatedBy();
        this.updatedBy = country.getUpdatedBy();
    }

    public Country toEntity() {
        return Country.builder()
            .id(id)
            .name(name)
            .code(code)
            .currency(currency != null ? currency.toEntity() : null)
            .createdAt(createdAt)
            .createdBy(createdBy)
            .updatedAt(updatedAt)
            .updatedBy(updatedBy)
            .build();
    }
}
