package pt.jumia.services.brad.api.payloads.request.executionlog;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.filter.executionlog.ExecutionLogFilters;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.text.ParseException;
import java.util.List;
import java.util.Objects;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ExecutionLogFiltersApiRequestPayload {

    private Long id;
    private List<String> logTypes;
    private List<String> logStatuses;
    private Integer recordsAmount;
    private String executionStartTime;
    private String executionEndTime;
    private String relatedEntity;

    private List<String> selectedFields;

    public ExecutionLogFilters toEntity() throws ParseException {
        return ExecutionLogFilters
                .builder()
                .id(id)
                .logType(logTypes)
                .logStatus(logStatuses)
                .recordsAmount(recordsAmount)
                .executionStartTime(StringUtils.isEmpty(executionStartTime) ? null :
                        DateParser.parseToLocalDateTime(executionStartTime))
                .executionEndTime(StringUtils.isEmpty(executionEndTime) ? null :
                        DateParser.parseToLocalDateTime(executionEndTime))
                .selectedFields(Objects.isNull(selectedFields) ? List.of() : BaseSelectFields.fromSelectCodes(
                                ExecutionLog.SelectFields.class, selectedFields)
                        .stream().map(ExecutionLog.SelectFields::getQueryField).toList()
                )
                .relatedEntity(relatedEntity)
                .build();
    }

}
