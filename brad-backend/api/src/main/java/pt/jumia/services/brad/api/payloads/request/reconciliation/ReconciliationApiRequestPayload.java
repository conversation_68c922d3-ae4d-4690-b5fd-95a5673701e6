package pt.jumia.services.brad.api.payloads.request.reconciliation;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.enumerations.ReconciliationStatus;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
public class ReconciliationApiRequestPayload {

    private Integer id;
    private String status;
    private String creator;
    private String approver;
    private BigDecimal amount;

    private Long account;
    private List<Long> baleIds;
    private List<Long> transactionIds;

    public ReconciliationApiRequestPayload(Long account, List<Long> baleIds, List<Long> transactionIds) {
        this.account = account;
        this.baleIds = new ArrayList<>(baleIds);
        this.transactionIds = new ArrayList<>(transactionIds);
    }

    public Reconciliation toEntity() {
        return Reconciliation.builder()
                .status(Objects.equals(status, null) ? ReconciliationStatus.fromString("PENDING") : ReconciliationStatus.fromString(status))
                .baleIds(baleIds)
                .transactionIds(transactionIds)
                .build();
    }



}
