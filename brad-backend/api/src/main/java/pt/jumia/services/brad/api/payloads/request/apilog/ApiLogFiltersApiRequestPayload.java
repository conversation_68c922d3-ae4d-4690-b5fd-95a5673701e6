package pt.jumia.services.brad.api.payloads.request.apilog;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import pt.jumia.services.brad.domain.entities.ApiLog;
import pt.jumia.services.brad.domain.entities.filter.apilog.ApiLogFilters;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.text.ParseException;
import java.util.List;
import java.util.Objects;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ApiLogFiltersApiRequestPayload {


    private List<String> logType;
    private String relatedEntityId;
    private List<String> logStatus;
    private String request;

    private String createdAtStart;
    private String createdAtEnd;

    private List<String> selectedFields;

    public ApiLogFilters toEntity() throws ParseException {

        return ApiLogFilters
            .builder()
            .logType(logType)
            .relatedEntityId(relatedEntityId)
            .logStatus(logStatus)
            .selectedFields(Objects.isNull(selectedFields) ? List.of() : BaseSelectFields.fromSelectCodes(
                            ApiLog.SelectFields.class, selectedFields)
                    .stream().map(ApiLog.SelectFields::getQueryField).toList()
            )
            .request(request)
            .createdAtStart(StringUtils.isEmpty(createdAtStart) ? null :
                    DateParser.parseToLocalDateTime(createdAtStart))
            .createdAtEnd(StringUtils.isEmpty(createdAtEnd) ? null :
                    DateParser.parseToLocalDateTime(createdAtEnd))
            .build();

    }
}
