package pt.jumia.services.brad.api.payloads.request.contact;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.account.Contact;

import java.util.Locale;

@Data
@NoArgsConstructor
public class ContactApiRequestPayload {

    private Long id;

    private String contactType;

    private String name;

    private String email;

    private String mobilePhoneNumber;

    private Long accountID;

    private String createdBy;

    private String updatedBy;

    private String createdAt;

    private String updatedAt;


    public ContactApiRequestPayload(Contact contact) {

        this.id = contact.getId();
        this.contactType = contact.getContactType();
        this.name = contact.getName();
        this.email = contact.getEmail();
        this.mobilePhoneNumber = contact.getMobilePhoneNumber();
        this.accountID = contact.getAccount().getId();
    }

    public Contact toEntity() {

        return Contact.builder()
            .id(id)
            .contactType(formatContactType(contactType))
            .name(name)
            .email(email)
            .mobilePhoneNumber(mobilePhoneNumber)
            .build();
    }

    private String formatContactType(String otherContactType) {

        return otherContactType.trim()
            .replaceAll("\\s+", "_")
            .replace("-", "_")
            .toUpperCase(Locale.ENGLISH);
    }

}
