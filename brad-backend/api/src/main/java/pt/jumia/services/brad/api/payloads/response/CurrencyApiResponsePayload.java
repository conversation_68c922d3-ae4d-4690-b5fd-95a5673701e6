package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.Currency;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class CurrencyApiResponsePayload {

    private Long id;
    private String name;
    private String code;
    private String symbol;
    private String createdBy;
    private String updatedBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;

    public CurrencyApiResponsePayload(Currency currency) {
        this.id = currency.getId();
        this.name = currency.getName();
        this.code = currency.getCode();
        this.symbol = currency.getSymbol();
        this.createdAt = currency.getCreatedAt();
        this.updatedAt = currency.getUpdatedAt();
        this.createdBy = currency.getCreatedBy();
        this.updatedBy = currency.getUpdatedBy();
    }

    public Currency toEntity() {
        return Currency.builder()
            .id(id)
            .name(name)
            .code(code)
            .symbol(symbol)
            .createdAt(createdAt)
            .createdBy(createdBy)
            .updatedAt(updatedAt)
            .updatedBy(updatedBy)
            .build();
    }
}
