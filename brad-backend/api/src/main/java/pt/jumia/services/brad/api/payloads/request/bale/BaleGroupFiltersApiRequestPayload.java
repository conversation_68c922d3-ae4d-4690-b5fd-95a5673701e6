package pt.jumia.services.brad.api.payloads.request.bale;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleGroupFilters;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaleGroupFiltersApiRequestPayload {

    private List<String> fields;

    public BaleGroupFilters toEntity() {
        return new BaleGroupFilters(Bale.GroupingFields.fromValues(this.fields));
    }

}
