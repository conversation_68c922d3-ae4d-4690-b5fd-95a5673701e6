package pt.jumia.services.brad.api.validations.annotations;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import pt.jumia.services.brad.api.validations.validators.EnumValueValidator;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;

@Documented
@Target({FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {EnumValueValidator.class})
public @interface ValidEnumValue {

    String message() default "Not a valid value!";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    Class<? extends Enum<?>> enumClass();

    boolean required() default false;
}
