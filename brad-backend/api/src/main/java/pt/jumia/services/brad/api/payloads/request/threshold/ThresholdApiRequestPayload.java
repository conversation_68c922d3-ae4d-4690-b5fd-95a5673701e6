package pt.jumia.services.brad.api.payloads.request.threshold;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.reconciliation.Threshold;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class ThresholdApiRequestPayload {

    private Long id;
    @NotNull
    private String countryCode;
    @NotNull
    private String currencyCode;
    @NotNull
    private BigDecimal amount;

    public ThresholdApiRequestPayload(Threshold threshold) {
        this.id = threshold.getId();
        this.countryCode = threshold.getCountry().getCode();
        this.currencyCode = threshold.getCurrency().getCode();
        this.amount = threshold.getAmount();
    }

    public Threshold toEntity() {
        return Threshold.builder()
            .id(this.id)
            .country(Country.builder().code(this.countryCode).build())
            .currency(Currency.builder().code(this.currencyCode).build())
            .amount(this.amount)
            .build();
    }
}
