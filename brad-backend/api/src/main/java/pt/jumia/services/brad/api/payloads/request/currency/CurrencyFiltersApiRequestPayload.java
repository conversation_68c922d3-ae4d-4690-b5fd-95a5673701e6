package pt.jumia.services.brad.api.payloads.request.currency;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.filter.currency.CurrencyFilters;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

import java.util.List;
import java.util.Objects;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class CurrencyFiltersApiRequestPayload {

    private String filterText;

    private List<String> selectedFields;

    public CurrencyFilters toEntity() {
        return CurrencyFilters
                .builder()
                .filterText(filterText)
                .selectedFields(Objects.isNull(selectedFields) ? List.of() : BaseSelectFields.fromSelectCodes(
                                Currency.SelectFields.class, selectedFields)
                        .stream().map(Currency.SelectFields::getQueryField).toList()
                )
                .build();

    }
}
