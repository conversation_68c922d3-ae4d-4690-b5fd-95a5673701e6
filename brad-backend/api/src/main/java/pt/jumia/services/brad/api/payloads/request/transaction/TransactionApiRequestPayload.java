package pt.jumia.services.brad.api.payloads.request.transaction;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.math.BigDecimal;
import java.text.ParseException;

@Data
@NoArgsConstructor
public class TransactionApiRequestPayload {

    @NotBlank
    String type;

    @NotBlank
    @JsonProperty("currency")
    String currencyCode;

    @NotNull
    @JsonProperty("value_date")
    String valueDate;

    @NotNull
    @JsonProperty("transaction_date")
    String transactionDate;

    @NotNull
    @JsonProperty("statement_date")
    String statementDate;

    @Min(1)
    @Max(2)
    @NotNull
    Integer direction;

    @NotNull
    @Positive
    @Digits(integer = 10, fraction = 2)
    BigDecimal amount;

    String reference;

    @NotBlank
    String description;

    String remittanceInformation;

    String orderingPartyName;

    public TransactionApiRequestPayload(final Transaction transaction) {

        this.type = transaction.getType();
        this.currencyCode = transaction.getCurrency().getCode();
        this.valueDate = transaction.getValueDate().toString();
        this.transactionDate = transaction.getTransactionDate().toString();
        this.statementDate = transaction.getStatementDate().toString();
        this.direction = transaction.getDirection().getValue();
        this.amount = transaction.getAmount();
        this.reference = transaction.getReference();
        this.description = transaction.getDescription();
        this.remittanceInformation = transaction.getRemittanceInformation();
        this.orderingPartyName = transaction.getOrderingPartyName();

    }

    public Transaction toEntity(String statementId) throws ParseException {

        return Transaction.builder()
            .type(this.type)
            .currency(Currency.builder()
                    .code(this.currencyCode)
                    .build())
            .valueDate(DateParser.parseToLocalDate(this.valueDate))
            .transactionDate(DateParser.parseToLocalDate(this.transactionDate))
            .statementDate(DateParser.parseToLocalDate(this.statementDate))
            .direction(Direction.getDirection(String.valueOf(this.direction)))
            .amount(this.amount)
            .reference(this.reference)
            .description(this.description)
            .accountStatement(AccountStatement.builder().statementId(statementId).build())
            .remittanceInformation(this.remittanceInformation)
            .orderingPartyName(this.orderingPartyName)
            .build();

    }


}
