package pt.jumia.services.brad.api.payloads.request.setting;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.Setting;
import pt.jumia.services.brad.domain.entities.filter.setting.SettingFilters;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class SettingFiltersApiRequestPayload {

    private String property;
    @ValidEnumValue(required = false, enumClass = Setting.Type.class)
    private String type;
    private String overrideKey;
    private String value;

    public SettingFilters toEntity() {

        return new SettingFilters().toBuilder()
            .property(!StringUtils.hasText(property) ? property : property.trim())
            .type(this.type == null ? null : Setting.Type.valueOf(this.type))
            .overrideKey(!StringUtils.hasText(overrideKey) ? overrideKey : overrideKey.trim())
            .value(!StringUtils.hasText(value) ? value : value.trim())
            .build();
    }

}
