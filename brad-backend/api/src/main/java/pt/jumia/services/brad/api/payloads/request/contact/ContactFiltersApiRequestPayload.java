package pt.jumia.services.brad.api.payloads.request.contact;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.account.Contact;
import pt.jumia.services.brad.domain.entities.filter.contact.ContactFilters;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ContactFiltersApiRequestPayload {

    private List<String> contactType;

    private String name;

    private String email;

    private Long accountID;

    private List<Long> accountIds;

    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;

    private List<String> selectedFields;


    public ContactFilters toEntity() {
        return ContactFilters
            .builder()
            .contactType(contactType)
            .name(name)
            .email(email)
            .accountID(accountID)
            .accountIds(accountIds)
            .createdAt(createdAt)
            .selectedFields(Objects.isNull(selectedFields) ? List.of() : BaseSelectFields.fromSelectCodes(
                            Contact.SelectFields.class, selectedFields)
                    .stream().map(Contact.SelectFields::getQueryField).toList()
            )
            .build();

    }

}
