package pt.jumia.services.brad.api.csvs.imports;

import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.math.BigDecimal;
import java.text.ParseException;

public class TransactionImportCsv implements CsvParser<Transaction> {
    @Override
    public Transaction parseFromCSV(String[] values) throws ParseException {
        return Transaction.builder()
                .type(values[0])
                .currency(Currency.builder().code(values[1]).build())
                .valueDate(DateParser.parseToLocalDate(values[2]))
                .transactionDate(DateParser.parseToLocalDate(values[3]))
                .statementDate(DateParser.parseToLocalDate(values[4]))
                .direction(Direction.getDirection(values[5]))
                .amount(new BigDecimal(values[6]))
                .reference(values[7])
                .description(values[8])
                .remittanceInformation(isValidField(values, 9, "<remittance_information>") ? values[9] : null)
                .orderingPartyName(isValidField(values, 10, "<ordering_party_name>") ? values[10] : null)
                .build();
    }

    private boolean isValidField(String[] values, int index, String invalidValue) {
        return !(values.length - 1 < index) && values[index] != null && !values[index].strip().equals(invalidValue);
    }
}
