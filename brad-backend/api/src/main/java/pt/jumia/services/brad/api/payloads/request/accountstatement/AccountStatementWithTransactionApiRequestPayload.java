package pt.jumia.services.brad.api.payloads.request.accountstatement;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import pt.jumia.services.brad.api.payloads.request.transaction.TransactionApiRequestPayload;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Data
@NoArgsConstructor
public class AccountStatementWithTransactionApiRequestPayload {

    @Valid
    @JsonProperty("account_statement")
    AccountStatementApiRequestPayload accountStatement;
    @JsonProperty("transactions")
    List<TransactionApiRequestPayload> transactions;

    public AccountStatementWithTransactionApiRequestPayload(final AccountStatement accountStatement, final List<Transaction> transactions) {
        this.accountStatement = new AccountStatementApiRequestPayload();
        this.accountStatement.currencyCode = accountStatement.getCurrency().getCode();
        this.accountStatement.statementId = accountStatement.getStatementId();
        this.accountStatement.initialDate = accountStatement.getInitialDate().toString();
        this.accountStatement.finalDate = accountStatement.getFinalDate().toString();
        this.accountStatement.initialDirection = accountStatement.getInitialDirection().getValue();
        this.accountStatement.finalDirection = accountStatement.getFinalDirection().getValue();
        this.accountStatement.initialAmount = accountStatement.getInitialAmount();
        this.accountStatement.finalAmount = accountStatement.getFinalAmount();
        this.accountStatement.accountID = accountStatement.getAccount().getAccountNumber();
        this.transactions = transactions == null ? new ArrayList<>() :
                transactions.stream().map(TransactionApiRequestPayload::new).toList();
    }

    public AccountStatement toAccountStatementEntity() throws ParseException {
        return this.accountStatement.toEntity();
    }

    public List<Transaction> toTransactionEntities() {
        return this.transactions.stream().map(transactionApiRequestPayload -> {
            try {
                return transactionApiRequestPayload.toEntity(this.accountStatement.statementId);
            } catch (ParseException e) {
                throw  new RuntimeException("Error parsing transaction", e);
            }
        }).toList();
    }

}
