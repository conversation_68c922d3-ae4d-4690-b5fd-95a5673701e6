package pt.jumia.services.brad.api.payloads.response.views;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.FxRate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class FxRateRawResponsePayload {

    private Integer id;
    private String baseCurrency;
    private String quoteCurrency;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate rateDate;
    private BigDecimal bid;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime bisLoadedAt;
    private Integer skAudInsert;
    private Integer skAudUpdate;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime timestampLastUpdate;

    public FxRateRawResponsePayload(FxRate fxRate){
        this.id = fxRate.getId();
        this.baseCurrency = fxRate.getBaseCurrency().getCode();
        this.quoteCurrency = fxRate.getQuoteCurrency().getCode();
        this.rateDate = fxRate.getRateDate();
        this.bid = fxRate.getBid();
        this.bisLoadedAt = fxRate.getBisLoadedAt();
        this.skAudInsert = fxRate.getSkAudInsert();
        this.skAudUpdate = fxRate.getSkAudUpdate();
        this.timestampLastUpdate = fxRate.getTimestampLastUpdate();
    }
}
