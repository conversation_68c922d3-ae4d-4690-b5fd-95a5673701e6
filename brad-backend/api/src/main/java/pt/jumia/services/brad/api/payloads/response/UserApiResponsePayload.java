package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.account.User;

import java.time.LocalDateTime;
import pt.jumia.services.brad.domain.entities.account.User.Status;

@Data
@NoArgsConstructor
public class UserApiResponsePayload {

    private Long id;
    private String userName;
    private String email;
    private Long accountID;
    private String createdBy;
    private String updatedBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;
    private String hrRole;
    private String permissionType;
    private String mobilePhoneNumber;
    private String status;



    public UserApiResponsePayload(User user) {
        this.id = user.getId();
        this.userName = user.getName();
        this.email = user.getEmail();
        this.accountID = user.getAccount().getId();
        this.createdAt = user.getCreatedAt();
        this.updatedAt = user.getUpdatedAt();
        this.createdBy = user.getCreatedBy();
        this.updatedBy = user.getUpdatedBy();
        this.hrRole = user.getHrRole();
        this.permissionType = user.getPermissionType();
        this.mobilePhoneNumber = user.getMobilePhoneNumber();
        this.status = user.getStatus().name();

    }

    public User toEntity() {
        return User
                .builder()
                .id(id)
                .name(userName)
                .email(email)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .hrRole(hrRole)
                .permissionType(permissionType)
                .mobilePhoneNumber(mobilePhoneNumber)
                .status(Status.valueOf(status))
                .build();
    }

    public User toEntityWithoutAccount() {
        return User
                .builder()
                .id(id)
                .name(userName)
                .account(null)
                .email(email)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .hrRole(hrRole)
                .permissionType(permissionType)
                .mobilePhoneNumber(mobilePhoneNumber)
                .status(Status.valueOf(status))
                .build();
    }
}
