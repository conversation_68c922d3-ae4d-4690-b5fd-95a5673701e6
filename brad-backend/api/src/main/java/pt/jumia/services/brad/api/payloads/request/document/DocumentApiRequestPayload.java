package pt.jumia.services.brad.api.payloads.request.document;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.enumerations.DocumentType;

@Data
@NoArgsConstructor
public class DocumentApiRequestPayload {
    private Long id;

    private String documentType;

    private String name;

    private String description;

    private String file;

    private Long accountID;


    public DocumentApiRequestPayload(Document document) {
        this.id = document.getId();
        this.documentType = document.getDocumentType().toString();
        this.name = document.getName();
        this.file = document.getFile();
        this.description = document.getDescription();
        this.accountID = document.getAccount().getId();
    }

    public Document toEntity() {
        return Document
            .builder()
            .id(id)
            .documentType(DocumentType.fromString(documentType))
            .name(name)
            .description(description)
            .file(file)
            .build();
    }
}
