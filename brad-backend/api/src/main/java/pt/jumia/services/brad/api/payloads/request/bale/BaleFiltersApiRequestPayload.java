package pt.jumia.services.brad.api.payloads.request.bale;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleFilters;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.List;
import java.util.Objects;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class BaleFiltersApiRequestPayload {

    private String filterText;
    
    private String idCompany;
    private String accountNumber;
    private List<Integer> entryNo;
    private String documentNo;
    private String documentType;
    private String postingDateStart;
    private String postingDateEnd;
    private String accountPostingGroup;
    private String description;
    private String sourceCode;
    private String reasonCode;
    private String busLine;
    private String department;
    private List<String> direction;
    private BigDecimal amount;
    private BigDecimal remainingAmount;
    private List<String> transactionCurrency;
    private BigDecimal amountLcy;
    private String balanceAccountType;
    private Boolean isOpen;
    private Boolean isReversed;
    private String postedBy;
    private String externalDocumentNo;
    private String baleTimestamp;
    private String accountTimestamp;
    private boolean exactFilters;

    //reconciliation related filters
    private Integer reconciliationId;
    private String reconciliationCreator;
    private String reconciliationCreationDateStart;
    private String reconciliationCreationDateEnd;
    private String reconciliationReviewer;
    private String reconciliationReviewDateStart;
    private String reconciliationReviewDateEnd;
    private List<String> reconciliationStatus;

    private List<String> selectedFields;

    public BaleFilters toEntity() throws ParseException {
        return BaleFilters
            .builder()
            .filterText(filterText)
            .idCompany(idCompany)
            .accountNumber(accountNumber)
            .entryNo(entryNo)
            .documentNo(documentNo)
            .documentType(documentType)
            .postingDateStart(DateParser.parseToLocalDate(postingDateStart))
            .postingDateEnd(DateParser.parseToLocalDate(postingDateEnd))
            .accountPostingGroup(accountPostingGroup)
            .description(description)
            .sourceCode(sourceCode)
            .reasonCode(reasonCode)
            .busLine(busLine)
            .department(department)
            .direction(direction)
            .amount(amount)
            .remainingAmount(remainingAmount)
            .transactionCurrency(transactionCurrency)
            .amountLcy(amountLcy)
            .balanceAccountType(balanceAccountType)
            .isOpen(isOpen)
            .isReversed(isReversed)
            .postedBy(postedBy)
            .externalDocumentNo(externalDocumentNo)
            .baleTimestamp(baleTimestamp)
            .accountTimestamp(accountTimestamp)
            .exactFilters(exactFilters)
            .reconciliationId(reconciliationId)
            .reconciliationCreator(reconciliationCreator)
            .reconciliationCreationDateStart(DateParser.parseToLocalDate(reconciliationCreationDateStart))
            .reconciliationCreationDateEnd(DateParser.parseToLocalDate(reconciliationCreationDateEnd))
            .reconciliationReviewer(reconciliationReviewer)
            .reconciliationReviewDateStart(DateParser.parseToLocalDate(reconciliationReviewDateStart))
            .reconciliationReviewDateEnd(DateParser.parseToLocalDate(reconciliationReviewDateEnd))
            .reconciliationStatus(reconciliationStatus)
            .selectedFields(Objects.isNull(selectedFields) ? List.of() : BaseSelectFields.fromSelectCodes(
                            Bale.SelectFields.class, selectedFields)
                    .stream().map(Bale.SelectFields::getQueryField).toList()
            )
            .build();

    }

}
