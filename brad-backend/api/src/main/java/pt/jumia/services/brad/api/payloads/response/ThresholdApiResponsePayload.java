package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.reconciliation.Threshold;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ThresholdApiResponsePayload {

    private Long id;
    private CountryApiResponsePayload country;
    private CurrencyApiResponsePayload currency;
    private BigDecimal amount;
    private String createdBy;
    private String updatedBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;

    public ThresholdApiResponsePayload(Threshold threshold) {
        this.id = threshold.getId();
        this.country = new CountryApiResponsePayload(threshold.getCountry());
        this.currency = new CurrencyApiResponsePayload(threshold.getCurrency());
        this.amount = threshold.getAmount();
        this.createdAt = threshold.getCreatedAt();
        this.updatedAt = threshold.getUpdatedAt();
        this.createdBy = threshold.getCreatedBy();
        this.updatedBy = threshold.getUpdatedBy();
    }

    public Threshold toEntity() {
        return Threshold.builder()
            .id(id)
            .country(country.toEntity())
            .currency(currency.toEntity())
            .amount(amount)
            .createdAt(createdAt)
            .createdBy(createdBy)
            .updatedAt(updatedAt)
            .updatedBy(updatedBy)
            .build();
    }
}
