package pt.jumia.services.brad.api.payloads.response.group;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.group.GroupInfo;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class GroupInfoResponsePayload {

    private BigDecimal totalBalance;
    private Long totalQuantity;
    private String status;
    private Boolean isStatusShared;
    private Boolean isReconciliationShared;
    private List<Long> allIds;

    public GroupInfoResponsePayload(GroupInfo groupInfo) {
        this.totalBalance = groupInfo.getTotalBalance();
        this.totalQuantity = groupInfo.getTotalQuantity();
        this.status = groupInfo.getStatus();
        this.isStatusShared = groupInfo.getIsStatusShared();
        this.isReconciliationShared = groupInfo.getIsReconciliationShared();
        this.allIds = groupInfo.getAllIds();
    }
}
