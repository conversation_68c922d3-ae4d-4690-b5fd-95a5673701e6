package pt.jumia.services.brad.api.payloads.request.setting;

import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.Setting;

@Data
@NoArgsConstructor
public class SettingApiRequestPayload {

    @NotEmpty
    private String property;
    @ValidEnumValue(required = true, enumClass = Setting.Type.class)
    private String type;
    private String overrideKey;
    private String description;
    @NotEmpty
    private String value;


    public Setting toEntity() {

        return new Setting().toBuilder()
            .property(this.property.trim())
            .type(Setting.Type.valueOf(this.type))
            .overrideKey(!StringUtils.hasText(overrideKey) ? overrideKey : overrideKey.trim())
            .description(!StringUtils.hasText(description) ? description : description.trim())
            .value(value.trim())
            .build();
    }

    public SettingApiRequestPayload(Setting setting) {

        this.property = setting.getProperty();
        this.type = setting.getType().toString();
        this.overrideKey = setting.getOverrideKey();
        this.description = setting.getDescription();
        this.value = setting.getValue();
    }

}
