package pt.jumia.services.brad.api.payloads.request.country;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Country;

@Data
@NoArgsConstructor
public class CountryApiRequestPayload {


    @NotBlank
    private String name;

    @NotBlank
    private String code;

    @NotBlank
    private String currencyCode;

    public CountryApiRequestPayload(Country country) {
        this.name = country.getName();
        this.code = country.getCode();
        this.currencyCode = country.getCurrency().getCode();
    }

    public Country toEntity() {
        return Country.builder()
            .name(this.name)
            .code(this.code)
            .currency(Currency.builder().code(this.currencyCode).build())
            .build();
    }
}
