package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import net.minidev.json.JSONObject;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;


@Data
@NoArgsConstructor
public class AccountStatementApiResponsePayload {

    @Getter(value = AccessLevel.NONE)
    @Setter(value = AccessLevel.NONE)
    private static final String dateTimeFormat = "yyyy-MM-dd HH:mm:ss";
    private static final String dateFormat = "yyyy-MM-dd";

    Long id;

    String accountID;

    CurrencyApiResponsePayload currency;

    String statementId;

    String previousStatementId;

    @JsonFormat(pattern = dateFormat)
    LocalDate initialDate;

    @JsonFormat(pattern = dateFormat)
    LocalDate finalDate;

    String initialDirection;

    String finalDirection;

    BigDecimal initialAmount;

    BigDecimal finalAmount;

    BigDecimal initialAmountUsd;

    BigDecimal finalAmountUsd;

    CurrencyApiResponsePayload lcy;

    BigDecimal initialAmountLcy;

    BigDecimal finalAmountLcy;

    AccountStatementStatus status;

    String statusDescription;

    String description;

    private String createdBy;

    @JsonFormat(pattern = dateTimeFormat)
    private LocalDateTime createdAt;

    private String updatedBy;

    @JsonFormat(pattern = dateTimeFormat)
    private LocalDateTime updatedAt;

    public AccountStatementApiResponsePayload(final AccountStatement accountStatement) {
        //for pmd reasons
        AccountStatementStatus emptyStatus = null;
        String emptyStatusDescription = null;

        this.id = accountStatement.getId();
        this.currency = new CurrencyApiResponsePayload(accountStatement.getCurrency());
        this.statementId = accountStatement.getStatementId();
        this.previousStatementId = accountStatement.getPreviousStatement() != null ? accountStatement.getPreviousStatement().getStatementId() : null;
        this.initialDate = accountStatement.getInitialDate();
        this.finalDate = accountStatement.getFinalDate();
        this.initialDirection = accountStatement.getInitialDirection().toString();
        this.finalDirection = accountStatement.getFinalDirection().toString();
        this.initialAmount = accountStatement.getInitialAmount();
        this.finalAmount = accountStatement.getFinalAmount();
        this.initialAmountUsd = accountStatement.getAmountInUsd(accountStatement.getInitialAmount());
        this.finalAmountUsd = accountStatement.getAmountInUsd(accountStatement.getFinalAmount());
        this.lcy = new CurrencyApiResponsePayload(accountStatement.getAccount().getCountry().getCurrency());
        this.initialAmountLcy = accountStatement.getAmountInLocalCurrency(accountStatement.getInitialAmount());
        this.finalAmountLcy = accountStatement.getAmountInLocalCurrency(accountStatement.getFinalAmount());
        this.accountID = accountStatement.getAccount().getAccountNumber();
        this.status = Objects.isNull(accountStatement.getStatus()) ? emptyStatus :
                accountStatement.getStatus();
        this.statusDescription = Objects.isNull(accountStatement.getStatusDescription()) ? emptyStatusDescription :
                accountStatement.getStatusDescription().getName();
        this.description = accountStatement.getDescription();
        this.createdAt = accountStatement.getCreatedAt();
        this.createdBy = accountStatement.getCreatedBy();
        this.updatedBy = accountStatement.getUpdatedBy();
        this.updatedAt = accountStatement.getUpdatedAt();
    }

    public AccountStatement toEntity(Account account) {
        //for pmd reasons
        AccountStatementStatus.Description emptyStatusDescription = null;
        return AccountStatement
                .builder()
                .id(id)
                .account(account)
                .currency(currency.toEntity())
                .statementId(statementId)
                .initialDate(initialDate)
                .finalDate(finalDate)
                .initialDirection(Direction.getDirection(initialDirection))
                .finalDirection(Direction.getDirection(finalDirection))
                .initialAmount(initialAmount)
                .finalAmount(finalAmount)
                .status(status)
                .statusDescription(Objects.isNull(statusDescription) ? emptyStatusDescription :
                        AccountStatementStatus.Description.fromString(statusDescription))
                .description(description)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .build();
    }

    public void toJson(JSONObject jsonObject) {
        JSONObject accountStatement = new JSONObject();
        accountStatement.put("id", this.id);
        accountStatement.put("currency", this.currency);
        accountStatement.put("statementId", this.statementId);
        accountStatement.put("previousStatementId", this.previousStatementId);
        accountStatement.put("initialDate", this.initialDate.format(DateTimeFormatter.ofPattern(dateFormat)));
        accountStatement.put("finalDate", this.finalDate.format(DateTimeFormatter.ofPattern(dateFormat)));
        accountStatement.put("initialDirection", this.initialDirection);
        accountStatement.put("finalDirection", this.finalDirection);
        accountStatement.put("initialAmount", this.initialAmount);
        accountStatement.put("finalAmount", this.finalAmount);
        accountStatement.put("initialAmountUsd", this.initialAmountUsd);
        accountStatement.put("finalAmountUsd", this.finalAmountUsd);
        accountStatement.put("lcy", this.lcy);
        accountStatement.put("initialAmountLcy", this.initialAmountLcy);
        accountStatement.put("finalAmountLcy", this.finalAmountLcy);
        accountStatement.put("accountID", this.accountID);
        accountStatement.put("status", this.status);
        accountStatement.put("statusDescription", this.statusDescription);
        accountStatement.put("description", this.description);
        accountStatement.put("createdAt", this.createdAt.format(DateTimeFormatter.ofPattern(dateTimeFormat)));
        accountStatement.put("createdBy", this.createdBy);
        accountStatement.put("updatedBy", this.updatedBy);
        accountStatement.put("updatedAt", this.updatedAt.format(DateTimeFormatter.ofPattern(dateTimeFormat)));

        jsonObject.put("accountStatement", accountStatement);
    }
}
