package pt.jumia.services.brad.api.payloads.request.accountstatement;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.minidev.json.JSONObject;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Objects;

@Data
@NoArgsConstructor
public class AccountStatementApiRequestPayload {

    @JsonProperty("account_id")
    @NotBlank
    String accountID;

    @JsonProperty("currency")
    @NotBlank
    String currencyCode;

    @JsonProperty("statement_id")
    @NotBlank
    String statementId;

    @JsonProperty("in_date")
    @NotNull
    String initialDate;

    @JsonProperty("fi_date")
    @NotNull
    String finalDate;

    @JsonProperty("in_direction")
    Integer initialDirection;

    @JsonProperty("fi_direction")
    Integer finalDirection;

    @JsonProperty("in_amount")
    @NotNull
    BigDecimal initialAmount;

    @JsonProperty("fi_amount")
    @NotNull
    BigDecimal finalAmount;

    String description;

    public AccountStatement toEntity() throws ParseException {

        return AccountStatement.builder()
            .currency(Currency.builder()
                    .code(this.currencyCode)
                    .build())
            .statementId(this.statementId)
            .initialDate(DateParser.parseToLocalDate(this.initialDate))
            .finalDate(DateParser.parseToLocalDate(this.finalDate))
            .initialDirection(Objects.nonNull(this.initialDirection) ?
                                      Direction.getDirection(String.valueOf(this.initialDirection)) :
                                      getDirection(this.initialAmount))
            .finalDirection(Objects.nonNull(this.finalDirection) ?
                                    Direction.getDirection(String.valueOf(this.finalDirection)) :
                                    getDirection(this.finalAmount))
            .initialAmount(Objects.nonNull(this.initialDirection) ?
                                   this.initialAmount :
                                   this.initialAmount.abs())
            .finalAmount(Objects.nonNull(this.finalDirection) ?
                                 this.finalAmount :
                                 this.finalAmount.abs())
            .status(AccountStatementStatus.OPEN)
            .description(this.description)
            .build();
    }

    public void toJson(JSONObject jsonObject) {
        jsonObject.put("accountStatement", this);
    }

    private Direction getDirection(final BigDecimal amount) {

        return amount.compareTo(BigDecimal.ZERO) < 0 ? Direction.DEBIT : Direction.CREDIT;
    }
}
