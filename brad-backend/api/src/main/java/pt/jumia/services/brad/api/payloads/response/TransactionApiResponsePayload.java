package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

@Data
@NoArgsConstructor
public class TransactionApiResponsePayload {

    @Getter(value = AccessLevel.NONE)
    @Setter(value = AccessLevel.NONE)
    private final String dateTimeFormat = "yyyy-MM-dd HH:mm:ss";
    private final String dateFormat = "yyyy-MM-dd";

    Long id;
    String type;
    CurrencyApiResponsePayload currency;
    @JsonFormat(pattern = dateFormat)
    LocalDate valueDate;
    @JsonFormat(pattern = dateFormat)
    LocalDate transactionDate;
    @JsonFormat(pattern = dateFormat)
    LocalDate statementDate;
    Direction direction;
    BigDecimal amount;
    CurrencyApiResponsePayload localCurrency;
    BigDecimal amountLocalCurrency;
    BigDecimal amountUsd;
    String reference;
    String description;
    String remittanceInformation;
    String orderingPartyName;
    String accountStatementID;
    String reconcileStatus;
    @JsonFormat(pattern = dateTimeFormat)
    LocalDateTime createdAt;
    String createdBy;
    @JsonFormat(pattern = dateTimeFormat)
    LocalDateTime updatedAt;
    String updatedBy;
    boolean statementIsInError;

    //reconciliation related fields
    private Integer reconciliationId;
    private String reconciliationCreator;
    @JsonFormat(pattern = dateTimeFormat)
    private LocalDateTime reconciliationCreationDate;
    private String reconciliationReviewer;
    @JsonFormat(pattern = dateTimeFormat)
    private LocalDateTime reconciliationReviewDate;
    private String reconciliationStatus;


    public TransactionApiResponsePayload(final Transaction transaction) {

        this.id = transaction.getId();
        this.type = transaction.getType();
        if (Objects.nonNull(transaction.getCurrency())) {
            this.currency = new CurrencyApiResponsePayload(transaction.getCurrency());
        }
        this.valueDate = transaction.getValueDate();
        this.transactionDate = transaction.getTransactionDate();
        this.statementDate = transaction.getStatementDate();
        this.direction = transaction.getDirection();
        this.amount = transaction.getAmount();
        if (Objects.nonNull(transaction.getAccountStatement())) {
            this.localCurrency = new CurrencyApiResponsePayload(transaction.getAccountStatement()
                    .getAccount().getCountry().getCurrency());
        }
        this.amountLocalCurrency = transaction.getAmountLocalCurrency();
        this.amountUsd = transaction.getAmountUsd();
        this.reference = transaction.getReference();
        this.description = transaction.getDescription();
        this.remittanceInformation = transaction.getRemittanceInformation();
        this.orderingPartyName = transaction.getOrderingPartyName();
        if (Objects.nonNull(transaction.getAccountStatement())) {
            this.accountStatementID = transaction.getAccountStatement().getId().toString();
        }
        this.createdAt = transaction.getCreatedAt();
        this.createdBy = transaction.getCreatedBy();
        this.updatedBy = transaction.getUpdatedBy();
        this.updatedAt = transaction.getUpdatedAt();
        this.statementIsInError = transaction.isStatementIsInError();
        if (Objects.nonNull(transaction.getReconcileStatus())) {
            this.reconcileStatus = transaction.getReconcileStatus().name();
        }
        if (Objects.nonNull(transaction.getReconciliation())) {
            this.reconciliationId = transaction.getReconciliation().getId();
            this.reconciliationCreator = transaction.getReconciliation().getCreator();
            this.reconciliationCreationDate = transaction.getReconciliation().getCreationDate();
            this.reconciliationReviewer = transaction.getReconciliation().getReviewer();
            this.reconciliationReviewDate = transaction.getReconciliation().getReviewDate();
            this.reconciliationStatus = transaction.getReconciliation().getStatus().name();
        }
    }

    public Transaction toEntity(AccountStatement accountStatement) {
        return Transaction
                .builder()
                .id(id)
                .type(type)
                .currency(currency.toEntity())
                .valueDate(valueDate)
                .transactionDate(transactionDate)
                .statementDate(statementDate)
                .direction(direction)
                .amount(amount)
                .reference(reference)
                .description(description)
                .remittanceInformation(remittanceInformation)
                .orderingPartyName(orderingPartyName)
                .accountStatement(accountStatement)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .statementIsInError(statementIsInError)
                .reconcileStatus(ReconcileStatus.valueOf(reconcileStatus))
                .build();
    }

}
