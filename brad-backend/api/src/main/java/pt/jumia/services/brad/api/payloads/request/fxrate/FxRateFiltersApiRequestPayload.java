package pt.jumia.services.brad.api.payloads.request.fxrate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.filter.fxrate.FxRateFilters;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class FxRateFiltersApiRequestPayload {

        private List<String> baseCurrency;
        private List<String> quoteCurrency;
        private LocalDate rateDate;
        private BigDecimal bid;
        private LocalDateTime bisLoadedAt;
        private Integer skAudInsert;
        private Integer skAudUpdate;
        private LocalDateTime timestampLastUpdate;

        private List<String> selectedFields;

        public FxRateFilters toEntity() {
            return FxRateFilters.builder()
                    .baseCurrency(baseCurrency)
                    .quoteCurrency(quoteCurrency)
                    .rateDate(rateDate)
                    .bid(bid)
                    .bisLoadedAt(bisLoadedAt)
                    .skAudInsert(skAudInsert)
                    .skAudUpdate(skAudUpdate)
                    .timestampLastUpdate(timestampLastUpdate)
                    .selectedFields(Objects.isNull(selectedFields) ? List.of() : BaseSelectFields.fromSelectCodes(
                                    FxRate.SelectFields.class, selectedFields)
                            .stream().map(FxRate.SelectFields::getQueryField).toList()
                    )
                    .build();
        }
}
