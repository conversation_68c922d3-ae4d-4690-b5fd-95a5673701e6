package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.ViewEntity;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ViewEntityApiResponsePayload {

    private Long id;
    private String viewType;
    private String driver;
    private String jdbcConnectionUrl;
    private String databaseName;
    private String schemaName;
    private String viewName;
    private String createdBy;
    private String updatedBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;

    public ViewEntityApiResponsePayload(ViewEntity viewEntity) {
        this.id = viewEntity.getId();
        this.viewType = viewEntity.getEntityType().name();
        this.driver = viewEntity.getDriver();
        this.jdbcConnectionUrl = viewEntity.getJdbcConnectionUrl();
        this.databaseName = viewEntity.getDatabaseName();
        this.schemaName = viewEntity.getSchemaName();
        this.viewName = viewEntity.getViewName();
        this.createdAt = viewEntity.getCreatedAt();
        this.updatedAt = viewEntity.getUpdatedAt();
        this.createdBy = viewEntity.getCreatedBy();
        this.updatedBy = viewEntity.getUpdatedBy();
    }

    public ViewEntity toEntity() {
        return ViewEntity.builder()
            .id(id)
            .entityType(ViewEntity.EntityType.valueOf(viewType))
            .driver(driver)
            .jdbcConnectionUrl(jdbcConnectionUrl)
            .databaseName(databaseName)
            .schemaName(schemaName)
            .viewName(viewName)
            .createdAt(createdAt)
            .createdBy(createdBy)
            .updatedAt(updatedAt)
            .updatedBy(updatedBy)
            .build();
    }
}
