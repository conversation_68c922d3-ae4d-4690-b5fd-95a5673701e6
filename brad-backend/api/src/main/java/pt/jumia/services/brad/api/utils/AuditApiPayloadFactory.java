package pt.jumia.services.brad.api.utils;

import pt.jumia.services.brad.api.payloads.response.AccountApiResponsePayload;
import pt.jumia.services.brad.domain.entities.AuditedEntity;
import pt.jumia.services.brad.domain.entities.account.Account;

public class AuditApiPayloadFactory {

    public static Object createAuditPayload(AuditedEntity auditedEntity) {
        if (auditedEntity.getEntity() instanceof Account) {
            return new AccountApiResponsePayload((Account) auditedEntity.getEntity());
        } else {
            throw new IllegalArgumentException("The given entity is not supported");
        }
    }
}
