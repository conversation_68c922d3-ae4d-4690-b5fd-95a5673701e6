package pt.jumia.services.brad.api.controllers;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.ResponseStatus;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.accountstatementfile.AccountStatementFileFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.accountstatementfile.AccountStatementFileSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.AccountStatementFileApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.ReprocessFilesUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.DownloadAccountStatementFileUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.ReadAccountStatementFileUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.ScanS3BucketForStatementFileUseCase;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/account-statement-files")
public class AccountStatementFileController {

    private final ReadAccountStatementFileUseCase readAccountStatementFileUseCase;
    private final ScanS3BucketForStatementFileUseCase scanS3BucketForStatementFileUseCase;
    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final DownloadAccountStatementFileUseCase downloadExportFromFileStorageUseCase;
    private final ReprocessFilesUseCase reprocessFilesUseCase;

    @GetMapping
    public PageApiResponsePayload<AccountStatementFileApiResponsePayload>
    fetch(HttpServletRequest httpServletRequest,
          @Valid AccountStatementFileFiltersApiRequestPayload accountStatementFileFiltersApiRequestPayload,
          @Valid AccountStatementFileSortFiltersApiRequestPayload accountStatementFileSortFiltersApiRequestPayload,
          @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload) throws UserForbiddenException, EntityErrorsException {

        validateUserAccessUseCase.checkCanAccessStatementFiles(RequestContext.getUser());

        log.info("Fetching all account statement files for user with identifier {}", RequestContext.getUsername());
        AccountStatementFileFilters accountStatementFileFilters = accountStatementFileFiltersApiRequestPayload.toEntity();
        AccountStatementFileSortFilters accountStatementFileSortFilters = accountStatementFileSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();

        List<AccountStatementFileApiResponsePayload> accountStatementFiles = readAccountStatementFileUseCase.execute(accountStatementFileFilters,
                accountStatementFileSortFilters, pageFilters)
                .stream().map(AccountStatementFileApiResponsePayload::new)
                .collect(Collectors.toList());

        long total = readAccountStatementFileUseCase.executeCount(accountStatementFileFilters);

        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                accountStatementFiles,
                total
        );
    }

    @GetMapping(value = "/{id}")
    public AccountStatementFileApiResponsePayload fetchById(@PathVariable(value = "id") long id)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessStatementFiles(RequestContext.getUser());

        log.info("Fetching account statement file {} for user with identifier {}", id, RequestContext.getUsername());

        return new AccountStatementFileApiResponsePayload(readAccountStatementFileUseCase.execute(id));

    }

    @PatchMapping(value = "/scan")
    public void scan() throws UserForbiddenException, SchedulerException {

        validateUserAccessUseCase.checkCanScanSftpFolder(RequestContext.getUser());

        log.info("Scanning for account statement files initiated by {}", RequestContext.getUsername());

        scanS3BucketForStatementFileUseCase.start();
    }

    @GetMapping(value = "/{id}/download", produces = MediaType.TEXT_PLAIN_VALUE)
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<String> downloadAsCsv(@PathVariable(value = "id") Long id)
            throws UserForbiddenException, NotFoundException {

        validateUserAccessUseCase.checkCanAccessStatementFiles(RequestContext.getUser());
        validateUserAccessUseCase.checkCanDownloadStatementFiles(RequestContext.getUser());

        log.info("'{}' downloading account statement file {}.",
                RequestContext.getUsername(), id);

        String downloadUrl = downloadExportFromFileStorageUseCase.executeGetTempDownloadUrl(id);
        return ResponseEntity.ok()
                .contentType(MediaType.TEXT_PLAIN)
                .body(downloadUrl);
    }

    @PatchMapping(value = "/reprocess")
    public void reprocess(
            @Valid AccountStatementFileFiltersApiRequestPayload filtersApiRequestPayload)
            throws EntityErrorsException, IOException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessStatementFiles(RequestContext.getUser());
        validateUserAccessUseCase.checkCanScanSftpFolder(RequestContext.getUser());

        log.info("Reprocessing statement files initiated by {}", RequestContext.getUsername());

        reprocessFilesUseCase.execute(filtersApiRequestPayload.toEntity());
    }

}
