package pt.jumia.services.brad.api.payloads.request.transaction;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class TransactionFilterListApiRequestPayload {

    List<TransactionFiltersApiRequestPayload> filterList;

    public List<TransactionFilters> toEntities() throws ParseException {
        List<TransactionFilters> transactionFilters = new ArrayList<>();
        for (TransactionFiltersApiRequestPayload filter : filterList) {
            transactionFilters.add(filter.toEntity());
        }
        return transactionFilters;
    }
}
