package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.ApiLog;

import java.time.LocalDateTime;
import java.util.Objects;

@Data
@NoArgsConstructor
public class ApiLogApiResponsePayload {

    private Long id;
    private String logType;
    private String request;
    private String response;
    private String relatedEntityId;
    private String logStatus;
    private String createdBy;
    private String updatedBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;
    private String partitionKey;

    public ApiLogApiResponsePayload(ApiLog apiLog) {
        this.id = apiLog.getId();
        this.logType = Objects.isNull(apiLog.getLogType()) ? null : apiLog.getLogType().name();
        this.request = apiLog.getRequest();
        this.response = apiLog.getResponse();
        this.logStatus = Objects.isNull(apiLog.getLogStatus()) ? null : apiLog.getLogStatus().name();
        this.relatedEntityId = apiLog.getRelatedEntityId();
        this.createdAt = apiLog.getCreatedAt();
        this.updatedAt = apiLog.getUpdatedAt();
        this.createdBy = apiLog.getCreatedBy();
        this.updatedBy = apiLog.getUpdatedBy();
        this.partitionKey = apiLog.getPartitionKey();

    }
}
