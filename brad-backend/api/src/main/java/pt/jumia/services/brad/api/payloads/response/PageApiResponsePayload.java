package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.util.UriComponentsBuilder;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;

import java.util.List;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PageApiResponsePayload<T> {

    private static final int FIRST_PAGE_IDX = 1;

    private Links links;
    private int page;
    private long total;
    private int size;
    private List<T> results;

    public static <E> PageApiResponsePayload<E> buildPageResponsePayload(
            HttpServletRequest request,
            PageFilters pageFilters,
            List<E> results,
            long total) {

        boolean hasNextPage = results.size() > pageFilters.getSize();

        String baseUrl = getBaseUrl(request);
        String endpoint = getEndpoint(request);

        String previousPage = null;
        if (pageFilters.getPage() > FIRST_PAGE_IDX) {
            previousPage = buildPreviousPage(pageFilters, endpoint);
        }

        String nextPage = null;
        if (hasNextPage) {
            nextPage = buildNextPage(pageFilters, endpoint);
            // remove element added as a workaround to see if we have a next page
            results.remove(results.size() - 1);
        }

        // remove element added as a workaround to see if we have a next page
        if (results.size() == total && total > pageFilters.getSize()) {
            results.remove(results.size() - 1);
        }

        return PageApiResponsePayload.<E>builder()
                .links(new Links(baseUrl, previousPage, nextPage))
                .page(pageFilters.getPage())
                .size(pageFilters.getSize())
                .total(total)
                .results(results)
                .build();
    }

    private static String getBaseUrl(HttpServletRequest request) {
        return request.getRequestURL().substring(
                0, request.getRequestURL().length() - request.getRequestURI().length()) + request.getContextPath();
    }

    private static String getEndpoint(HttpServletRequest request) {
        return request.getQueryString() == null ? request.getRequestURI() :
                request.getRequestURI() + "?" + request.getQueryString();
    }

    private static String buildPreviousPage(PageFilters pageFilters, String endpoint) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromUriString(endpoint);
        urlBuilder.replaceQueryParam("page", pageFilters.getPage() - 1);
        return urlBuilder.build().toUriString();
    }

    private static String buildNextPage(PageFilters pageFilters, String endpoint) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromUriString(endpoint);
        urlBuilder.replaceQueryParam("page", pageFilters.getPage() + 1);
        return urlBuilder.build().toUriString();
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Links {
        private String base;
        private String previousPage;
        private String nextPage;
    }

}
