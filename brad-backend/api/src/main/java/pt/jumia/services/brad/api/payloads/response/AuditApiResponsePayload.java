package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.utils.AuditApiPayloadFactory;
import pt.jumia.services.brad.domain.entities.AuditedEntity;
import pt.jumia.services.brad.domain.enumerations.AuditedEntities;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuditApiResponsePayload {

    private String type;
    private Object entity;
    private RevisionApiResponsePayload revision;
    private String operation;

    public AuditApiResponsePayload(AuditedEntity auditedEntity) {
        this.type = auditedEntity.getAuditedEntity().name();
        this.entity = AuditApiPayloadFactory.createAuditPayload(auditedEntity);
        this.revision = new RevisionApiResponsePayload(auditedEntity.getRevisionInfo());
        this.operation = auditedEntity.getOperationType().name();
    }

    public AuditedEntity toEntity() {
        return AuditedEntity.builder()
                .auditedEntity(AuditedEntities.valueOf(type))
                .entity(entity)
                .revisionInfo(revision.toEntity())
                .operationType(AuditedEntity.OperationType.valueOf(operation))
                .build();
    }
}
