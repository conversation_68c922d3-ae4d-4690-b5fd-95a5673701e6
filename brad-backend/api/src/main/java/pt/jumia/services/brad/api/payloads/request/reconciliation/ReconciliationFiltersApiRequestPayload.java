package pt.jumia.services.brad.api.payloads.request.reconciliation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import pt.jumia.services.brad.domain.entities.filter.reconciliation.ReconciliationFilters;
import pt.jumia.services.brad.domain.enumerations.ReconciliationStatus;

import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.List;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ReconciliationFiltersApiRequestPayload {

    private static final String DATE_FORMAT = "yyyy-MM-dd";

    private Integer id;
    private String status;
    private String creator;
    @DateTimeFormat(pattern = DATE_FORMAT)
    private LocalDate creationDateStart;
    @DateTimeFormat(pattern = DATE_FORMAT)
    private LocalDate creationDateEnd;
    private String reviewer;
    @DateTimeFormat(pattern = DATE_FORMAT)
    private LocalDate reviewDateStart;
    @DateTimeFormat(pattern = DATE_FORMAT)
    private LocalDate reviewDateEnd;
    private BigDecimal amountTransaction;
    private BigDecimal amountBale;
    private BigDecimal amountThreshold;
    private List<Long> baleIds;
    private List<Long> transactionIds;

    public ReconciliationFilters toEntity() throws ParseException {
        return ReconciliationFilters.builder()
                .id(id)
                .status(StringUtils.isEmpty(status) ? null : ReconciliationStatus.valueOf(status))
                .creator(creator)
                .creationDateStart(creationDateStart)
                .creationDateEnd(creationDateEnd)
                .reviewer(reviewer)
                .reviewDateStart(reviewDateStart)
                .reviewDateEnd(reviewDateEnd)
                .amountTransaction(amountTransaction)
                .amountBale(amountBale)
                .amountThreshold(amountThreshold)
                .baleIdsList(baleIds)
                .build();
    }

}
