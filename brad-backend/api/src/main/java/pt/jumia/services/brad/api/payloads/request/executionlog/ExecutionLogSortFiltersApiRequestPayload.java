package pt.jumia.services.brad.api.payloads.request.executionlog;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.filter.executionlog.ExecutionLogSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExecutionLogSortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = ExecutionLog.SortingFields.class)
    private String orderField = ExecutionLog.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public ExecutionLogSortFilters toEntity() {
        return ExecutionLogSortFilters.builder()
                .field(ExecutionLog.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }
}
