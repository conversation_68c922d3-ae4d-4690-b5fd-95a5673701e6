package pt.jumia.services.brad.api.payloads.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PageFiltersApiRequestPayload {
    @Builder.Default
    @Min(1L)
    @NotNull
    private Integer page = 1;

    @Builder.Default
    @Min(1L)
    @Max(1000L)
    @NotNull
    private Integer size = 10;

    public PageFiltersApiRequestPayload(PageFilters entity) {
        this.page = entity.getPage();
        this.size = entity.getSize();
    }

    public PageFilters toEntity() {
        return PageFilters.builder()
                .page(page)
                .size(size)
                .build();
    }

}
