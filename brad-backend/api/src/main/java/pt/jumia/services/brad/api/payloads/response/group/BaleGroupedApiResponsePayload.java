package pt.jumia.services.brad.api.payloads.response.group;

import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.payloads.response.BaleApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class BaleGroupedApiResponsePayload {

    private PageApiResponsePayload<BaleApiResponsePayload> pagedEntityList;
    private List<Long> allIds;

    public BaleGroupedApiResponsePayload(
            HttpServletRequest httpServletRequest,
            PageFilters pageFilters,
            List<Bale> bales,
            List<Long> allIds,
            Integer totalQuantity
    ) {
        this.pagedEntityList = PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                bales.stream().map(BaleApiResponsePayload::new)
                        .collect(Collectors.toList()),
                totalQuantity);
        this.allIds = new ArrayList<>(allIds);
    }

}
