package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.enumerations.ReconciliationStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
public class ReconciliationApiResponsePayload {

    private Integer id;
    private AccountApiResponsePayload account;
    private String status;
    private String creator;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime creationDate;
    private String reviewer;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime reviewDate;
    private BigDecimal amountTransaction;
    private BigDecimal amountBale;
    private BigDecimal amountThreshold;

    private List<Long> baleIds;
    private List<Long> transactionIds;

    @SuppressWarnings("PMD.NullAssignment")
    public ReconciliationApiResponsePayload(Reconciliation reconciliation) {
        this.id = reconciliation.getId();
        this.account = Objects.isNull(reconciliation.getAccount()) ? null :
                new AccountApiResponsePayload(reconciliation.getAccount());
        this.status = reconciliation.getStatus().toString();
        this.amountTransaction = reconciliation.getAmountTransaction();
        this.amountBale = reconciliation.getAmountBale();
        this.amountThreshold = reconciliation.getAmountThreshold();
        this.creator = reconciliation.getCreator();
        this.creationDate = reconciliation.getCreationDate();
        this.reviewer = reconciliation.getReviewer();
        this.reviewDate = reconciliation.getReviewDate();
        this.baleIds = reconciliation.getBaleIds();
        this.transactionIds = reconciliation.getTransactionIds();
    }

    @SuppressWarnings("PMD.NullAssignment")
    public Reconciliation toEntity() {
        return Reconciliation.builder()
                .id(id)
                .account(Objects.isNull(account) ? null : account.toEntity())
                .status(ReconciliationStatus.fromString(status))
                .creator(creator)
                .creationDate(creationDate)
                .reviewer(reviewer)
                .reviewDate(reviewDate)
                .amountTransaction(amountTransaction)
                .amountBale(amountBale)
                .amountThreshold(amountThreshold)
                .baleIds(baleIds)
                .transactionIds(transactionIds)
                .build();
    }

}
