package pt.jumia.services.brad.api.payloads.request.document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.entities.filter.document.DocumentFilters;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

import java.util.List;
import java.util.Objects;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class DocumentFiltersApiRequestPayload {

    private List<String> types;

    private String name;

    private String description;

    private Long accountID;

    private List<String> selectedFields;

    public DocumentFilters toEntity() {
        return DocumentFilters
            .builder()
            .types(types)
            .name(name)
            .description(description)
            .accountId(accountID)
            .selectedFields(Objects.isNull(selectedFields) ? List.of() : BaseSelectFields.fromSelectCodes(
                            Document.SelectFields.class, selectedFields)
                    .stream().map(Document.SelectFields::getQueryField).toList()
            )
            .build();

    }

}
