package pt.jumia.services.brad.api.payloads.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.AuditedEntity;
import pt.jumia.services.brad.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuditedEntitySortFiltersApiRequestPayload {

    @ValidEnumValue(required = false, enumClass = AuditedEntity.SortingFields.class)
    private String orderField = AuditedEntity.SortingFields.REV.name();

    @ValidEnumValue(required = false, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public AuditedEntitySortFilters toEntity() {
        return AuditedEntitySortFilters.builder()
                .field(AuditedEntity.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }
}
