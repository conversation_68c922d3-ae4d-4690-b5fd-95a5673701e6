package pt.jumia.services.brad.api.payloads.request.accountstatementfile;

import java.time.LocalDateTime;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileFilters;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class AccountStatementFileFiltersApiRequestPayload {

    private List<Long> ids;

    private String name;

    private String processingStatus;

    private Long accountId;

    private Long statementId;

    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAtFrom;

    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAtTo;

    public AccountStatementFileFilters toEntity() {

        return AccountStatementFileFilters
            .builder()
            .ids(this.ids)
            .name(this.name)
            .processingStatus(processingStatus == null ? null : ProcessingStatus.valueOf(this.processingStatus))
            .accountId(this.accountId)
            .statementId(this.statementId)
            .createdAtFrom(this.createdAtFrom)
            .createdAtTo(this.createdAtTo)
            .build();

    }

}
