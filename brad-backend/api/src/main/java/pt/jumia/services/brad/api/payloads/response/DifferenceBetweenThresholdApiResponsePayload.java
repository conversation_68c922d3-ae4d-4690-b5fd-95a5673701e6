package pt.jumia.services.brad.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.reconciliation.DifferenceBetweenThreshold;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class DifferenceBetweenThresholdApiResponsePayload {

    private BigDecimal transactionAmount;
    private BigDecimal baleAmount;
    private BigDecimal amountDifference;
    private BigDecimal thresholdAmount;
    private Boolean isBetweenThreshold;

    public DifferenceBetweenThresholdApiResponsePayload(DifferenceBetweenThreshold differenceBetweenThreshold) {
        this.transactionAmount = differenceBetweenThreshold.getTransactionAmount();
        this.baleAmount = differenceBetweenThreshold.getBaleAmount();
        this.amountDifference = differenceBetweenThreshold.getAmountDifference();
        this.thresholdAmount = differenceBetweenThreshold.getThresholdAmount();
        this.isBetweenThreshold = differenceBetweenThreshold.getIsBetweenThreshold();
    }

    public DifferenceBetweenThreshold toEntity() {
        return DifferenceBetweenThreshold.builder()
            .amountDifference(amountDifference)
            .thresholdAmount(thresholdAmount)
            .isBetweenThreshold(isBetweenThreshold)
            .build();
    }
}
