package pt.jumia.services.brad.api.payloads.request.accountstatement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class AccountStatementFiltersApiRequestPayload {
    private String filterText;
    private List<String> currencyCodes;
    private String statementId;
    private String initialDateStart;
    private String initialDateEnd;
    private String finalDateStart;
    private String finalDateEnd;
    private List<String> initialDirection;
    private List<String> finalDirection;
    private BigDecimal initialAmount;
    private BigDecimal finalAmount;
    private List<String> status;
    private List<String> statusDescription;
    private Long accountID;
    private String partitionKey;
    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAtStart;
    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAtEnd;
    private List<String> selectedFields;

    public AccountStatementFilters toEntity() throws ParseException {

        return AccountStatementFilters
            .builder()
            .filterText(filterText)
            .currencyCodes(currencyCodes)
            .statementId(statementId)
            .initialDateStart(DateParser.parseToLocalDate(initialDateStart))
            .initialDateEnd(DateParser.parseToLocalDate(initialDateEnd))
            .finalDateStart(DateParser.parseToLocalDate(finalDateStart))
            .finalDateEnd(DateParser.parseToLocalDate(finalDateEnd))
            .initialDirection(initialDirection)
            .finalDirection(finalDirection)
            .initialAmount(initialAmount)
            .finalAmount(finalAmount)
            .status(Objects.isNull(status) ? null :
                    status.stream().map(AccountStatementStatus::fromString).toList())
            .statusDescription(Objects.isNull(statusDescription) ? null :
                    statusDescription.stream().map(AccountStatementStatus.Description::fromString).toList())
            .accountID(accountID)
            .partitionKey(partitionKey)
            .createdAtStart(createdAtStart)
            .createdAtEnd(createdAtEnd)
            .selectedFields(Objects.isNull(selectedFields) ? List.of() : BaseSelectFields.fromSelectCodes(
                            Account.SelectFields.class, selectedFields)
                    .stream().map(Account.SelectFields::getQueryField).toList()
            )
            .build();

    }

}
