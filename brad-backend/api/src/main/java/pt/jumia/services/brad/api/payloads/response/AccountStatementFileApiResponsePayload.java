package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;

@Data
@NoArgsConstructor
public class AccountStatementFileApiResponsePayload {

    private Long id;
    private String name;
    private String url;
    private String processingStatus;
    private String statusDescription;
    private Long executionLog;
    private AccountStatement statement;
    private String createdBy;
    private String updatedBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;

    public AccountStatementFileApiResponsePayload(AccountStatementFile accountStatementFile) {

        this.id = accountStatementFile.getId();
        this.name = accountStatementFile.getName();
        this.url = accountStatementFile.getUrl();
        this.processingStatus = accountStatementFile.getProcessingStatus().name();
        this.statusDescription = accountStatementFile.getStatusDescription();
        this.executionLog = accountStatementFile.getExecutionLog().getId();
        this.statement = accountStatementFile.getStatement() == null ? null : accountStatementFile.getStatement();
        this.createdAt = accountStatementFile.getCreatedAt();
        this.updatedAt = accountStatementFile.getUpdatedAt();
        this.createdBy = accountStatementFile.getCreatedBy();
        this.updatedBy = accountStatementFile.getUpdatedBy();

    }

}
