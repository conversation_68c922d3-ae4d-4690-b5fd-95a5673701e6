package pt.jumia.services.brad.api.payloads.response.error;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Copyright (c) 2016, 2017, Jumia.
 */
@Data
@NoArgsConstructor
public class ErrorResponsePayload {

    private String object;
    private List<ErrorFieldPayload> errorFields;
    private String globalError;

    private ErrorResponsePayload(String object, List<ErrorFieldPayload> errorFields, String globalError) {
        this.object = object;
        this.errorFields = errorFields;
        this.globalError = globalError;
    }

    public static ErrorResponsePayload createWithFields(String object, List<ErrorFieldPayload> errorFields) {
        return new ErrorResponsePayload(object, errorFields, null);
    }

    public static ErrorResponsePayload createGlobal(String object, String globalError){
        return new ErrorResponsePayload(object, null, globalError);
    }

    public static ErrorResponsePayload createGlobal(String globalError){
        return new ErrorResponsePayload(null, null, globalError);
    }

}
