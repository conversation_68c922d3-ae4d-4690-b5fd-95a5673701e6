package pt.jumia.services.brad.api.payloads.request.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.entities.filter.document.DocumentSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DocumentSortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = Document.SortingFields.class)
    private String orderField = Document.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public DocumentSortFilters toEntity() {
        return DocumentSortFilters.builder()
                .field(Document.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }
}
