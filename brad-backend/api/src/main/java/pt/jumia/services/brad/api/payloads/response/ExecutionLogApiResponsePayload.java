package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.ExecutionLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
public class ExecutionLogApiResponsePayload {

    private Long id;
    private String logType;
    private Integer recordsAmount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime executionStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime executionEndTime;
    private String appliedFilters;
    private String query;
    private String logStatus;
    private List<ExecutionLog.SyncingError> errors;


    public ExecutionLogApiResponsePayload(ExecutionLog executionLog) {
        //pmd reasons
        String nullLog = null;
        this.id = executionLog.getId();
        this.logType = Objects.isNull(executionLog.getLogType()) ? nullLog : executionLog.getLogType().name();
        this.recordsAmount = executionLog.getRecordsAmount();
        this.executionStartTime = executionLog.getExecutionStartTime();
        this.executionEndTime = executionLog.getExecutionEndTime();
        this.appliedFilters = executionLog.getAppliedFilters();
        this.query = executionLog.getQuery();
        this.logStatus = Objects.isNull(executionLog.getLogStatus()) ? nullLog : executionLog.getLogStatus().name();
        this.errors = executionLog.getErrors();
    }

    public ExecutionLog toEntity() {
        //pmd reasons
        ExecutionLog.ExecutionLogType nullLogType = null;
        ExecutionLog.ExecutionLogStatus nullLogStatus = null;
        return ExecutionLog
                .builder()
                .id(this.id)
                .logType(Objects.isNull(this.logType) ? nullLogType : ExecutionLog.ExecutionLogType.valueOf(this.logType))
                .recordsAmount(this.recordsAmount)
                .executionStartTime(this.executionStartTime)
                .executionEndTime(this.executionEndTime)
                .appliedFilters(this.appliedFilters)
                .query(this.query)
                .logStatus(Objects.isNull(this.logStatus) ? nullLogStatus : ExecutionLog.ExecutionLogStatus.valueOf(this.logStatus))
                .errors(this.errors)
                .build();
    }

}
