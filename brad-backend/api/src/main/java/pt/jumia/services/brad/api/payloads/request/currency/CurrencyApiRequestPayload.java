package pt.jumia.services.brad.api.payloads.request.currency;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.Currency;

@Data
@NoArgsConstructor
public class CurrencyApiRequestPayload {

    private Long id;

    @NotBlank
    private String name;

    @NotBlank
    private String code;

    @NotBlank
    private String symbol;

    public CurrencyApiRequestPayload(Currency currency) {
        this.id = currency.getId();
        this.name = currency.getName();
        this.code = currency.getCode();
        this.symbol = currency.getSymbol();
    }

    public Currency toEntity() {
        return Currency.builder()
            .id(this.id)
            .name(this.name)
            .code(this.code)
            .symbol(this.symbol)
            .build();
    }
}
