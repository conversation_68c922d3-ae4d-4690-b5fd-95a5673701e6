package pt.jumia.services.brad.api.csvs.exports;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import org.springframework.http.ResponseEntity;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.api.validations.annotations.CsvHeaderName;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.enumerations.Direction;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static pt.jumia.services.brad.api.csvs.CsvBuilder.buildCsv;

@Builder(toBuilder = true)
public class TransactionExportCSV {

    @CsvHeaderName("ID")
    public Long id;
    @CsvHeaderName("Type")
    public String type;
    @CsvHeaderName("Currency")
    public String currencyCode;
    @CsvHeaderName("Value Date")
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    public LocalDate valueDate;
    @CsvHeaderName("Transaction Date")
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    public LocalDate transactionDate;
    @CsvHeaderName("Statement Date")
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    public LocalDate statementDate;
    @CsvHeaderName("Direction")
    public Direction direction;
    @CsvHeaderName("Amount")
    public BigDecimal amount;
    @CsvHeaderName("Amount (LCY)")
    public BigDecimal amountLocalCurrency;
    @CsvHeaderName("Amount (USD)")
    public BigDecimal amountUsd;
    @CsvHeaderName("Rate")
    public BigDecimal rate;
    @CsvHeaderName("Reference")
    public String reference;
    @CsvHeaderName("Description")
    public String description;
    @CsvHeaderName("Statement Number")
    public String accountStatementID;
    @CsvHeaderName("Created By")
    public String createdBy;
    @CsvHeaderName("Created At")
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    public LocalDateTime createdAt;
    @CsvHeaderName("Updated By")
    public String updatedBy;
    @CsvHeaderName("Updated At")
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    public LocalDateTime updatedAt;
    @CsvHeaderName("Status")
    public String status;
    @CsvHeaderName("Remittance Information")
    public String remittanceInformation;
    @CsvHeaderName("Ordering Party Name")
    public String orderingPartyName;


    public static ResponseEntity<byte[]> buildTransactionCSV(List<Transaction> transactions) {
        List<TransactionExportCSV> transactionCSVs = transactions.stream()
                .map((transaction) -> TransactionExportCSV.builder()
                        .id(transaction.getId())
                        .type(transaction.getType())
                        .currencyCode(transaction.getCurrency().getCode())
                        .valueDate(transaction.getValueDate())
                        .transactionDate(transaction.getTransactionDate())
                        .statementDate(transaction.getStatementDate())
                        .direction(transaction.getDirection())
                        .amount(transaction.getAmount())
                        .amountLocalCurrency(transaction.getAmountLocalCurrency())
                        .amountUsd(transaction.getAmountUsd())
                        .reference(transaction.getReference())
                        .description(transaction.getDescription())
                        .accountStatementID(transaction.getAccountStatement() != null ? transaction.getAccountStatement().getStatementId() : null)
                        .createdBy(transaction.getCreatedBy())
                        .createdAt(transaction.getCreatedAt())
                        .updatedBy(transaction.getUpdatedBy())
                        .updatedAt(transaction.getUpdatedAt())
                        .status(transaction.isStatementIsInError() ? "Troubleshooting" : "Imported")
                        .remittanceInformation(transaction.getRemittanceInformation())
                        .orderingPartyName(transaction.getOrderingPartyName())
                        .build())
                .toList();
        return buildCsv(transactionCSVs);
    }

}
