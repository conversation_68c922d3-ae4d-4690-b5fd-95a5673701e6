package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.account.Contact;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ContactApiResponsePayload {

    private Long id;
    private String contactType;
    private String name;
    private String email;
    private String mobilePhoneNumber;
    private Long accountID;
    private String createdBy;
    private String updatedBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;



    public ContactApiResponsePayload(Contact contact) {
        this.id = contact.getId();
        this.contactType = contact.getContactType();
        this.name = contact.getName();
        this.email = contact.getEmail();
        this.mobilePhoneNumber = contact.getMobilePhoneNumber();
        this.accountID = contact.getAccount().getId();
        this.createdAt = contact.getCreatedAt();
        this.updatedAt = contact.getUpdatedAt();
        this.createdBy = contact.getCreatedBy();
        this.updatedBy = contact.getUpdatedBy();

    }

    public Contact toEntity() {
        return Contact
                .builder()
                .id(id)
                .contactType(contactType)
                .name(name)
                .email(email)
                .mobilePhoneNumber(mobilePhoneNumber)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }


}
