package pt.jumia.services.brad.api.payloads.request.account;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.filter.account.AccountNetChangeFilters;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class AccountNetChangeRequestPayload {

    private List<Long> partitionKeys;
    private Boolean fromBeginning;
    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime startDate;
    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime endDate;

    public AccountNetChangeFilters toEntity() throws ParseException {

        return new AccountNetChangeFilters(Objects.isNull(fromBeginning) ? Boolean.FALSE : fromBeginning,
            startDate == null ? null : startDate.toLocalDate(),
            endDate == null ? null : endDate.toLocalDate());
    }
}
