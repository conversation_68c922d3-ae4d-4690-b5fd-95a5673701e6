package pt.jumia.services.brad.api.validations.validators;

import lombok.extern.slf4j.Slf4j;
import pt.jumia.services.brad.api.validations.annotations.ValidPayload;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.lang.reflect.InvocationTargetException;

@Slf4j
public class GenericPayloadValidator implements ConstraintValidator<ValidPayload, Object> {
    private PayloadValidator<Object> validator;

    @Override
    public void initialize(ValidPayload validPayload) {
        Class<? extends PayloadValidator<?>> validatorClass = validPayload.payloadValidator();
        try {
            this.validator = (PayloadValidator<Object>) validatorClass.getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            throw new RuntimeException("Failed to instantiate validator: " + validatorClass.getName(), e);
        }
    }

    @Override
    public boolean isValid(Object payload, ConstraintValidatorContext context) {
        if (payload == null) {
            log.error("Payload is null");
            return false;
        }
        return validator.isValid(payload, context);
    }
}
