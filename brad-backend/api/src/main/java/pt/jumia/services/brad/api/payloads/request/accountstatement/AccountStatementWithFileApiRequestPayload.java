package pt.jumia.services.brad.api.payloads.request.accountstatement;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.AccountStatement;

import java.text.ParseException;

@Data
@NoArgsConstructor
public class AccountStatementWithFileApiRequestPayload {

    @NotNull
    @JsonProperty("account_statement")
    private AccountStatementApiRequestPayload accountStatement;

    @NotEmpty
    @JsonProperty("transactions_csv")
    private String transactions;
    @JsonProperty("next_statement_id")
    private String nextStatementId;

    public AccountStatementWithFileApiRequestPayload(final AccountStatement accountStatement, String transactions) {
        this.accountStatement = new AccountStatementApiRequestPayload();
        this.accountStatement.currencyCode = accountStatement.getCurrency().getCode();
        this.accountStatement.statementId = accountStatement.getStatementId();
        this.accountStatement.initialDate = accountStatement.getInitialDate().toString();
        this.accountStatement.finalDate = accountStatement.getFinalDate().toString();
        this.accountStatement.initialDirection = accountStatement.getInitialDirection().getValue();
        this.accountStatement.finalDirection = accountStatement.getFinalDirection().getValue();

        this.accountStatement.initialAmount = accountStatement.getInitialAmount();
        this.accountStatement.finalAmount = accountStatement.getFinalAmount();
        this.accountStatement.accountID = accountStatement.getAccount().getAccountNumber();
        this.transactions = transactions;

    }

    public AccountStatementWithFileApiRequestPayload(final AccountStatement accountStatement, String transactions, String nextStatementId) {
        this(accountStatement, transactions);
        this.nextStatementId = nextStatementId;
    }

    public AccountStatement toAccountStatementEntity() throws ParseException {
        return this.accountStatement.toEntity();
    }
}
