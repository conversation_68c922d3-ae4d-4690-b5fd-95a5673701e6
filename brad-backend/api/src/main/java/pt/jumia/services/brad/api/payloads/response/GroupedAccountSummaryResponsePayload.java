package pt.jumia.services.brad.api.payloads.response;

import java.math.BigDecimal;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.dtos.GroupedAccountDailySummaryDto;


@Data
@Value
@AllArgsConstructor
public class GroupedAccountSummaryResponsePayload {

    private String groupLabel;
    private BigDecimal initialBalanceUsd;
    private BigDecimal finalBalanceUsd;
    private BigDecimal initialBalanceLcy;
    private BigDecimal finalBalanceLcy;
    private BigDecimal creditAmountLcy;
    private BigDecimal debitAmountLcy;
    private BigDecimal creditAmountUsd;
    private BigDecimal debitAmountUsd;
    private AccountApiResponsePayload account;
    private CountryApiResponsePayload country;

    public GroupedAccountSummaryResponsePayload(GroupedAccountDailySummaryDto dto) {

        this.groupLabel = dto.getGroupLabel();
        this.initialBalanceUsd = dto.getInitialBalanceUsd();
        this.finalBalanceUsd = dto.getFinalBalanceUsd();
        this.initialBalanceLcy = dto.getInitialBalanceLcy();
        this.finalBalanceLcy = dto.getFinalBalanceLcy();
        this.creditAmountLcy = dto.getCreditAmountLcy();
        this.debitAmountLcy = dto.getDebitAmountLcy();
        this.creditAmountUsd = dto.getCreditAmountUsd();
        this.debitAmountUsd = dto.getDebitAmountUsd();
        this.account = Objects.isNull(dto.getAccount()) ? null : new AccountApiResponsePayload(dto.getAccount());
        this.country = Objects.isNull(dto.getCountry()) ? null : new CountryApiResponsePayload(dto.getCountry());

    }

}
