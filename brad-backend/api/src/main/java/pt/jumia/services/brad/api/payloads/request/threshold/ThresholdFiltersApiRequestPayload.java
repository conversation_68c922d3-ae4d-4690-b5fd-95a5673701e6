package pt.jumia.services.brad.api.payloads.request.threshold;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.filter.threshold.ThresholdFilters;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ThresholdFiltersApiRequestPayload {

    private String countryCode;
    private String currencyCode;
    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;

    private List<String> selectedFields;

    public ThresholdFilters toEntity() {
        return ThresholdFilters
            .builder()
            .countryCode(countryCode)
            .currencyCode(currencyCode)
            .createdAt(createdAt)
            .selectedFields(Objects.isNull(selectedFields) ? List.of() : BaseSelectFields.fromSelectCodes(
                            Transaction.SelectFields.class, selectedFields)
                    .stream().map(Transaction.SelectFields::getQueryField).toList()
            )
            .build();
    }

}
