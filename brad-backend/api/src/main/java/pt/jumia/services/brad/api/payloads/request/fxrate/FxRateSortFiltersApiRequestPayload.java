package pt.jumia.services.brad.api.payloads.request.fxrate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.filter.fxrate.FxRateSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

import java.util.Objects;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FxRateSortFiltersApiRequestPayload {

    private String orderField;

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public FxRateSortFilters toEntity() {
        return FxRateSortFilters.builder()
                .field(Objects.isNull(orderField) ? null : FxRate.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }
}
