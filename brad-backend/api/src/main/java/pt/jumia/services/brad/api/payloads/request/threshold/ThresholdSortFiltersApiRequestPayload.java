package pt.jumia.services.brad.api.payloads.request.threshold;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.filter.threshold.ThresholdSortFilters;
import pt.jumia.services.brad.domain.entities.reconciliation.Threshold;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ThresholdSortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = Threshold.SortingFields.class)
    private String orderField = Threshold.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public ThresholdSortFilters toEntity() {
        return ThresholdSortFilters.builder()
                .field(Threshold.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }
}
