package pt.jumia.services.brad.api.payloads.request.exportlog;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.ExportLog;
import pt.jumia.services.brad.domain.entities.filter.exportlog.ExportLogFilters;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class ExportLogFiltersRequestPayload {

    private String filterText;

    private List<ExportLog.Status> status;

    private List<ExportLog.Type> types;

    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAtFrom;

    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAtTo;

    private List<String> countryCodes;

    public ExportLogFilters toEntity() {
        return ExportLogFilters.builder()
                .filterText(StringUtils.trim(filterText))
                .types(types)
                .status(status)
                .createdAtFrom(createdAtFrom)
                .createdAtTo(createdAtTo)
                .countryCodes(countryCodes)
                .build();
    }
}
