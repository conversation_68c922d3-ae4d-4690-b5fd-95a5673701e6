package pt.jumia.services.brad.api.payloads.request.setting;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.Setting;
import pt.jumia.services.brad.domain.entities.filter.setting.SettingSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;


@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SettingSortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = Setting.SortingFields.class)
    private String orderField = Setting.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public SettingSortFilters toEntity() {

        return SettingSortFilters.builder()
            .field(Setting.SortingFields.valueOf(orderField))
            .direction(OrderDirection.valueOf(orderDirection))
            .build();
    }

}
