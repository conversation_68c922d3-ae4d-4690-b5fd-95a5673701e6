package pt.jumia.services.brad.api.payloads.request.bale;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

import java.util.Objects;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaleSortFiltersApiRequestPayload {

    private String orderField;

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public BaleSortFilters toEntity() {
        return BaleSortFilters.builder()
                .field(Objects.isNull(orderField) ? null : Bale.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }
}
