package pt.jumia.services.brad.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

@Data
@NoArgsConstructor
public class BaleApiResponsePayload {

    @Getter(value = AccessLevel.NONE)
    @Setter(value = AccessLevel.NONE)
    private static final String dateFormat = "yyyy-MM-dd";
    private static final String dateTimeFormat = "yyyy-MM-dd HH:mm:ss";

    Long id;
    String idCompany;
    AccountApiResponsePayload account;
    Integer entryNo;
    String documentNo;
    String documentType;
    @JsonFormat(pattern = dateFormat)
    LocalDate postingDate;
    String accountPostingGroup;
    String description;
    String sourceCode;
    String reasonCode;
    String busLine;
    String department;
    Direction direction;
    BigDecimal amount;
    BigDecimal amountLocalCurrency;
    BigDecimal amountUsd;
    BigDecimal remainingAmount;
    CurrencyApiResponsePayload transactionCurrency;
    BigDecimal amountLcy;
    String balanceAccountNumber;
    String balanceAccountType;
    Boolean isOpen;
    Boolean isReversed;
    String postedBy;
    String externalDocumentNo;
    String baleTimestamp;
    String accountTimestamp;
    String reconcileStatus;

    //reconciliation related fields
    private Integer reconciliationId;
    private String reconciliationCreator;
    @JsonFormat(pattern = dateTimeFormat)
    private LocalDateTime reconciliationCreationDate;
    private String reconciliationReviewer;
    @JsonFormat(pattern = dateTimeFormat)
    private LocalDateTime reconciliationReviewDate;
    private String reconciliationStatus;

    public BaleApiResponsePayload(Bale bale) {
        this.id = bale.getId();
        this.idCompany = bale.getIdCompany();
        this.account = new AccountApiResponsePayload(bale.getAccount());
        this.entryNo = bale.getEntryNo();
        this.documentNo = bale.getDocumentNo();
        this.documentType = bale.getDocumentType();
        this.postingDate = bale.getPostingDate();
        this.accountPostingGroup = bale.getAccountPostingGroup();
        this.description = bale.getDescription();
        this.sourceCode = bale.getSourceCode();
        this.reasonCode = bale.getReasonCode();
        this.busLine = bale.getBusLine();
        this.department = bale.getDepartment();
        this.direction = bale.getDirection();
        this.amount = bale.getAmount();
        this.amountLocalCurrency = bale.getAmountLocalCurrency();
        this.amountUsd = bale.getAmountUsd();
        this.remainingAmount = bale.getRemainingAmount();
        this.transactionCurrency = new CurrencyApiResponsePayload(bale.getTransactionCurrency());
        this.amountLcy = bale.getAmountLcy();
        this.balanceAccountNumber = bale.getBalanceAccountNumber();
        this.balanceAccountType = bale.getBalanceAccountType();
        this.isOpen = bale.getIsOpen();
        this.isReversed = bale.getIsReversed();
        this.postedBy = bale.getPostedBy();
        this.externalDocumentNo = bale.getExternalDocumentNo();
        this.baleTimestamp = bale.getBaleTimestamp();
        this.accountTimestamp = bale.getAccountTimestamp();
        this.reconcileStatus = bale.getReconcileStatus().name();
        if (Objects.nonNull(bale.getReconciliation())) {
            this.reconciliationId = bale.getReconciliation().getId();
            this.reconciliationCreator = bale.getReconciliation().getCreator();
            this.reconciliationCreationDate = bale.getReconciliation().getCreationDate();
            this.reconciliationReviewer = bale.getReconciliation().getReviewer();
            this.reconciliationReviewDate = bale.getReconciliation().getReviewDate();
            this.reconciliationStatus = bale.getReconciliation().getStatus().name();
        }
    }

    public Bale toEntity() {
        return Bale.builder()
                .idCompany(idCompany)
                .account(account.toEntity())
                .entryNo(entryNo)
                .documentNo(documentNo)
                .documentType(documentType)
                .postingDate(postingDate)
                .accountPostingGroup(accountPostingGroup)
                .description(description)
                .sourceCode(sourceCode)
                .reasonCode(reasonCode)
                .busLine(busLine)
                .department(department)
                .direction(direction)
                .amount(amount)
                .remainingAmount(remainingAmount)
                .transactionCurrency(transactionCurrency.toEntity())
                .amountLcy(amountLcy)
                .balanceAccountNumber(balanceAccountNumber)
                .balanceAccountType(balanceAccountType)
                .isOpen(isOpen)
                .isReversed(isReversed)
                .postedBy(postedBy)
                .externalDocumentNo(externalDocumentNo)
                .baleTimestamp(baleTimestamp)
                .accountTimestamp(accountTimestamp)
                .reconcileStatus(ReconcileStatus.valueOf(reconcileStatus))
                .build();
    }
}
