package pt.jumia.services.brad.api.payloads.request.transaction;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

import java.util.Objects;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TransactionSortFiltersApiRequestPayload {

    private String orderField;

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public TransactionSortFilters toEntity() {
        return TransactionSortFilters.builder()
                .field(Objects.isNull(orderField) ? null : Transaction.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }

}
