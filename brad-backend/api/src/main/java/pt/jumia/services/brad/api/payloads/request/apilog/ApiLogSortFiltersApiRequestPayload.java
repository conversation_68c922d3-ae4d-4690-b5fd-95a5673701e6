package pt.jumia.services.brad.api.payloads.request.apilog;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.ApiLog;
import pt.jumia.services.brad.domain.entities.filter.apilog.ApiLogSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApiLogSortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = ApiLog.SortingFields.class)
    private String orderField = ApiLog.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public ApiLogSortFilters toEntity() {
        return ApiLogSortFilters.builder()
                .field(ApiLog.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }
}
