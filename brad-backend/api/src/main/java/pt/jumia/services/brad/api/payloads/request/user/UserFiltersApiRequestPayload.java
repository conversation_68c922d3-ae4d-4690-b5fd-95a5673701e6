package pt.jumia.services.brad.api.payloads.request.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.entities.account.User;
import pt.jumia.services.brad.domain.entities.account.User.Status;
import pt.jumia.services.brad.domain.entities.filter.user.UserFilters;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class UserFiltersApiRequestPayload {

    private String userName;

    private String email;

    private Long accountID;

    private List<Long> accountIds;

    private String status;

    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;

    private List<String> selectedFields;


    public UserFilters toEntity() {
        return UserFilters
                .builder()
                .userName(userName)
                .email(email)
                .status(status == null ? null : Status.valueOf(status))
                .accountID(accountID)
                .accountIds(accountIds)
                .createdAt(createdAt)
                .selectedFields(Objects.isNull(selectedFields) ? List.of() : BaseSelectFields.fromSelectCodes(
                                User.SelectFields.class, selectedFields)
                        .stream().map(User.SelectFields::getQueryField).toList()
                )
                .build();

    }

}
