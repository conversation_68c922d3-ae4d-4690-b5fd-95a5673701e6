package pt.jumia.services.brad.api.payloads.request.bale;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleFilters;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class BaleFilterListApiRequestPayload {

    List<BaleFiltersApiRequestPayload> filterList;

    public List<BaleFilters> toEntities() throws ParseException {
        List<BaleFilters> baleFilters = new ArrayList<>();
        for (BaleFiltersApiRequestPayload filter : filterList) {
            baleFilters.add(filter.toEntity());
        }
        return baleFilters;
    }
}
