apply from: '../config/quality/quality.gradle'

dependencies {
    implementation project(':domain')

    // commons
    implementation("javax.validation:validation-api:2.0.1.Final")

    // TODO: this was added to remove the dependency from data module, but should be removed
    implementation("org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    api("org.springframework.boot:spring-boot-starter-web:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    implementation ("org.springframework.boot:spring-boot-starter-validation:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    implementation 'org.apache.commons:commons-csv:1.9.0'

    // Decode auth token
    implementation "com.nimbusds:nimbus-jose-jwt:2.10.1"

    // Decoding b2b token
    implementation group: 'com.sun.xml.security', name: 'xml-security-impl', version: '1.0'

    //swagger, for api documentation
    implementation group: 'org.springdoc', name: 'springdoc-openapi-starter-webmvc-ui', version: '2.0.2'

    testImplementation ("org.springframework.boot:spring-boot-starter-test:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    // https://mvnrepository.com/artifact/org.quartz-scheduler/quartz
    implementation 'org.quartz-scheduler:quartz:2.3.0'
}
