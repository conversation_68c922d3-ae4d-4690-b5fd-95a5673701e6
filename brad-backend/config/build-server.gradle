apply plugin: 'maven'

repositories {
    maven {
        credentials {
            username "${nexusUsername}"
            password "${nexusPassword}"
        }
        url "${nexusUrl}${nexusPath}"
    }
}

uploadArchives {
    repositories {
        mavenDeployer {
            repository(
                    url: "${nexusUrl}${nexusUploadReleasePath}") {
                authentication(userName: nexusUsername, password: nexusPassword)
            }
            snapshotRepository(
                    url: "${nexusUrl}${nexusUploadeSnapshotPath}") {
                authentication(userName: nexusUsername, password: nexusPassword)
            }
            pom.version = "${version}"
            pom.artifactId = "brad"
            pom.groupId = "com.jumia.services.brad"
        }
    }
}