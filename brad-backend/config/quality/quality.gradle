apply plugin: 'jaco<PERSON>'
apply plugin: 'com.github.spotbugs'
apply plugin: 'pmd'
apply plugin: 'checkstyle'
apply plugin: 'de.aaschmid.cpd'

// spotbugs and cpd do not allow both xml and html/txt reports, so toggle this for readable reports
def readableReports = false

jacoco {
    toolVersion = "0.8.8"
}

jacocoTestReport {

    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: [
                    '**/*Test*.*',
                    '**/Q*.class'
            ])
        }))
    }
}

spotbugs {
    if (project.hasProperty("ignoreQualityFailures")) {
        ignoreFailures = project.ignoreQualityFailures
    }
    excludeFilter = new File("${project.rootDir}/config/quality/spotbugs-filter.xml")
    spotbugsTest.enabled = false
}

spotbugsMain {
    reports {
        xml.enabled !readableReports
        html.enabled readableReports
    }
}

pmd {
    if (project.hasProperty("ignoreQualityFailures")) {
        ignoreFailures = project.ignoreQualityFailures
    }
    consoleOutput = true
    toolVersion = '6.54.0'
    ruleSetFiles = files("${project.rootDir}/config/quality/pmd-ruleset.xml")
    ruleSets = []
    pmdTest.enabled = false
}

checkstyle {
    if (project.hasProperty("ignoreQualityFailures")) {
        ignoreFailures = project.ignoreQualityFailures
    }
    configFile = file("${project.rootDir}/config/quality/checkstyle.xml")
    checkstyleTest.enabled = false
    maxErrors = 0
    maxWarnings = 0
}

cpd{
    ignoreFailures = true
}

cpdCheck{
    reports {
        text.enabled = readableReports
        xml.enabled = !readableReports
    }
    exclude '**/*Test.java'
    exclude '**/*Robot.java'
    exclude '**/*Payload.java'
}
