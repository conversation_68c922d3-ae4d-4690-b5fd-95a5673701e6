<?xml version="1.0"?>

<ruleset name="Custom Rules"
         xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 https://pmd.sourceforge.io/ruleset_2_0_0.xsd">

    <description>
        brad <PERSON> custom rule set
    </description>

    <rule ref="category/java/errorprone.xml">
        <!-- We don't use java serialization -->
        <exclude name="NonSerializableClass"/>
        <!-- No one needs to avoid null assignment -->
        <exclude name="NullAssignment"/>
    </rule>

</ruleset>
